{"version": 3, "file": "security.js", "sourceRoot": "", "sources": ["../../src/middleware/security.ts"], "names": [], "mappings": ";;;;;;AAAA,oDAA4B;AAC5B,4EAA2C;AAC3C,0EAAyC;AACzC,8CAAsB;AACtB,oFAAmD;AACnD,0DAA4B;AAC5B,gDAAwB;AAExB,oDAA4B;AAC5B,6DAAqC;AAiBrC,MAAM,WAAW,GAAqB;IACpC,MAAM,EAAE,UAAU,MAA0B,EAAE,QAAsB;QAElE,MAAM,cAAc,GAAG;YACrB,uBAAuB;YACvB,uBAAuB;YACvB,yBAAyB;YACzB,6BAA6B;SAC9B,CAAC;QAGF,IAAI,CAAC,MAAM;YAAE,OAAO,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAEzC,IAAI,cAAc,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YAC1C,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACvB,CAAC;aAAM,CAAC;YACN,gBAAM,CAAC,WAAW,CAAC,gBAAgB,EAAE;gBACnC,MAAM;gBACN,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC,CAAC;YACH,QAAQ,CAAC,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IACD,WAAW,EAAE,IAAI;IACjB,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;IAC7D,cAAc,EAAE;QACd,QAAQ;QACR,kBAAkB;QAClB,cAAc;QACd,QAAQ;QACR,eAAe;QACf,WAAW;QACX,cAAc;KACf;IACD,cAAc,EAAE,CAAC,eAAe,EAAE,cAAc,CAAC;IACjD,MAAM,EAAE,KAAK;CACd,CAAC;AAoRA,kCAAW;AA/Qb,MAAM,YAAY,GAAG;IACnB,qBAAqB,EAAE;QACrB,UAAU,EAAE;YACV,UAAU,EAAE,CAAC,QAAQ,CAAC;YACtB,QAAQ,EAAE,CAAC,QAAQ,EAAE,iBAAiB,EAAE,8BAA8B,CAAC;YACvE,OAAO,EAAE,CAAC,QAAQ,EAAE,2BAA2B,CAAC;YAChD,MAAM,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC;YACrC,SAAS,EAAE,CAAC,QAAQ,CAAC;YACrB,UAAU,EAAE,CAAC,QAAQ,EAAE,wBAAwB,CAAC;YAChD,QAAQ,EAAE,CAAC,QAAQ,CAAC;YACpB,SAAS,EAAE,CAAC,QAAQ,CAAC;YACrB,uBAAuB,EAAE,EAAE;SAC5B;KACF;IACD,yBAAyB,EAAE,KAAK;IAChC,IAAI,EAAE;QACJ,MAAM,EAAE,QAAQ;QAChB,iBAAiB,EAAE,IAAI;QACvB,OAAO,EAAE,IAAI;KACd;IACD,OAAO,EAAE,IAAI;IACb,UAAU,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE;IAC9B,SAAS,EAAE,IAAI;IACf,cAAc,EAAE,EAAE,MAAM,EAAE,iCAAiC,EAAE;CAC9D,CAAC;AAwPA,oCAAY;AAnPd,MAAM,iBAAiB,GAAG,CACxB,QAAgB,EAChB,GAAW,EACX,OAAe,EACf,yBAAkC,KAAK,EACvC,EAAE;IACF,OAAO,IAAA,4BAAS,EAAC;QACf,QAAQ;QACR,GAAG;QACH,OAAO,EAAE;YACP,OAAO,EAAE,KAAK;YACd,KAAK,EAAE;gBACL,OAAO;gBACP,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;aACvC;SACF;QACD,eAAe,EAAE,IAAI;QACrB,aAAa,EAAE,KAAK;QACpB,sBAAsB;QACtB,OAAO,EAAE,CAAC,GAAyB,EAAE,GAAa,EAAE,EAAE;YACpD,gBAAM,CAAC,WAAW,CAAC,qBAAqB,EAAE;gBACxC,EAAE,EAAE,GAAG,CAAC,EAAE;gBACV,GAAG,EAAE,GAAG,CAAC,WAAW;gBACpB,MAAM,EAAE,GAAG,CAAC,MAAM;gBAClB,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;gBAChC,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE;aACrB,CAAC,CAAC;YAEH,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACL,OAAO;oBACP,UAAU,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;iBACvC;aACF,CAAC,CAAC;QACL,CAAC;KACF,CAAC,CAAC;AACL,CAAC,CAAC;AA+MA,8CAAiB;AA1MnB,MAAM,cAAc,GAAG,CAAC,QAAgB,EAAE,UAAkB,EAAE,OAAe,EAAE,EAAE;IAC/E,OAAO,IAAA,2BAAQ,EAAC;QACd,QAAQ;QACR,UAAU;QACV,OAAO;QACP,UAAU,EAAE,OAAO,GAAG,EAAE;KACzB,CAAC,CAAC;AACL,CAAC,CAAC;AAoMA,wCAAc;AA/LhB,MAAM,eAAe,GAAG,CAAC,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IAC7F,MAAM,kBAAkB,GAAG;QAEzB,iCAAiC;QAEjC,yFAAyF;QAEzF,uBAAuB;QAEvB,wCAAwC;KACzC,CAAC;IAEF,MAAM,eAAe,GAAG,CAAC,IAAS,EAAE,MAAc,EAAW,EAAE;QAC7D,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC7B,KAAK,MAAM,OAAO,IAAI,kBAAkB,EAAE,CAAC;gBACzC,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;oBACvB,gBAAM,CAAC,WAAW,CAAC,2BAA2B,EAAE;wBAC9C,EAAE,EAAE,GAAG,CAAC,EAAE;wBACV,GAAG,EAAE,GAAG,CAAC,WAAW;wBACpB,MAAM,EAAE,GAAG,CAAC,MAAM;wBAClB,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;wBAChC,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE;wBACpB,MAAM;wBACN,OAAO,EAAE,OAAO,CAAC,QAAQ,EAAE;wBAC3B,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;qBAC7B,CAAC,CAAC;oBACH,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC;aAAM,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;YACrD,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;gBACvB,IAAI,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,MAAM,IAAI,GAAG,EAAE,CAAC,EAAE,CAAC;oBACnD,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC,CAAC;IAGF,eAAe,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAGpC,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IAGlC,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;IAEtC,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AA+IA,0CAAe;AA1IjB,MAAM,cAAc,GAAG,CAAC,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IAE5F,MAAM,SAAS,GAAG,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IACxC,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,EAAE,IAAI,SAAS,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;QAClE,gBAAM,CAAC,WAAW,CAAC,uBAAuB,EAAE;YAC1C,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,GAAG,EAAE,GAAG,CAAC,WAAW;YACpB,SAAS,EAAE,SAAS,IAAI,SAAS;YACjC,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE;SACrB,CAAC,CAAC;IACL,CAAC;IAGD,MAAM,iBAAiB,GAAG,CAAC,kBAAkB,EAAE,gBAAgB,EAAE,eAAe,CAAC,CAAC;IAClF,KAAK,MAAM,MAAM,IAAI,iBAAiB,EAAE,CAAC;QACvC,IAAI,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YACpB,gBAAM,CAAC,WAAW,CAAC,4BAA4B,EAAE;gBAC/C,EAAE,EAAE,GAAG,CAAC,EAAE;gBACV,GAAG,EAAE,GAAG,CAAC,WAAW;gBACpB,MAAM;gBACN,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC;gBACtB,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE;aACrB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAGD,GAAG,CAAC,GAAG,CAAC;QACN,cAAc,EAAE,GAAG,CAAC,EAAE,IAAI,gBAAM,CAAC,UAAU,EAAE;QAC7C,iBAAiB,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;QACxC,wBAAwB,EAAE,SAAS;QACnC,oBAAoB,EAAE,QAAQ;QAC9B,mCAAmC,EAAE,MAAM;KAC5C,CAAC,CAAC;IAEH,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAuGA,wCAAc;AAlGhB,MAAM,gBAAgB,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IACjF,GAAG,CAAC,SAAS,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAC;IACzC,GAAG,CAAC,SAAS,CAAC,yBAAyB,EAAE,wBAAwB,CAAC,CAAC;IACnE,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AA+FA,4CAAgB;AA1FlB,MAAM,YAAY,GAAG,CAAC,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IAC1F,MAAM,SAAS,GAAG,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;IAC9C,MAAM,WAAW,GAAG;QAClB,MAAM;QACN,UAAU;QACV,SAAS;QACT,UAAU;QACV,OAAO;QACP,OAAO;QACP,kBAAkB;QAClB,UAAU;KACX,CAAC;IAEF,MAAM,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;IAEnE,IAAI,KAAK,EAAE,CAAC;QACV,gBAAM,CAAC,WAAW,CAAC,cAAc,EAAE;YACjC,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,GAAG,EAAE,GAAG,CAAC,WAAW;YACpB,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,SAAS;YACT,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE;SACrB,CAAC,CAAC;QAGH,MAAM,gBAAgB,GAAG,CAAC,SAAS,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC;QACjE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YAChE,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACL,OAAO,EAAE,uDAAuD;iBACjE;aACF,CAAC,CAAC;YACH,OAAO;QACT,CAAC;IACH,CAAC;IAED,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAqDA,oCAAY;AAhDd,MAAM,aAAa,GAAG,CAAC,GAAgB,EAAQ,EAAE;IAE/C,GAAG,CAAC,GAAG,CAAC,IAAA,cAAI,EAAC,WAAW,CAAC,CAAC,CAAC;IAG3B,GAAG,CAAC,GAAG,CAAC,IAAA,gBAAM,EAAC,YAAmB,CAAC,CAAC,CAAC;IAGrC,GAAG,CAAC,GAAG,CAAC,IAAA,aAAG,EAAC;QACV,SAAS,EAAE,CAAC,MAAM,EAAE,YAAY,CAAC;KAClC,CAAC,CAAC,CAAC;IAGJ,GAAG,CAAC,GAAG,CAAC,IAAA,gCAAa,GAAE,CAAC,CAAC;IAGzB,GAAG,CAAC,GAAG,CAAC,IAAA,mBAAG,GAAE,CAAC,CAAC;IAGf,GAAG,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;IACxB,GAAG,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC;IACzB,GAAG,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IAC1B,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IAGtB,GAAG,CAAC,GAAG,CAAC,iBAAiB,CACvB,EAAE,GAAG,EAAE,GAAG,IAAI,EACd,IAAI,EACJ,wEAAwE,CACzE,CAAC,CAAC;IAGH,GAAG,CAAC,GAAG,CAAC,cAAc,CACpB,EAAE,GAAG,EAAE,GAAG,IAAI,EACd,GAAG,EACH,GAAG,CACJ,CAAC,CAAC;AACL,CAAC,CAAC;AAGA,sCAAa"}