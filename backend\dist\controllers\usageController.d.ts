import { Request, Response, NextFunction } from 'express';
interface AuthenticatedRequest extends Request {
    user?: any;
}
declare const getCurrentUsage: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
declare const getUsageStats: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
declare const checkActionPermission: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
declare const getUpgradeRecommendations: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export { getCurrentUsage, getUsageStats, checkActionPermission, getUpgradeRecommendations };
declare const _default: {
    getCurrentUsage: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    getUsageStats: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    checkActionPermission: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
    getUpgradeRecommendations: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
};
export default _default;
//# sourceMappingURL=usageController.d.ts.map