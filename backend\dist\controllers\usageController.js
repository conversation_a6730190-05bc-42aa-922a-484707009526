"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUpgradeRecommendations = exports.checkActionPermission = exports.getUsageStats = exports.getCurrentUsage = void 0;
const subscriptionLimits_1 = require("../middleware/subscriptionLimits");
const errorHandler_1 = require("../utils/errorHandler");
const prisma_1 = require("../config/prisma");
const getCurrentUsage = async (req, res, next) => {
    try {
        const userId = req.user.id;
        const usageInfo = await (0, subscriptionLimits_1.getUserUsageInfo)(userId);
        res.json({
            success: true,
            data: usageInfo
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getCurrentUsage = getCurrentUsage;
const getUsageStats = async (req, res, next) => {
    try {
        const userId = req.user.id;
        const { period = 'month' } = req.query;
        const now = new Date();
        let startDate;
        switch (period) {
            case 'week':
                startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 7);
                break;
            case 'month':
                startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                break;
            case 'year':
                startDate = new Date(now.getFullYear(), 0, 1);
                break;
            default:
                startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        }
        const expenseStats = await prisma_1.prisma.expense.groupBy({
            by: ['date'],
            where: {
                user_id: userId,
                date: {
                    gte: startDate,
                    lte: now
                }
            },
            _count: {
                id: true
            },
            _sum: {
                amount: true
            },
            orderBy: {
                date: 'asc'
            }
        });
        const categoryStats = await prisma_1.prisma.expense.groupBy({
            by: ['category_id'],
            where: {
                user_id: userId,
                date: {
                    gte: startDate,
                    lte: now
                }
            },
            _count: {
                id: true
            },
            _sum: {
                amount: true
            }
        });
        const categories = await prisma_1.prisma.category.findMany({
            where: {
                id: {
                    in: categoryStats.map(stat => stat.category_id)
                }
            },
            select: {
                id: true,
                name: true,
                color: true,
                icon: true
            }
        });
        const categoryStatsWithInfo = categoryStats.map(stat => {
            const category = categories.find(cat => cat.id === stat.category_id);
            return {
                ...stat,
                category: category
            };
        });
        const usageInfo = await (0, subscriptionLimits_1.getUserUsageInfo)(userId);
        res.json({
            success: true,
            data: {
                period,
                startDate,
                endDate: now,
                usage: usageInfo,
                expenses: {
                    daily: expenseStats,
                    byCategory: categoryStatsWithInfo,
                    total: {
                        count: expenseStats.reduce((sum, stat) => sum + stat._count.id, 0),
                        amount: expenseStats.reduce((sum, stat) => sum + (stat._sum.amount || 0), 0)
                    }
                }
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getUsageStats = getUsageStats;
const checkActionPermission = async (req, res, next) => {
    try {
        const userId = req.user.id;
        const { action, data } = req.body;
        if (!action) {
            throw new errorHandler_1.AppError('Acțiunea este obligatorie', 400);
        }
        const usageInfo = await (0, subscriptionLimits_1.getUserUsageInfo)(userId);
        let canPerform = true;
        let reason = null;
        let upgradeRequired = false;
        switch (action) {
            case 'create_expense':
                if (usageInfo.expenses.limit > 0 && usageInfo.expenses.remaining <= 0) {
                    canPerform = false;
                    reason = `Ați atins limita de ${usageInfo.expenses.limit} cheltuieli pentru planul ${usageInfo.planType.toUpperCase()}`;
                    upgradeRequired = true;
                }
                break;
            case 'create_category':
                if (usageInfo.categories.limit > 0 && usageInfo.categories.remaining <= 0) {
                    canPerform = false;
                    reason = `Ați atins limita de ${usageInfo.categories.limit} categorii personalizate pentru planul ${usageInfo.planType.toUpperCase()}`;
                    upgradeRequired = true;
                }
                break;
            case 'export_data':
                const format = data?.format?.toLowerCase() || 'csv';
                if (!usageInfo.exportFormats.includes(format)) {
                    canPerform = false;
                    reason = `Formatul ${format.toUpperCase()} nu este disponibil pentru planul ${usageInfo.planType.toUpperCase()}`;
                    upgradeRequired = true;
                }
                break;
            case 'access_advanced_reports':
                if (!usageInfo.features.includes('advanced_reports') && !usageInfo.features.includes('all_features')) {
                    canPerform = false;
                    reason = `Rapoartele avansate nu sunt disponibile pentru planul ${usageInfo.planType.toUpperCase()}`;
                    upgradeRequired = true;
                }
                break;
            default:
                throw new errorHandler_1.AppError('Acțiune necunoscută', 400);
        }
        res.json({
            success: true,
            data: {
                action,
                canPerform,
                reason,
                upgradeRequired,
                currentPlan: usageInfo.planType,
                usage: usageInfo
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.checkActionPermission = checkActionPermission;
const getUpgradeRecommendations = async (req, res, next) => {
    try {
        const userId = req.user.id;
        const usageInfo = await (0, subscriptionLimits_1.getUserUsageInfo)(userId);
        const recommendations = [];
        if (usageInfo.planType === 'free') {
            if (usageInfo.expenses.percentage >= 80) {
                recommendations.push({
                    type: 'expense_limit',
                    priority: 'high',
                    title: 'Vă apropiați de limita de cheltuieli',
                    description: `Ați folosit ${usageInfo.expenses.current} din ${usageInfo.expenses.limit} cheltuieli disponibile (${usageInfo.expenses.percentage}%)`,
                    suggestedPlan: 'basic',
                    benefits: ['500 cheltuieli/lună', 'Export PDF', 'Categorii nelimitate']
                });
            }
            if (usageInfo.categories.limit > 0 && usageInfo.categories.current >= usageInfo.categories.limit * 0.8) {
                recommendations.push({
                    type: 'category_limit',
                    priority: 'medium',
                    title: 'Vă apropiați de limita de categorii',
                    description: `Ați creat ${usageInfo.categories.current} din ${usageInfo.categories.limit} categorii personalizate disponibile`,
                    suggestedPlan: 'basic',
                    benefits: ['Categorii nelimitate', 'Export avansat']
                });
            }
            recommendations.push({
                type: 'features',
                priority: 'low',
                title: 'Deblocați funcționalități avansate',
                description: 'Accesați rapoarte avansate, export în multiple formate și multe altele',
                suggestedPlan: 'premium',
                benefits: ['Rapoarte avansate', 'Export Excel', 'Suport prioritar', 'API access']
            });
        }
        else if (usageInfo.planType === 'basic') {
            if (usageInfo.expenses.percentage >= 80) {
                recommendations.push({
                    type: 'expense_limit',
                    priority: 'high',
                    title: 'Vă apropiați de limita de cheltuieli',
                    description: `Ați folosit ${usageInfo.expenses.current} din ${usageInfo.expenses.limit} cheltuieli disponibile (${usageInfo.expenses.percentage}%)`,
                    suggestedPlan: 'premium',
                    benefits: ['Cheltuieli nelimitate', 'Rapoarte avansate', 'Suport prioritar']
                });
            }
            recommendations.push({
                type: 'premium_features',
                priority: 'medium',
                title: 'Accesați funcționalități premium',
                description: 'Obțineți rapoarte avansate, predicții și suport prioritar',
                suggestedPlan: 'premium',
                benefits: ['Rapoarte avansate', 'Predicții buget', 'Export Excel', 'API access']
            });
        }
        res.json({
            success: true,
            data: {
                currentPlan: usageInfo.planType,
                usage: usageInfo,
                recommendations: recommendations.sort((a, b) => {
                    const priorityOrder = { high: 3, medium: 2, low: 1 };
                    return priorityOrder[b.priority] - priorityOrder[a.priority];
                })
            }
        });
    }
    catch (error) {
        next(error);
    }
};
exports.getUpgradeRecommendations = getUpgradeRecommendations;
exports.default = {
    getCurrentUsage,
    getUsageStats,
    checkActionPermission,
    getUpgradeRecommendations
};
//# sourceMappingURL=usageController.js.map