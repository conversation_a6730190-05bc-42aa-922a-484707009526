"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.resendVerification = exports.verifyEmail = exports.resetPassword = exports.forgotPassword = exports.changePassword = exports.updateProfile = exports.getProfile = exports.logout = exports.refreshToken = exports.login = exports.register = void 0;
const crypto_1 = __importDefault(require("crypto"));
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const prisma_1 = require("../config/prisma");
const auth_1 = require("../middleware/auth");
const register = async (req, res) => {
    try {
        const { email, password, name, currency = 'USD' } = req.body;
        const existingUser = await prisma_1.prisma.user.findUnique({
            where: { email: email.toLowerCase().trim() },
        });
        if (existingUser) {
            res.status(409).json({
                success: false,
                message: 'User with this email already exists',
            });
            return;
        }
        const hashedPassword = await bcryptjs_1.default.hash(password, 12);
        const user = await prisma_1.prisma.user.create({
            data: {
                email: email.toLowerCase().trim(),
                password: hashedPassword,
                name: name.trim(),
                currency: currency.toUpperCase(),
            },
        });
        const defaultCategories = [
            {
                name: 'Alimentație',
                description: 'Cheltuieli pentru mâncare și băuturi',
                color: '#FF6B6B',
                icon: 'utensils',
                is_default: true,
                sort_order: 1,
                user_id: user.id,
            },
            {
                name: 'Transport',
                description: 'Cheltuieli pentru transport',
                color: '#4ECDC4',
                icon: 'car',
                sort_order: 2,
                user_id: user.id,
            },
            {
                name: 'Utilități',
                description: 'Facturi și utilități',
                color: '#45B7D1',
                icon: 'home',
                sort_order: 3,
                user_id: user.id,
            },
        ];
        await prisma_1.prisma.category.createMany({
            data: defaultCategories,
        });
        const accessToken = (0, auth_1.generateToken)(user.id);
        const refreshToken = (0, auth_1.generateRefreshToken)(user.id);
        await prisma_1.prisma.user.update({
            where: { id: user.id },
            data: { refresh_token: refreshToken },
        });
        const { password: _, ...userResponse } = user;
        res.status(201).json({
            success: true,
            message: 'User registered successfully',
            data: {
                user: userResponse,
                tokens: {
                    accessToken,
                    refreshToken,
                },
            },
        });
    }
    catch (error) {
        console.error('Registration error:', error);
        if (error.name === 'SequelizeValidationError') {
            const errors = error.errors.map((err) => ({
                field: err.path,
                message: err.message,
            }));
            res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors,
            });
            return;
        }
        res.status(500).json({
            success: false,
            message: 'Internal server error during registration',
        });
    }
};
exports.register = register;
const login = async (req, res) => {
    try {
        const { email, password } = req.body;
        const user = await prisma_1.prisma.user.findUnique({
            where: { email: email.toLowerCase().trim() },
        });
        if (!user) {
            res.status(401).json({
                success: false,
                message: 'Invalid email or password',
            });
            return;
        }
        if (!user.is_active) {
            res.status(401).json({
                success: false,
                message: 'Account is deactivated. Please contact support.',
            });
            return;
        }
        const isPasswordValid = await bcryptjs_1.default.compare(password, user.password);
        if (!isPasswordValid) {
            res.status(401).json({
                success: false,
                message: 'Invalid email or password',
            });
            return;
        }
        const accessToken = (0, auth_1.generateToken)(user.id);
        const refreshToken = (0, auth_1.generateRefreshToken)(user.id);
        await prisma_1.prisma.user.update({
            where: { id: user.id },
            data: {
                last_login: new Date(),
                login_count: { increment: 1 },
                refresh_token: refreshToken,
            },
        });
        const { password: _, password_reset_token, password_reset_expires, email_verification_token, ...userResponse } = user;
        const formattedUser = {
            ...userResponse,
            subscription: userResponse.plan_type ? {
                plan: {
                    name: userResponse.plan_type,
                },
                status: userResponse.subscription_status || 'free',
                currentPeriodEnd: userResponse.subscription_current_period_end,
            } : null,
        };
        res.json({
            success: true,
            message: 'Login successful',
            data: {
                user: formattedUser,
                tokens: {
                    accessToken,
                    refreshToken,
                },
            },
        });
    }
    catch (error) {
        console.error('Login error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error during login',
        });
    }
};
exports.login = login;
const refreshToken = async (req, res) => {
    try {
        const { refreshToken } = req.body;
        if (!refreshToken) {
            res.status(401).json({
                success: false,
                message: 'Refresh token is required',
            });
            return;
        }
        const decoded = (0, auth_1.verifyRefreshToken)(refreshToken);
        const user = await prisma_1.prisma.user.findUnique({
            where: { id: decoded.userId },
        });
        if (!user || user.refresh_token !== refreshToken) {
            res.status(401).json({
                success: false,
                message: 'Invalid refresh token',
            });
            return;
        }
        if (!user.is_active) {
            res.status(401).json({
                success: false,
                message: 'Account is deactivated',
            });
            return;
        }
        const newAccessToken = (0, auth_1.generateToken)(user.id);
        const newRefreshToken = (0, auth_1.generateRefreshToken)(user.id);
        await prisma_1.prisma.user.update({
            where: { id: user.id },
            data: { refresh_token: newRefreshToken },
        });
        res.json({
            success: true,
            message: 'Token refreshed successfully',
            data: {
                tokens: {
                    accessToken: newAccessToken,
                    refreshToken: newRefreshToken,
                },
            },
        });
    }
    catch (error) {
        console.error('Token refresh error:', error);
        if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
            res.status(401).json({
                success: false,
                message: 'Invalid or expired refresh token',
            });
            return;
        }
        res.status(500).json({
            success: false,
            message: 'Internal server error during token refresh',
        });
    }
};
exports.refreshToken = refreshToken;
const logout = async (req, res) => {
    try {
        const { user } = req;
        await prisma_1.prisma.user.update({
            where: { id: req.user.id },
            data: { refresh_token: null },
        });
        res.json({
            success: true,
            message: 'Logout successful',
        });
    }
    catch (error) {
        console.error('Logout error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error during logout',
        });
    }
};
exports.logout = logout;
const getProfile = async (req, res) => {
    try {
        const { user } = req;
        const { password: _, refresh_token, password_reset_token, password_reset_expires, email_verification_token, ...userResponse } = req.user;
        const formattedUser = {
            ...userResponse,
            subscription: userResponse.plan_type ? {
                plan: {
                    name: userResponse.plan_type,
                },
                status: userResponse.subscription_status || 'free',
                currentPeriodEnd: userResponse.subscription_current_period_end,
            } : null,
        };
        res.json({
            success: true,
            data: {
                user: formattedUser,
            },
        });
    }
    catch (error) {
        console.error('Get profile error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error while fetching profile',
        });
    }
};
exports.getProfile = getProfile;
const updateProfile = async (req, res) => {
    try {
        const { user } = req;
        const { name, currency, timezone } = req.body;
        const updateData = {};
        if (name !== undefined)
            updateData.name = name.trim();
        if (currency !== undefined)
            updateData.currency = currency.toUpperCase();
        if (timezone !== undefined)
            updateData.timezone = timezone;
        const updatedUser = await prisma_1.prisma.user.update({
            where: { id: req.user.id },
            data: updateData,
        });
        const { password: _, refresh_token, password_reset_token, password_reset_expires, email_verification_token, ...userResponse } = updatedUser;
        const formattedUser = {
            ...userResponse,
            subscription: userResponse.plan_type ? {
                plan: {
                    name: userResponse.plan_type,
                },
                status: userResponse.subscription_status || 'free',
                currentPeriodEnd: userResponse.subscription_current_period_end,
            } : null,
        };
        res.json({
            success: true,
            message: 'Profile updated successfully',
            data: {
                user: formattedUser,
            },
        });
    }
    catch (error) {
        console.error('Update profile error:', error);
        if (error.name === 'SequelizeValidationError') {
            const errors = error.errors.map((err) => ({
                field: err.path,
                message: err.message,
            }));
            res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors,
            });
            return;
        }
        res.status(500).json({
            success: false,
            message: 'Internal server error while updating profile',
        });
    }
};
exports.updateProfile = updateProfile;
const changePassword = async (req, res) => {
    try {
        const { user } = req;
        const { currentPassword, newPassword } = req.body;
        const isCurrentPasswordValid = await bcryptjs_1.default.compare(currentPassword, user.password);
        if (!isCurrentPasswordValid) {
            res.status(400).json({
                success: false,
                message: 'Current password is incorrect',
            });
            return;
        }
        const isSamePassword = await bcryptjs_1.default.compare(newPassword, user.password);
        if (isSamePassword) {
            res.status(400).json({
                success: false,
                message: 'New password must be different from current password',
            });
            return;
        }
        const hashedNewPassword = await bcryptjs_1.default.hash(newPassword, 12);
        await prisma_1.prisma.user.update({
            where: { id: user.id },
            data: {
                password: hashedNewPassword,
                refresh_token: null,
            },
        });
        res.json({
            success: true,
            message: 'Password changed successfully. Please login again.',
        });
    }
    catch (error) {
        console.error('Change password error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error while changing password',
        });
    }
};
exports.changePassword = changePassword;
const forgotPassword = async (req, res) => {
    try {
        const { email } = req.body;
        const user = await prisma_1.prisma.user.findUnique({
            where: { email: email.toLowerCase().trim() },
        });
        if (!user) {
            res.json({
                success: true,
                message: 'If an account with that email exists, a password reset link has been sent.',
            });
            return;
        }
        const resetToken = crypto_1.default.randomBytes(32).toString('hex');
        const resetTokenExpires = new Date(Date.now() + 3600000);
        await prisma_1.prisma.user.update({
            where: { id: user.id },
            data: {
                password_reset_token: resetToken,
                password_reset_expires: resetTokenExpires,
            },
        });
        console.log(`Password reset token for ${email}: ${resetToken}`);
        res.json({
            success: true,
            message: 'If an account with that email exists, a password reset link has been sent.',
            resetToken: process.env.NODE_ENV === 'development' ? resetToken : undefined,
        });
    }
    catch (error) {
        console.error('Forgot password error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error while processing password reset request',
        });
    }
};
exports.forgotPassword = forgotPassword;
const resetPassword = async (req, res) => {
    try {
        const { token, password } = req.body;
        const user = await prisma_1.prisma.user.findFirst({
            where: {
                password_reset_token: token,
                password_reset_expires: {
                    gt: new Date(),
                },
            },
        });
        if (!user) {
            res.status(400).json({
                success: false,
                message: 'Invalid or expired reset token',
            });
            return;
        }
        const hashedPassword = await bcryptjs_1.default.hash(password, 12);
        await prisma_1.prisma.user.update({
            where: { id: user.id },
            data: {
                password: hashedPassword,
                password_reset_token: null,
                password_reset_expires: null,
                refresh_token: null,
            },
        });
        res.json({
            success: true,
            message: 'Password reset successfully. Please login with your new password.',
        });
    }
    catch (error) {
        console.error('Reset password error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error while resetting password',
        });
    }
};
exports.resetPassword = resetPassword;
const verifyEmail = async (req, res) => {
    try {
        const { token } = req.params;
        const user = await prisma_1.prisma.user.findFirst({
            where: {
                email_verification_token: token || null,
            },
        });
        if (!user) {
            res.status(400).json({
                success: false,
                message: 'Invalid or expired verification token',
            });
            return;
        }
        await prisma_1.prisma.user.update({
            where: { id: user.id },
            data: {
                email_verified: true,
                email_verification_token: null,
            },
        });
        res.json({
            success: true,
            message: 'Email verified successfully',
        });
    }
    catch (error) {
        console.error('Email verification error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error during email verification',
        });
    }
};
exports.verifyEmail = verifyEmail;
const resendVerification = async (req, res) => {
    try {
        const { user } = req;
        if (user.email_verified) {
            res.status(400).json({
                success: false,
                message: 'Email is already verified',
            });
            return;
        }
        const verificationToken = crypto_1.default.randomBytes(32).toString('hex');
        await prisma_1.prisma.user.update({
            where: { id: user.id },
            data: { email_verification_token: verificationToken },
        });
        console.log(`Verification token for ${user.email}: ${verificationToken}`);
        res.json({
            success: true,
            message: 'Verification email sent successfully',
        });
    }
    catch (error) {
        console.error('Resend verification error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error while sending verification email',
        });
    }
};
exports.resendVerification = resendVerification;
exports.default = {
    register,
    login,
    refreshToken,
    logout,
    getProfile,
    updateProfile,
    changePassword,
    forgotPassword,
    resetPassword,
    verifyEmail,
    resendVerification,
};
//# sourceMappingURL=authController.js.map