{"version": 3, "file": "audit.js", "sourceRoot": "", "sources": ["../../src/middleware/audit.ts"], "names": [], "mappings": ";;;;;;AACA,+BAAoC;AACpC,6DAAqC;AAmFrC,MAAM,WAAW,GAAG,CAAC,GAAyB,EAAE,GAAkB,EAAE,IAAkB,EAAQ,EAAE;IAE9F,GAAG,CAAC,OAAO,GAAG,IAAA,SAAM,GAAE,CAAC;IAGvB,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAG3B,MAAM,YAAY,GAAG,GAAG,CAAC,IAAI,CAAC;IAC9B,MAAM,YAAY,GAAG,GAAG,CAAC,IAAI,CAAC;IAG9B,GAAG,CAAC,IAAI,GAAG,UAAS,IAAS;QAC3B,GAAG,CAAC,YAAY,GAAG,IAAI,CAAC;QACxB,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACvC,CAAC,CAAC;IAEF,GAAG,CAAC,IAAI,GAAG,UAAS,IAAS;QAC3B,GAAG,CAAC,YAAY,GAAG,IAAI,CAAC;QACxB,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACvC,CAAC,CAAC;IAGF,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;QACpB,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,SAAS,IAAI,CAAC,CAAC,CAAC;QAGnD,MAAM,MAAM,GAAG,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK,EAAE,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;QAGxE,MAAM,SAAS,GAAc;YAC3B,OAAO,EAAE,GAAG,CAAC,OAAO,IAAI,EAAE;YAC1B,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE,IAAI,IAAI;YAC5B,SAAS,EAAE,GAAG,CAAC,IAAI,EAAE,KAAK,IAAI,IAAI;YAClC,MAAM;YACN,QAAQ,EAAE,eAAe,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC;YACtD,UAAU,EAAE,GAAG,CAAC,MAAM,EAAE,EAAE,IAAI,IAAI;YAClC,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,GAAG,EAAE,GAAG,CAAC,WAAW;YACpB,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,UAAU,EAAE,GAAG,CAAC,UAAU;YAC1B,QAAQ;YACR,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;YAChC,OAAO,EAAE,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC;YAC3B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,OAAO,EAAE,GAAG,CAAC,UAAU,GAAG,GAAG;YAG7B,WAAW,EAAE,mBAAmB,CAAC,GAAG,CAAC;YAGrC,YAAY,EAAE,iBAAiB,CAAC,MAAM,EAAE,GAAG,CAAC,UAAU,CAAC;gBACrD,CAAC,CAAC,oBAAoB,CAAC,GAAG,CAAC,YAAY,CAAC;gBACxC,CAAC,CAAC,IAAI;YAGR,SAAS,EAAE,GAAG,CAAC,SAAS,IAAI,IAAI;YAGhC,QAAQ,EAAE;gBACR,WAAW,EAAE,GAAG,CAAC,GAAG,CAAC,cAAc,CAAC;gBACpC,aAAa,EAAE,GAAG,CAAC,GAAG,CAAC,gBAAgB,CAAC;gBACxC,cAAc,EAAE,GAAG,CAAC,GAAG,CAAC,iBAAiB,CAAC;gBAC1C,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC;aAC1B;SACF,CAAC;QAGF,IAAI,yBAAyB,CAAC,MAAM,CAAC,EAAE,CAAC;YACtC,gBAAM,CAAC,WAAW,CAAC,iBAAiB,EAAE,SAAS,CAAC,CAAC;QACnD,CAAC;aAAM,IAAI,wBAAwB,CAAC,MAAM,CAAC,EAAE,CAAC;YAC5C,gBAAM,CAAC,QAAQ,CAAC,iBAAiB,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE,IAAI,WAAW,EAAE,SAAS,CAAC,CAAC;QAC7E,CAAC;aAAM,CAAC;YACN,gBAAM,CAAC,UAAU,CAAC,GAAG,EAAE,GAAG,EAAE,QAAQ,CAAC,CAAC;QACxC,CAAC;QAGD,IAAI,GAAG,CAAC,UAAU,IAAI,GAAG,EAAE,CAAC;YAC1B,gBAAM,CAAC,WAAW,CAAC,gBAAgB,EAAE;gBACnC,GAAG,SAAS;gBACZ,SAAS,EAAE,eAAe,CAAC,GAAG,CAAC,UAAU,CAAC;gBAC1C,YAAY,EAAE,GAAG,CAAC,YAAY;aAC/B,CAAC,CAAC;QACL,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AA2RA,kCAAW;AAtRb,SAAS,eAAe,CAAC,MAAc,EAAE,IAAY;IACnD,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACrD,MAAM,QAAQ,GAAG,YAAY,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC;IAE9C,MAAM,SAAS,GAA0E;QACvF,KAAK,EAAE;YACL,OAAO,EAAE,MAAM;YACf,QAAQ,EAAE;gBACR,UAAU,EAAE,cAAc;gBAC1B,cAAc,EAAE,QAAQ;gBACxB,kBAAkB,EAAE,aAAa;gBACjC,UAAU,EAAE,iBAAiB;aAC9B;SACF;QACD,MAAM,EAAE;YACN,OAAO,EAAE,QAAQ;YACjB,QAAQ,EAAE;gBACR,gBAAgB,EAAE,UAAU;gBAC5B,aAAa,EAAE,OAAO;gBACtB,uBAAuB,EAAE,iBAAiB;gBAC1C,sBAAsB,EAAE,gBAAgB;gBACxC,oBAAoB,EAAE,cAAc;gBACpC,gBAAgB,EAAE,sBAAsB;gBACxC,kBAAkB,EAAE,wBAAwB;aAC7C;SACF;QACD,KAAK,EAAE;YACL,OAAO,EAAE,QAAQ;YACjB,QAAQ,EAAE;gBACR,uBAAuB,EAAE,iBAAiB;gBAC1C,gBAAgB,EAAE,gBAAgB;aACnC;SACF;QACD,OAAO,EAAE;YACP,OAAO,EAAE,gBAAgB;YACzB,QAAQ,EAAE,EAAE;SACb;QACD,QAAQ,EAAE;YACR,OAAO,EAAE,QAAQ;YACjB,QAAQ,EAAE;gBACR,gBAAgB,EAAE,qBAAqB;aACxC;SACF;KACF,CAAC;IAEF,MAAM,aAAa,GAAG,SAAS,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,EAAE,EAAE,CAAC;IAGhF,KAAK,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE,CAAC;QACvE,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YAC3B,OAAO,MAAM,CAAC;QAChB,CAAC;IACH,CAAC;IAGD,OAAO,GAAG,aAAa,CAAC,OAAO,IAAI,QAAQ,EAAE,CAAC;AAChD,CAAC;AAKD,SAAS,eAAe,CAAC,IAAY;IACnC,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACrD,OAAO,YAAY,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC;AACtC,CAAC;AAKD,SAAS,mBAAmB,CAAC,GAAyB;IACpD,MAAM,eAAe,GAAG;QACtB,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,OAAO;QACrD,QAAQ,EAAE,QAAQ,EAAE,eAAe,EAAE,QAAQ;KAC9C,CAAC;IAEF,MAAM,SAAS,GAAG;QAChB,KAAK,EAAE,EAAE,GAAG,GAAG,CAAC,KAAK,EAAE;QACvB,MAAM,EAAE,EAAE,GAAG,GAAG,CAAC,MAAM,EAAE;QACzB,IAAI,EAAE,EAAE,GAAG,GAAG,CAAC,IAAI,EAAE;KACtB,CAAC;IAGF,SAAS,mBAAmB,CAAC,GAAQ;QACnC,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,KAAK,IAAI;YAAE,OAAO,GAAG,CAAC;QAExD,MAAM,OAAO,GAAQ,EAAE,CAAC;QACxB,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;YAC/C,IAAI,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC;gBACnF,OAAO,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC;YAC9B,CAAC;iBAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;gBACrC,OAAO,CAAC,GAAG,CAAC,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC;YAC5C,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;YACvB,CAAC;QACH,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,OAAO,mBAAmB,CAAC,SAAS,CAAC,CAAC;AACxC,CAAC;AAKD,SAAS,oBAAoB,CAAC,IAAS;IACrC,IAAI,CAAC,IAAI;QAAE,OAAO,IAAI,CAAC;IAEvB,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAGlE,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAC3C,IAAI,WAAW,CAAC,MAAM,GAAG,IAAI,EAAE,CAAC;YAC9B,OAAO;gBACL,OAAO,EAAE,qCAAqC;gBAC9C,IAAI,EAAE,WAAW,CAAC,MAAM;gBACxB,OAAO,EAAE,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK;aAC/C,CAAC;QACJ,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,EAAE,KAAK,EAAE,+BAA+B,EAAE,CAAC;IACpD,CAAC;AACH,CAAC;AAKD,SAAS,iBAAiB,CAAC,MAAc,EAAE,UAAkB;IAE3D,IAAI,UAAU,IAAI,GAAG;QAAE,OAAO,IAAI,CAAC;IAGnC,MAAM,gBAAgB,GAAG;QACvB,OAAO,EAAE,UAAU,EAAE,iBAAiB,EAAE,gBAAgB;QACxD,QAAQ,EAAE,aAAa,EAAE,aAAa;KACvC,CAAC;IAEF,OAAO,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;AACxE,CAAC;AAKD,SAAS,yBAAyB,CAAC,MAAc;IAC/C,MAAM,eAAe,GAAG;QACtB,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,iBAAiB,EAAE,gBAAgB;QAClE,iBAAiB,EAAE,cAAc,EAAE,QAAQ,EAAE,qBAAqB;KACnE,CAAC;IAEF,OAAO,eAAe,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;AACvE,CAAC;AAKD,SAAS,wBAAwB,CAAC,MAAc;IAC9C,MAAM,eAAe,GAAG;QACtB,gBAAgB,EAAE,gBAAgB,EAAE,gBAAgB;QACpD,iBAAiB,EAAE,iBAAiB,EAAE,iBAAiB;QACvD,aAAa,EAAE,aAAa,EAAE,iBAAiB;KAChD,CAAC;IAEF,OAAO,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;AACrE,CAAC;AAKD,SAAS,eAAe,CAAC,UAAkB;IACzC,IAAI,UAAU,IAAI,GAAG,IAAI,UAAU,GAAG,GAAG,EAAE,CAAC;QAC1C,MAAM,YAAY,GAA2B;YAC3C,GAAG,EAAE,aAAa;YAClB,GAAG,EAAE,cAAc;YACnB,GAAG,EAAE,WAAW;YAChB,GAAG,EAAE,WAAW;YAChB,GAAG,EAAE,UAAU;YACf,GAAG,EAAE,kBAAkB;YACvB,GAAG,EAAE,qBAAqB;SAC3B,CAAC;QACF,OAAO,YAAY,CAAC,UAAU,CAAC,IAAI,cAAc,CAAC;IACpD,CAAC;IAED,IAAI,UAAU,IAAI,GAAG,EAAE,CAAC;QACtB,OAAO,cAAc,CAAC;IACxB,CAAC;IAED,OAAO,eAAe,CAAC;AACzB,CAAC;AAKD,MAAM,gBAAgB,GAAG,CAAC,GAAyB,EAAE,GAAkB,EAAE,IAAkB,EAAQ,EAAE;IAEnG,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;QAC3C,OAAO,IAAI,EAAE,CAAC;IAChB,CAAC;IAED,MAAM,YAAY,GAAG,GAAG,CAAC,IAAI,CAAC;IAC9B,MAAM,YAAY,GAAG,GAAG,CAAC,IAAI,CAAC;IAE9B,GAAG,CAAC,IAAI,GAAG,UAAS,IAAS;QAC3B,GAAG,CAAC,YAAY,GAAG,IAAI,CAAC;QACxB,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACvC,CAAC,CAAC;IAEF,GAAG,CAAC,IAAI,GAAG,UAAS,IAAS;QAC3B,GAAG,CAAC,YAAY,GAAG,IAAI,CAAC;QACxB,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACvC,CAAC,CAAC;IAEF,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;QACpB,MAAM,SAAS,GAAmB;YAChC,OAAO,EAAE,GAAG,CAAC,IAAK,CAAC,EAAE;YACrB,UAAU,EAAE,GAAG,CAAC,IAAK,CAAC,KAAK;YAC3B,MAAM,EAAE,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK,EAAE,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC;YAChE,cAAc,EAAE,eAAe,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC;YAC5D,QAAQ,EAAE,GAAG,CAAC,MAAM,EAAE,EAAE,IAAI,IAAI;YAChC,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,GAAG,EAAE,GAAG,CAAC,WAAW;YACpB,UAAU,EAAE,GAAG,CAAC,UAAU;YAC1B,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;YAChC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,WAAW,EAAE,mBAAmB,CAAC,GAAG,CAAC;YACrC,YAAY,EAAE,oBAAoB,CAAC,GAAG,CAAC,YAAY,CAAC;YACpD,OAAO,EAAE,GAAG,CAAC,UAAU,GAAG,GAAG;SAC9B,CAAC;QAEF,gBAAM,CAAC,QAAQ,CAAC,cAAc,EAAE,GAAG,CAAC,IAAK,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;IAC3D,CAAC,CAAC,CAAC;IAEH,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AA4CA,4CAAgB;AAvClB,MAAM,qBAAqB,GAAG,CAAC,KAAa,EAAE,SAAiB,EAAE,EAAE;IACjE,OAAO,CAAC,GAAyB,EAAE,GAAkB,EAAE,IAAkB,EAAQ,EAAE;QACjF,MAAM,YAAY,GAAG,GAAG,CAAC,IAAI,CAAC;QAC9B,MAAM,YAAY,GAAG,GAAG,CAAC,IAAI,CAAC;QAE9B,GAAG,CAAC,IAAI,GAAG,UAAS,IAAS;YAC3B,GAAG,CAAC,YAAY,GAAG,IAAI,CAAC;YACxB,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACvC,CAAC,CAAC;QAEF,GAAG,CAAC,IAAI,GAAG,UAAS,IAAS;YAC3B,GAAG,CAAC,YAAY,GAAG,IAAI,CAAC;YACxB,OAAO,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACvC,CAAC,CAAC;QAEF,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;YACpB,IAAI,GAAG,CAAC,UAAU,GAAG,GAAG,EAAE,CAAC;gBACzB,MAAM,SAAS,GAAwB;oBACrC,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE;oBACpB,SAAS,EAAE,GAAG,CAAC,IAAI,EAAE,KAAK;oBAC1B,KAAK;oBACL,SAAS;oBACT,QAAQ,EAAE,GAAG,CAAC,MAAM,EAAE,EAAE,IAAI,IAAI;oBAChC,OAAO,EAAE,SAAS,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI;oBACjD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;oBACnC,EAAE,EAAE,GAAG,CAAC,EAAE;oBACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;iBACjC,CAAC;gBAEF,gBAAM,CAAC,QAAQ,CAAC,aAAa,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE,IAAI,WAAW,EAAE,SAAS,CAAC,CAAC;YACzE,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAKA,sDAAqB"}