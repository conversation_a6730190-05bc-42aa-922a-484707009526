import express, { Request, Response, NextFunction } from 'express';
import { body, param, query } from 'express-validator';
import { subscriptionController } from '../controllers/subscriptionController';
import { webhookController } from '../controllers/webhookController';
import * as authMiddleware from '../middleware/auth';
import * as subscriptionMiddleware from '../middleware/subscription';
import { AuthenticatedRequest } from '../middleware/auth';

const router = express.Router();

// Validări pentru request-uri
const validateCheckoutSession = [
  body('planId')
    .notEmpty()
    .withMessage('Plan ID is required')
    .isUUID()
    .withMessage('Plan ID must be a valid UUID'),
];

const validateSessionId = [
  param('sessionId')
    .notEmpty()
    .withMessage('Session ID is required')
    .isString()
    .withMessage('Session ID must be a string'),
];

const validateAction = [
  param('action')
    .notEmpty()
    .withMessage('Action is required')
    .isIn(['create_expense', 'export_data', 'advanced_reports'])
    .withMessage('Invalid action'),
];

const validateDateRange = [
  query('startDate')
    .optional()
    .isISO8601()
    .withMessage('Start date must be a valid ISO 8601 date'),
  query('endDate')
    .optional()
    .isISO8601()
    .withMessage('End date must be a valid ISO 8601 date'),
];

// Rute publice (fără autentificare)

/**
 * @route GET /api/subscriptions/plans
 * @desc Obține toate planurile disponibile
 * @access Public
 */
router.get('/plans', subscriptionController.getPlans);

/**
 * @route POST /api/subscriptions/webhook
 * @desc Webhook pentru evenimente Stripe
 * @access Public (dar verificat prin semnătură)
 */
router.post('/webhook', express.raw({ type: 'application/json' }), webhookController.handleStripeWebhook);

// Rute protejate (necesită autentificare)
router.use(authMiddleware.authenticateToken);

/**
 * @route GET /api/subscriptions/current
 * @desc Obține abonamentul curent al utilizatorului
 * @access Private
 */
router.get('/current', subscriptionController.getCurrentSubscription);

/**
 * @route POST /api/subscriptions/checkout
 * @desc Creează o sesiune de checkout pentru abonament
 * @access Private
 */
router.post('/checkout', validateCheckoutSession, subscriptionController.createCheckoutSession);

/**
 * @route POST /api/subscriptions/portal
 * @desc Creează un portal pentru gestionarea abonamentului
 * @access Private
 */
router.post('/portal', subscriptionController.createCustomerPortal);

/**
 * @route POST /api/subscriptions/cancel
 * @desc Anulează abonamentul curent
 * @access Private
 */
router.post('/cancel', subscriptionController.cancelSubscription);

/**
 * @route POST /api/subscriptions/reactivate
 * @desc Reactivează un abonament anulat
 * @access Private
 */
router.post('/reactivate', subscriptionController.reactivateSubscription);

/**
 * @route GET /api/subscriptions/checkout/:sessionId
 * @desc Verifică statusul unei sesiuni de checkout
 * @access Private
 */
router.get('/checkout/:sessionId', validateSessionId, subscriptionController.checkCheckoutSession);

/**
 * @route GET /api/subscriptions/usage
 * @desc Obține statistici de utilizare pentru utilizatorul curent
 * @access Private
 */
router.get('/usage', validateDateRange, subscriptionController.getUsageStats);

/**
 * @route GET /api/subscriptions/permissions/:action
 * @desc Verifică dacă utilizatorul poate efectua o acțiune
 * @access Private
 */
router.get('/permissions/:action', validateAction, subscriptionController.checkPermission);

// Rute pentru admin
router.use('/admin', authMiddleware.requireAdmin);

/**
 * @route POST /api/subscriptions/admin/sync-plans
 * @desc Sincronizează planurile din Stripe (admin only)
 * @access Private (Admin)
 */
router.post('/admin/sync-plans', subscriptionController.syncPlans);

/**
 * @route GET /api/subscriptions/admin/stats
 * @desc Obține statistici despre abonamente (admin only)
 * @access Private (Admin)
 */
router.get('/admin/stats', validateDateRange, subscriptionController.getSubscriptionStats);

/**
 * @route GET /api/subscriptions/admin/webhooks
 * @desc Obține statistici despre webhook-uri (admin only)
 * @access Private (Admin)
 */
router.get('/admin/webhooks', validateDateRange, webhookController.getWebhookStats);

// Rute pentru testare (doar în development)
if (process.env.NODE_ENV === 'development') {
  /**
   * @route POST /api/subscriptions/dev/simulate-webhook
   * @desc Simulează un webhook pentru testare
   * @access Private (Development only)
   */
  router.post('/dev/simulate-webhook', async (req: Request, res: Response) => {
    try {
      const { eventType, data } = req.body;

      if (!eventType || !data) {
        return res.status(400).json({
          success: false,
          message: 'Event type and data are required',
        });
      }

      // Simulează un eveniment webhook
      const mockEvent = {
        id: `evt_test_${Date.now()}`,
        type: eventType,
        data: { object: data },
        created: Math.floor(Date.now() / 1000),
      };

      await webhookController.processWebhookEvent(mockEvent);

      res.json({
        success: true,
        message: 'Webhook simulated successfully',
        event: mockEvent,
      });
    } catch (error: any) {
      res.status(500).json({
        success: false,
        message: 'Failed to simulate webhook',
        error: error.message,
      });
    }
  });

  /**
   * @route GET /api/subscriptions/dev/user-info
   * @desc Obține informații detaliate despre utilizatorul curent pentru debugging
   * @access Private (Development only)
   */
  router.get('/dev/user-info', subscriptionMiddleware.addPlanInfo(), (req: AuthenticatedRequest, res: Response) => {
    res.json({
      success: true,
      data: {
        user: {
          id: req.user!.id,
          email: req.user!.email,
          role: req.user!.role,
          stripe_customer_id: req.user!.stripe_customer_id,
          plan_type: req.user!.plan_type,
          subscription_status: req.user!.subscription_status,
        },
        subscription: (req as any).userSubscription,
        usage: (req as any).userUsage,
      },
    });
  });
}

// Middleware pentru gestionarea erorilor specifice rutelor de abonament
router.use((error: any, req: Request, res: Response, next: NextFunction) => {
  console.error('Subscription route error:', error);

  // Erori specifice Stripe
  if (error.type && error.type.startsWith('Stripe')) {
    return res.status(400).json({
      success: false,
      message: 'Payment processing error',
      code: error.code,
      details: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
  }

  // Erori de validare
  if (error.name === 'ValidationError') {
    return res.status(400).json({
      success: false,
      message: 'Validation error',
      errors: error.errors,
    });
  }

  // Erori generale
  return res.status(500).json({
    success: false,
    message: 'Internal server error',
    details: process.env.NODE_ENV === 'development' ? error.message : undefined,
  });
});

export default router;