import { Request, Response } from 'express';
interface AuthenticatedRequest extends Request {
    user?: any;
    userId?: string;
}
declare class WebhookController {
    handleStripeWebhook(req: Request, res: Response): Promise<void>;
    logWebhookEvent(event: any): Promise<void>;
    processWebhookEvent(event: any): Promise<void>;
    handleSubscriptionCreated(subscription: any): Promise<void>;
    handleSubscriptionUpdated(subscription: any): Promise<void>;
    handleSubscriptionDeleted(subscription: any): Promise<void>;
    handleTrialWillEnd(subscription: any): Promise<void>;
    handlePaymentSucceeded(invoice: any): Promise<void>;
    handlePaymentFailed(invoice: any): Promise<void>;
    handleCustomerCreated(customer: any): Promise<void>;
    handleCustomerUpdated(customer: any): Promise<void>;
    markEventAsProcessed(eventId: string): Promise<void>;
    getWebhookStats(req: AuthenticatedRequest, res: Response): Promise<void>;
}
export declare const webhookController: WebhookController;
export {};
//# sourceMappingURL=webhookController.d.ts.map