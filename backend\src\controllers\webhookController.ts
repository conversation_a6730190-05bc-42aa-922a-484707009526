import { Request, Response } from 'express';
import { prisma } from '../config/prisma';
import { stripeService } from '../services/stripeService';
import { subscriptionService } from '../services/subscriptionService';
import { usageService } from '../services/usageService';

// Temporary logger replacement
const logger = {
  info: (message: string, data?: any) => console.log('INFO:', message, data),
  error: (message: string, error?: any) => console.error('ERROR:', message, error)
};

interface AuthenticatedRequest extends Request {
  user?: any;
  userId?: string;
}

class WebhookController {
  /**
   * Gestionează webhook-urile Stripe
   */
  async handleStripeWebhook(req: Request, res: Response): Promise<void> {
    const sig = req.headers['stripe-signature'];
    const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

    let event;

    try {
      // Verifică semnătura webhook-ului
      event = stripeService.constructWebhookEvent(req.body, sig, endpointSecret);
    } catch (err) {
      logger.error('Webhook signature verification failed:', err.message);
      return res.status(400).send(`Webhook Error: ${err.message}`);
    }

    try {
      // Înregistrează evenimentul în baza de date
      await this.logWebhookEvent(event);

      // Procesează evenimentul
      await this.processWebhookEvent(event);

      res.json({ received: true });
    } catch (error) {
      logger.error('Error processing webhook:', error);
      res.status(500).json({ error: 'Webhook processing failed' });
    }
  }

  /**
   * Înregistrează evenimentul webhook în baza de date
   */
  async logWebhookEvent(event: any): Promise<void> {
    try {
      await prisma.webhookEvent.create({
        data: {
          stripe_id: event.id,
          type: event.type,
          data: event.data,
          processed: false,
          created_at: new Date(event.created * 1000),
        },
      });

      logger.info(`Webhook event logged: ${event.type} - ${event.id}`);
    } catch (error) {
      // Dacă evenimentul există deja, îl ignorăm
      if (error.code === 'P2002') {
        logger.info(`Webhook event already exists: ${event.id}`);
        return;
      }
      throw error;
    }
  }

  /**
   * Procesează evenimentul webhook
   */
  async processWebhookEvent(event: any): Promise<void> {
    logger.info(`Processing webhook: ${event.type}`);

    switch (event.type) {
      case 'customer.subscription.created':
        await this.handleSubscriptionCreated(event.data.object);
        break;

      case 'customer.subscription.updated':
        await this.handleSubscriptionUpdated(event.data.object);
        break;

      case 'customer.subscription.deleted':
        await this.handleSubscriptionDeleted(event.data.object);
        break;

      case 'customer.subscription.trial_will_end':
        await this.handleTrialWillEnd(event.data.object);
        break;

      case 'invoice.payment_succeeded':
        await this.handlePaymentSucceeded(event.data.object);
        break;

      case 'invoice.payment_failed':
        await this.handlePaymentFailed(event.data.object);
        break;

      case 'customer.created':
        await this.handleCustomerCreated(event.data.object);
        break;

      case 'customer.updated':
        await this.handleCustomerUpdated(event.data.object);
        break;

      default:
        logger.info(`Unhandled webhook type: ${event.type}`);
    }

    // Marchează evenimentul ca procesat
    await this.markEventAsProcessed(event.id);
  }

  /**
   * Gestionează crearea unui abonament
   */
  async handleSubscriptionCreated(subscription: any): Promise<void> {
    try {
      logger.info(`Processing subscription created: ${subscription.id}`);

      // Găsește utilizatorul bazat pe customer ID
      const user = await prisma.user.findFirst({
        where: { stripe_customer_id: subscription.customer },
      });

      if (!user) {
        logger.error(`User not found for customer: ${subscription.customer}`);
        return;
      }

      // Găsește planul bazat pe price ID
      const plan = await prisma.plan.findFirst({
        where: { stripe_id: subscription.items.data[0].price.id },
      });

      if (!plan) {
        logger.error(`Plan not found for price: ${subscription.items.data[0].price.id}`);
        return;
      }

      // Creează abonamentul în baza de date
      await subscriptionService.createSubscription({
        stripeSubscriptionId: subscription.id,
        userId: user.id,
        planId: plan.id,
        status: subscription.status,
        currentPeriodStart: new Date(subscription.current_period_start * 1000),
        currentPeriodEnd: new Date(subscription.current_period_end * 1000),
        trialStart: subscription.trial_start ? new Date(subscription.trial_start * 1000) : null,
        trialEnd: subscription.trial_end ? new Date(subscription.trial_end * 1000) : null,
        metadata: subscription.metadata,
      });

      // Înregistrează utilizarea
      await usageService.logUsage(user.id, 'subscription_created', {
        subscription_id: subscription.id,
        plan_id: plan.id,
      });

      logger.info(`Subscription created successfully for user: ${user.id}`);
    } catch (error) {
      logger.error('Error handling subscription created:', error);
      throw error;
    }
  }

  /**
   * Gestionează actualizarea unui abonament
   */
  async handleSubscriptionUpdated(subscription: any): Promise<void> {
    try {
      logger.info(`Processing subscription updated: ${subscription.id}`);

      const updateData = {
        status: subscription.status,
        current_period_start: new Date(subscription.current_period_start * 1000),
        current_period_end: new Date(subscription.current_period_end * 1000),
        trial_start: subscription.trial_start ? new Date(subscription.trial_start * 1000) : null,
        trial_end: subscription.trial_end ? new Date(subscription.trial_end * 1000) : null,
        canceled_at: subscription.canceled_at ? new Date(subscription.canceled_at * 1000) : null,
        metadata: subscription.metadata,
        updated_at: new Date(),
      };

      await subscriptionService.updateSubscription(subscription.id, updateData);

      // Găsește utilizatorul pentru logging
      const dbSubscription = await prisma.subscription.findUnique({
        where: { stripe_id: subscription.id },
        include: { user: true },
      });

      if (dbSubscription) {
        await usageService.logUsage(dbSubscription.user.id, 'subscription_updated', {
          subscription_id: subscription.id,
          status: subscription.status,
        });
      }

      logger.info(`Subscription updated successfully: ${subscription.id}`);
    } catch (error) {
      logger.error('Error handling subscription updated:', error);
      throw error;
    }
  }

  /**
   * Gestionează ștergerea unui abonament
   */
  async handleSubscriptionDeleted(subscription: any): Promise<void> {
    try {
      logger.info(`Processing subscription deleted: ${subscription.id}`);

      // Găsește abonamentul în baza de date
      const dbSubscription = await prisma.subscription.findUnique({
        where: { stripe_id: subscription.id },
        include: { user: true },
      });

      if (!dbSubscription) {
        logger.error(`Subscription not found in database: ${subscription.id}`);
        return;
      }

      // Actualizează statusul abonamentului
      await subscriptionService.updateSubscription(subscription.id, {
        status: 'canceled',
        canceled_at: new Date(),
        updated_at: new Date(),
      });

      // Downgradează utilizatorul la planul free
      await subscriptionService.downgradeToFreePlan(dbSubscription.user.id);

      // Înregistrează utilizarea
      await usageService.logUsage(dbSubscription.user.id, 'subscription_deleted', {
        subscription_id: subscription.id,
      });

      logger.info(`Subscription deleted successfully: ${subscription.id}`);
    } catch (error) {
      logger.error('Error handling subscription deleted:', error);
      throw error;
    }
  }

  /**
   * Gestionează sfârșitul perioadei de trial
   */
  async handleTrialWillEnd(subscription: any): Promise<void> {
    try {
      logger.info(`Processing trial will end: ${subscription.id}`);

      // Găsește utilizatorul
      const dbSubscription = await prisma.subscription.findUnique({
        where: { stripe_id: subscription.id },
        include: { user: true },
      });

      if (!dbSubscription) {
        logger.error(`Subscription not found: ${subscription.id}`);
        return;
      }

      // Înregistrează evenimentul
      await usageService.logUsage(dbSubscription.user.id, 'trial_will_end', {
        subscription_id: subscription.id,
        trial_end: subscription.trial_end,
      });

      // Aici poți adăuga logica pentru notificări email
      logger.info(`Trial will end notification logged for user: ${dbSubscription.user.id}`);
    } catch (error) {
      logger.error('Error handling trial will end:', error);
      throw error;
    }
  }

  /**
   * Gestionează plata reușită
   */
  async handlePaymentSucceeded(invoice: any): Promise<void> {
    try {
      logger.info(`Processing payment succeeded: ${invoice.id}`);

      if (invoice.subscription) {
        // Găsește abonamentul
        const dbSubscription = await prisma.subscription.findUnique({
          where: { stripe_id: invoice.subscription },
          include: { user: true },
        });

        if (dbSubscription) {
          // Înregistrează plata reușită
          await usageService.logUsage(dbSubscription.user.id, 'payment_succeeded', {
            invoice_id: invoice.id,
            amount: invoice.amount_paid,
            subscription_id: invoice.subscription,
          });

          // Resetează contorul de utilizare pentru noua perioadă
          await usageService.resetMonthlyUsageIfNeeded(dbSubscription.user.id);
        }
      }

      logger.info(`Payment succeeded processed: ${invoice.id}`);
    } catch (error) {
      logger.error('Error handling payment succeeded:', error);
      throw error;
    }
  }

  /**
   * Gestionează plata eșuată
   */
  async handlePaymentFailed(invoice: any): Promise<void> {
    try {
      logger.info(`Processing payment failed: ${invoice.id}`);

      if (invoice.subscription) {
        // Găsește abonamentul
        const dbSubscription = await prisma.subscription.findUnique({
          where: { stripe_id: invoice.subscription },
          include: { user: true },
        });

        if (dbSubscription) {
          // Înregistrează plata eșuată
          await usageService.logUsage(dbSubscription.user.id, 'payment_failed', {
            invoice_id: invoice.id,
            amount: invoice.amount_due,
            subscription_id: invoice.subscription,
          });

          // Aici poți adăuga logica pentru notificări și gestionarea eșecurilor de plată
        }
      }

      logger.info(`Payment failed processed: ${invoice.id}`);
    } catch (error) {
      logger.error('Error handling payment failed:', error);
      throw error;
    }
  }

  /**
   * Gestionează crearea unui client
   */
  async handleCustomerCreated(customer: any): Promise<void> {
    try {
      logger.info(`Processing customer created: ${customer.id}`);

      // Actualizează utilizatorul cu customer ID-ul
      if (customer.email) {
        await prisma.user.updateMany({
          where: { email: customer.email },
          data: { stripe_customer_id: customer.id },
        });
      }

      logger.info(`Customer created processed: ${customer.id}`);
    } catch (error) {
      logger.error('Error handling customer created:', error);
      throw error;
    }
  }

  /**
   * Gestionează actualizarea unui client
   */
  async handleCustomerUpdated(customer: any): Promise<void> {
    try {
      logger.info(`Processing customer updated: ${customer.id}`);

      // Găsește utilizatorul și actualizează informațiile
      const user = await prisma.user.findFirst({
        where: { stripe_customer_id: customer.id },
      });

      if (user) {
        await usageService.logUsage(user.id, 'customer_updated', {
          customer_id: customer.id,
        });
      }

      logger.info(`Customer updated processed: ${customer.id}`);
    } catch (error) {
      logger.error('Error handling customer updated:', error);
      throw error;
    }
  }

  /**
   * Marchează evenimentul ca procesat
   */
  async markEventAsProcessed(eventId: string): Promise<void> {
    try {
      await prisma.webhookEvent.update({
        where: { stripe_id: eventId },
        data: {
          processed: true,
          processed_at: new Date(),
        },
      });
    } catch (error) {
      logger.error('Error marking event as processed:', error);
      // Nu aruncăm eroarea pentru că nu vrem să eșueze webhook-ul
    }
  }

  /**
   * Obține statistici despre webhook-uri (admin only)
   */
  async getWebhookStats(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {

      const { startDate, endDate } = req.query;
      const dateFilter = {};

      if (startDate || endDate) {
        dateFilter.created_at = {};
        if (startDate) dateFilter.created_at.gte = new Date(startDate);
        if (endDate) dateFilter.created_at.lte = new Date(endDate);
      }

      const stats = await prisma.webhookEvent.groupBy({
        by: ['type', 'processed'],
        where: dateFilter,
        _count: {
          id: true,
        },
      });

      const recentEvents = await prisma.webhookEvent.findMany({
        where: dateFilter,
        orderBy: { created_at: 'desc' },
        take: 20,
      });

      res.json({
        success: true,
        data: {
          stats,
          recent_events: recentEvents,
        },
      });
    } catch (error) {
      logger.error('Error getting webhook stats:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get webhook statistics',
      });
    }
  }
}

export const webhookController = new WebhookController();