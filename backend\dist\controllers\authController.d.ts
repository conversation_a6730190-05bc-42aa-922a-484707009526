import { Request, Response } from 'express';
interface AuthenticatedRequest extends Request {
    user?: any;
}
declare const register: (req: Request, res: Response) => Promise<void>;
declare const login: (req: Request, res: Response) => Promise<void>;
declare const refreshToken: (req: Request, res: Response) => Promise<void>;
declare const logout: (req: AuthenticatedRequest, res: Response) => Promise<void>;
declare const getProfile: (req: AuthenticatedRequest, res: Response) => Promise<void>;
declare const updateProfile: (req: AuthenticatedRequest, res: Response) => Promise<void>;
declare const changePassword: (req: AuthenticatedRequest, res: Response) => Promise<void>;
declare const forgotPassword: (req: Request, res: Response) => Promise<void>;
declare const resetPassword: (req: Request, res: Response) => Promise<void>;
declare const verifyEmail: (req: Request, res: Response) => Promise<void>;
declare const resendVerification: (req: AuthenticatedRequest, res: Response) => Promise<void>;
export { register, login, refreshToken, logout, getProfile, updateProfile, changePassword, forgotPassword, resetPassword, verifyEmail, resendVerification, };
declare const _default: {
    register: (req: Request, res: Response) => Promise<void>;
    login: (req: Request, res: Response) => Promise<void>;
    refreshToken: (req: Request, res: Response) => Promise<void>;
    logout: (req: AuthenticatedRequest, res: Response) => Promise<void>;
    getProfile: (req: AuthenticatedRequest, res: Response) => Promise<void>;
    updateProfile: (req: AuthenticatedRequest, res: Response) => Promise<void>;
    changePassword: (req: AuthenticatedRequest, res: Response) => Promise<void>;
    forgotPassword: (req: Request, res: Response) => Promise<void>;
    resetPassword: (req: Request, res: Response) => Promise<void>;
    verifyEmail: (req: Request, res: Response) => Promise<void>;
    resendVerification: (req: AuthenticatedRequest, res: Response) => Promise<void>;
};
export default _default;
//# sourceMappingURL=authController.d.ts.map