import { Request, Response, NextFunction } from 'express';
interface AuthenticatedRequest extends Request {
    user?: {
        id: string;
        email?: string;
        role?: string;
        [key: string]: any;
    };
    auditId?: string;
    startTime?: number;
    sessionID?: string;
}
interface AuditResponse extends Response {
    responseData?: any;
}
declare const auditLogger: (req: AuthenticatedRequest, res: AuditResponse, next: NextFunction) => void;
declare const adminAuditLogger: (req: AuthenticatedRequest, res: AuditResponse, next: NextFunction) => void;
declare const dataChangeAuditLogger: (model: string, operation: string) => (req: AuthenticatedRequest, res: AuditResponse, next: NextFunction) => void;
export { auditLogger, adminAuditLogger, dataChangeAuditLogger };
//# sourceMappingURL=audit.d.ts.map