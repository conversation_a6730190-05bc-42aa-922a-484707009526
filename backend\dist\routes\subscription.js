"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const subscriptionController_1 = require("../controllers/subscriptionController");
const webhookController_1 = require("../controllers/webhookController");
const authMiddleware = __importStar(require("../middleware/auth"));
const subscriptionMiddleware = __importStar(require("../middleware/subscription"));
const router = express_1.default.Router();
const validateCheckoutSession = [
    (0, express_validator_1.body)('planId')
        .notEmpty()
        .withMessage('Plan ID is required')
        .isUUID()
        .withMessage('Plan ID must be a valid UUID'),
];
const validateSessionId = [
    (0, express_validator_1.param)('sessionId')
        .notEmpty()
        .withMessage('Session ID is required')
        .isString()
        .withMessage('Session ID must be a string'),
];
const validateAction = [
    (0, express_validator_1.param)('action')
        .notEmpty()
        .withMessage('Action is required')
        .isIn(['create_expense', 'export_data', 'advanced_reports'])
        .withMessage('Invalid action'),
];
const validateDateRange = [
    (0, express_validator_1.query)('startDate')
        .optional()
        .isISO8601()
        .withMessage('Start date must be a valid ISO 8601 date'),
    (0, express_validator_1.query)('endDate')
        .optional()
        .isISO8601()
        .withMessage('End date must be a valid ISO 8601 date'),
];
router.get('/plans', subscriptionController_1.subscriptionController.getPlans);
router.post('/webhook', express_1.default.raw({ type: 'application/json' }), webhookController_1.webhookController.handleStripeWebhook);
router.use(authMiddleware.authenticateToken);
router.get('/current', subscriptionController_1.subscriptionController.getCurrentSubscription);
router.post('/checkout', validateCheckoutSession, subscriptionController_1.subscriptionController.createCheckoutSession);
router.post('/portal', subscriptionController_1.subscriptionController.createCustomerPortal);
router.post('/cancel', subscriptionController_1.subscriptionController.cancelSubscription);
router.post('/reactivate', subscriptionController_1.subscriptionController.reactivateSubscription);
router.get('/checkout/:sessionId', validateSessionId, subscriptionController_1.subscriptionController.checkCheckoutSession);
router.get('/usage', validateDateRange, subscriptionController_1.subscriptionController.getUsageStats);
router.get('/permissions/:action', validateAction, subscriptionController_1.subscriptionController.checkPermission);
router.use('/admin', authMiddleware.requireAdmin);
router.post('/admin/sync-plans', subscriptionController_1.subscriptionController.syncPlans);
router.get('/admin/stats', validateDateRange, subscriptionController_1.subscriptionController.getSubscriptionStats);
router.get('/admin/webhooks', validateDateRange, webhookController_1.webhookController.getWebhookStats);
if (process.env.NODE_ENV === 'development') {
    router.post('/dev/simulate-webhook', async (req, res) => {
        try {
            const { eventType, data } = req.body;
            if (!eventType || !data) {
                return res.status(400).json({
                    success: false,
                    message: 'Event type and data are required',
                });
            }
            const mockEvent = {
                id: `evt_test_${Date.now()}`,
                type: eventType,
                data: { object: data },
                created: Math.floor(Date.now() / 1000),
            };
            await webhookController_1.webhookController.processWebhookEvent(mockEvent);
            res.json({
                success: true,
                message: 'Webhook simulated successfully',
                event: mockEvent,
            });
        }
        catch (error) {
            res.status(500).json({
                success: false,
                message: 'Failed to simulate webhook',
                error: error.message,
            });
        }
    });
    router.get('/dev/user-info', subscriptionMiddleware.addPlanInfo(), (req, res) => {
        res.json({
            success: true,
            data: {
                user: {
                    id: req.user.id,
                    email: req.user.email,
                    role: req.user.role,
                    stripe_customer_id: req.user.stripe_customer_id,
                    plan_type: req.user.plan_type,
                    subscription_status: req.user.subscription_status,
                },
                subscription: req.userSubscription,
                usage: req.userUsage,
            },
        });
    });
}
router.use((error, req, res, next) => {
    console.error('Subscription route error:', error);
    if (error.type && error.type.startsWith('Stripe')) {
        return res.status(400).json({
            success: false,
            message: 'Payment processing error',
            code: error.code,
            details: process.env.NODE_ENV === 'development' ? error.message : undefined,
        });
    }
    if (error.name === 'ValidationError') {
        return res.status(400).json({
            success: false,
            message: 'Validation error',
            errors: error.errors,
        });
    }
    res.status(500).json({
        success: false,
        message: 'Internal server error',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined,
    });
});
exports.default = router;
//# sourceMappingURL=subscription.js.map