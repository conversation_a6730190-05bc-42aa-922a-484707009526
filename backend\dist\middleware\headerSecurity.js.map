{"version": 3, "file": "headerSecurity.js", "sourceRoot": "", "sources": ["../../src/middleware/headerSecurity.ts"], "names": [], "mappings": ";;;;;;AACA,mCAAoC;AACpC,6DAAqC;AAc9B,MAAM,cAAc,GAAG,CAAC,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IAEnG,MAAM,SAAS,GAAG,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;IACxC,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,EAAE,IAAI,SAAS,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;QAClE,gBAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE;YAC5C,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,GAAG,EAAE,GAAG,CAAC,WAAW;YACpB,SAAS,EAAE,SAAS,IAAI,SAAS;YACjC,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE;SACrB,CAAC,CAAC;IACL,CAAC;IAGD,MAAM,iBAAiB,GAAG,CAAC,kBAAkB,EAAE,gBAAgB,EAAE,eAAe,CAAC,CAAC;IAClF,KAAK,MAAM,MAAM,IAAI,iBAAiB,EAAE,CAAC;QACvC,IAAI,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YACpB,gBAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE;gBACxC,EAAE,EAAE,GAAG,CAAC,EAAE;gBACV,GAAG,EAAE,GAAG,CAAC,WAAW;gBACpB,MAAM;gBACN,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC;gBACtB,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE;aACrB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAGD,GAAG,CAAC,GAAG,CAAC;QACN,cAAc,EAAE,GAAG,CAAC,EAAE,IAAI,IAAA,mBAAU,GAAE;QACtC,iBAAiB,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;QACxC,wBAAwB,EAAE,SAAS;QACnC,oBAAoB,EAAE,QAAQ;QAC9B,mCAAmC,EAAE,MAAM;KAC5C,CAAC,CAAC;IAEH,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AApCW,QAAA,cAAc,kBAoCzB;AAEF,kBAAe,sBAAc,CAAC"}