import { Request, Response } from 'express';
import { JwtPayload } from 'jsonwebtoken';
export interface AuthenticatedRequest extends Request {
    user?: {
        id: string;
        email: string;
        subscription?: {
            planId: string;
            status: string;
        };
    };
}
export interface CustomJwtPayload extends JwtPayload {
    userId: string;
    email: string;
}
export interface ApiError extends Error {
    statusCode: number;
    isOperational: boolean;
}
export interface SubscriptionUsage {
    userId: string;
    feature: string;
    count: number;
    limit: number;
    resetDate: Date;
}
export interface StripeWebhookEvent {
    id: string;
    type: string;
    data: {
        object: any;
    };
}
export interface User {
    id: string;
    email: string;
    name: string;
    password: string;
    avatar?: string;
    created_at: Date;
    updated_at: Date;
    subscription?: Subscription;
    expenses: Expense[];
    categories: Category[];
}
export interface Expense {
    id: string;
    amount: number;
    description: string;
    date: Date;
    userId: string;
    categoryId: string;
    created_at: Date;
    updated_at: Date;
    user: User;
    category: Category;
}
export interface Category {
    id: string;
    name: string;
    color: string;
    icon: string;
    userId: string;
    created_at: Date;
    updated_at: Date;
    user: User;
    expenses: Expense[];
}
export interface Subscription {
    id: string;
    userId: string;
    stripeCustomerId: string;
    stripeSubscriptionId?: string;
    planId: string;
    status: 'active' | 'canceled' | 'past_due' | 'incomplete';
    currentPeriodStart: Date;
    currentPeriodEnd: Date;
    created_at: Date;
    updated_at: Date;
    user: User;
    plan: SubscriptionPlan;
}
export interface SubscriptionPlan {
    id: string;
    name: string;
    description: string;
    price: number;
    currency: string;
    interval: 'month' | 'year';
    features: string[];
    stripePriceId: string;
    isActive: boolean;
    created_at: Date;
    updated_at: Date;
    subscriptions: Subscription[];
}
export interface ApiResponse<T = any> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
}
export interface PaginatedResponse<T> {
    data: T[];
    pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
    };
}
export interface CreateExpenseDto {
    amount: number;
    description: string;
    categoryId: string;
    date: string;
}
export interface UpdateExpenseDto {
    amount?: number;
    description?: string;
    categoryId?: string;
    date?: string;
}
export interface CreateCategoryDto {
    name: string;
    color: string;
    icon: string;
}
export interface UpdateCategoryDto {
    name?: string;
    color?: string;
    icon?: string;
}
export interface RegisterDto {
    name: string;
    email: string;
    password: string;
}
export interface LoginDto {
    email: string;
    password: string;
}
export interface ExpenseQueryParams {
    page?: number;
    limit?: number;
    categoryId?: string;
    dateFrom?: string;
    dateTo?: string;
    minAmount?: number;
    maxAmount?: number;
    sortBy?: 'date' | 'amount' | 'description';
    sortOrder?: 'asc' | 'desc';
}
export interface ExpenseStats {
    totalExpenses: number;
    monthlyTotal: number;
    categoryBreakdown: {
        categoryId: string;
        categoryName: string;
        total: number;
        count: number;
        percentage: number;
    }[];
    monthlyTrend: {
        month: string;
        year: number;
        total: number;
        count: number;
    }[];
}
export interface ValidationError {
    field: string;
    message: string;
    value?: any;
}
export interface RateLimitInfo {
    limit: number;
    current: number;
    remaining: number;
    resetTime: Date;
}
export interface EmailOptions {
    to: string;
    subject: string;
    html: string;
    text?: string;
}
export interface NotificationData {
    userId: string;
    type: 'email' | 'push';
    title: string;
    message: string;
    data?: Record<string, any>;
}
export interface DatabaseConfig {
    url: string;
    maxConnections: number;
    timeout: number;
}
export interface JwtConfig {
    secret: string;
    expiresIn: string;
    refreshExpiresIn: string;
}
export interface StripeConfig {
    secretKey: string;
    publishableKey: string;
    webhookSecret: string;
    successUrl: string;
    cancelUrl: string;
}
export type Omit<T, K extends keyof T> = Pick<T, Exclude<keyof T, K>>;
export type Partial<T> = {
    [P in keyof T]?: T[P];
};
export type Required<T> = {
    [P in keyof T]-?: T[P];
};
export interface ControllerResponse<T = any> {
    status: number;
    data?: T;
    message?: string;
    error?: string;
}
export type AsyncHandler = (req: AuthenticatedRequest, res: Response, next: Function) => Promise<any>;
//# sourceMappingURL=index.d.ts.map