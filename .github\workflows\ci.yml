name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

env:
  NODE_VERSION: '18'
  POSTGRES_VERSION: '15'
  REDIS_VERSION: '7'

jobs:
  # Job pentru testarea și linting-ul backend-ului
  backend-test:
    name: Backend Tests & Linting
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: expense_tracker_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: backend/package-lock.json

      - name: Install backend dependencies
        working-directory: ./backend
        run: npm ci

      - name: Setup environment variables
        working-directory: ./backend
        run: |
          echo "NODE_ENV=test" >> .env.test
          echo "DATABASE_URL=postgresql://postgres:postgres@localhost:5432/expense_tracker_test" >> .env.test
          echo "REDIS_URL=redis://localhost:6379" >> .env.test
          echo "JWT_SECRET=test-jwt-secret-key-for-ci" >> .env.test
          echo "JWT_REFRESH_SECRET=test-refresh-secret-key-for-ci" >> .env.test

      - name: Run database migrations
        working-directory: ./backend
        run: npx prisma migrate deploy
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/expense_tracker_test

      - name: Generate Prisma client
        working-directory: ./backend
        run: npx prisma generate

      - name: Run ESLint
        working-directory: ./backend
        run: npm run lint

      - name: Run TypeScript type checking
        working-directory: ./backend
        run: npm run type-check

      - name: Run unit tests
        working-directory: ./backend
        run: npm run test:coverage
        env:
          NODE_ENV: test
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/expense_tracker_test
          REDIS_URL: redis://localhost:6379

      - name: Upload backend coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          files: ./backend/coverage/lcov.info
          flags: backend
          name: backend-coverage

  # Job pentru testarea și linting-ul frontend-ului
  frontend-test:
    name: Frontend Tests & Linting
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Install frontend dependencies
        working-directory: ./frontend
        run: npm ci

      - name: Run ESLint
        working-directory: ./frontend
        run: npm run lint

      - name: Run TypeScript type checking
        working-directory: ./frontend
        run: npm run type-check

      - name: Run unit tests
        working-directory: ./frontend
        run: npm run test:coverage

      - name: Upload frontend coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          files: ./frontend/coverage/lcov.info
          flags: frontend
          name: frontend-coverage

  # Job pentru security scanning
  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest
    needs: [backend-test, frontend-test]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Run npm audit for backend
        working-directory: ./backend
        run: npm audit --audit-level high
        continue-on-error: true

      - name: Run npm audit for frontend
        working-directory: ./frontend
        run: npm audit --audit-level high
        continue-on-error: true

      - name: Run Snyk security scan
        if: ${{ secrets.SNYK_TOKEN != '' }}
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high
        continue-on-error: true

  # Job pentru build și deployment
  build-and-deploy:
    name: Build & Deploy
    runs-on: ubuntu-latest
    needs: [backend-test, frontend-test, security-scan]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install backend dependencies
        working-directory: ./backend
        run: npm ci

      - name: Install frontend dependencies
        working-directory: ./frontend
        run: npm ci

      - name: Build backend
        working-directory: ./backend
        run: npm run build

      - name: Build frontend
        working-directory: ./frontend
        run: npm run build

      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: build-files
          path: |
            backend/dist
            frontend/dist
          retention-days: 7

      # Deploy to staging environment
      - name: Deploy to staging
        if: github.ref == 'refs/heads/develop'
        run: |
          echo "Deploying to staging environment..."
          # Add staging deployment commands here

      # Deploy to production environment
      - name: Deploy to production
        if: github.ref == 'refs/heads/main' && secrets.VERCEL_TOKEN != '' && secrets.RAILWAY_TOKEN != ''
        env:
          VERCEL_TOKEN: ${{ secrets.VERCEL_TOKEN }}
          VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
          VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}
          RAILWAY_TOKEN: ${{ secrets.RAILWAY_TOKEN }}
        run: |
          echo "Deploying to production environment..."

          # Deploy frontend to Vercel
          npx vercel --token $VERCEL_TOKEN --prod --yes

          # Deploy backend to Railway
          # Add Railway deployment commands here

      - name: Run database migrations in production
        if: github.ref == 'refs/heads/main' && secrets.DATABASE_URL != ''
        working-directory: ./backend
        run: npx prisma migrate deploy
        env:
          DATABASE_URL: ${{ secrets.DATABASE_URL }}

      - name: Notify deployment success
        if: success() && secrets.SLACK_WEBHOOK != ''
        uses: 8398a7/action-slack@v3
        with:
          status: success
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
          message: |
            🚀 Deployment successful!
            Branch: ${{ github.ref }}
            Commit: ${{ github.sha }}
            Author: ${{ github.actor }}

      - name: Notify deployment failure
        if: failure()
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
          message: |
            ❌ Deployment failed!
            Branch: ${{ github.ref }}
            Commit: ${{ github.sha }}
            Author: ${{ github.actor }}

  # Job pentru performance testing
  performance-test:
    name: Performance Testing
    runs-on: ubuntu-latest
    needs: [build-and-deploy]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install Lighthouse CI
        run: npm install -g @lhci/cli@0.12.x

      - name: Run Lighthouse CI
        run: lhci autorun
        env:
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}

      - name: Upload Lighthouse results
        uses: actions/upload-artifact@v3
        with:
          name: lighthouse-results
          path: .lighthouseci
          retention-days: 7
