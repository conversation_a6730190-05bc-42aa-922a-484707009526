import express from 'express';
import {
  getCategories,
  getCategory,
  createCategory,
  updateCategory,
  deleteCategory,
  getCategoryStats,
  getCategoriesWithStats,
  reorderCategories,
  setDefaultCategory,
} from '../controllers/categoryController';
import { authenticateToken } from '../middleware/auth';
import { validate, categorySchemas, paramSchemas } from '../middleware/validation';
import { usageMiddleware } from '../middleware/usageLimits';

const router = express.Router();

/**
 * @route   GET /api/categories
 * @desc    Get all categories for the authenticated user
 * @access  Private
 */
router.get('/', authenticateToken, getCategories);

/**
 * @route   GET /api/categories/stats
 * @desc    Get categories with expense statistics
 * @access  Private
 */
router.get('/stats', authenticateToken, getCategoriesWithStats);

/**
 * @route   GET /api/categories/:id
 * @desc    Get a single category by ID
 * @access  Private
 */
router.get('/:id', authenticateToken, validate(paramSchemas.id, 'params'), getCategory);

/**
 * @route   GET /api/categories/:id/stats
 * @desc    Get category statistics
 * @access  Private
 */
router.get('/:id/stats', authenticateToken, validate(paramSchemas.id, 'params'), getCategoryStats);

/**
 * @route   POST /api/categories
 * @desc    Create a new category
 * @access  Private
 */
router.post(
  '/',
  authenticateToken,
  ...usageMiddleware('create_category'),
  validate(categorySchemas.create),
  createCategory,
);

/**
 * @route   PUT /api/categories/:id
 * @desc    Update a category
 * @access  Private
 */
router.put(
  '/:id',
  authenticateToken,
  validate(paramSchemas.id, 'params'),
  validate(categorySchemas.update),
  updateCategory,
);

/**
 * @route   DELETE /api/categories/:id
 * @desc    Delete a category
 * @access  Private
 */
router.delete('/:id', authenticateToken, validate(paramSchemas.id, 'params'), deleteCategory);

/**
 * @route   POST /api/categories/reorder
 * @desc    Reorder categories
 * @access  Private
 */
router.post('/reorder', authenticateToken, reorderCategories);

/**
 * @route   POST /api/categories/:id/set-default
 * @desc    Set category as default
 * @access  Private
 */
router.post('/:id/set-default', authenticateToken, validate(paramSchemas.id, 'params'), setDefaultCategory);

export default router;
