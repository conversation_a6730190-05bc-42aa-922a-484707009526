import { Request, Response, NextFunction } from 'express';
interface AuthenticatedRequest extends Request {
    user?: any;
}
export declare const checkCategoryLimit: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const checkExpenseLimit: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const checkExportLimit: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const checkExportPermission: (format: string) => (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export {};
//# sourceMappingURL=subscriptionLimits.d.ts.map