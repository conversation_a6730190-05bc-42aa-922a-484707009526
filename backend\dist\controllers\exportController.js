"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.exportController = void 0;
const prisma_1 = require("../config/prisma");
const logger = {
    info: (message, data) => console.log('INFO:', message, data),
    error: (message, error) => console.error('ERROR:', message, error)
};
class AppError extends Error {
    constructor(message, statusCode) {
        super(message);
        this.statusCode = statusCode;
    }
}
const exportCSV = async (req, res) => {
    try {
        const { userId } = req;
        const { startDate, endDate, categoryId } = req.query;
        const startDateStr = typeof startDate === 'string' ? startDate : undefined;
        const endDateStr = typeof endDate === 'string' ? endDate : undefined;
        const categoryIdStr = typeof categoryId === 'string' ? categoryId : undefined;
        const where = {
            user_id: Number(userId),
            ...(startDateStr && endDateStr && {
                date: {
                    gte: new Date(startDateStr),
                    lte: new Date(endDateStr)
                }
            }),
            ...(categoryIdStr && { category_id: categoryIdStr })
        };
        const expenses = await prisma_1.prisma.expense.findMany({
            where,
            include: {
                category: {
                    select: {
                        name: true
                    }
                }
            },
            orderBy: {
                date: 'desc'
            }
        });
        const csvHeader = 'Data,Suma,Descriere,Categorie\n';
        const csvRows = expenses.map(expense => {
            const date = new Date(expense.date).toLocaleDateString('ro-RO');
            const amount = expense.amount.toString();
            const description = `"${expense.description.replace(/"/g, '""')}"`;
            const category = expense.category.name;
            return `${date},${amount},${description},${category}`;
        }).join('\n');
        const csvContent = csvHeader + csvRows;
        const filename = `cheltuieli_${new Date().toISOString().split('T')[0]}.csv`;
        res.setHeader('Content-Type', 'text/csv; charset=utf-8');
        res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
        res.setHeader('Content-Length', Buffer.byteLength(csvContent, 'utf8'));
        res.write('\uFEFF');
        res.end(csvContent);
        logger.info(`CSV export generat pentru utilizatorul ${userId}`, {
            userId,
            expenseCount: expenses.length,
            filters: { startDate, endDate, categoryId }
        });
    }
    catch (error) {
        logger.error('Eroare la exportul CSV:', error);
        throw new AppError('Eroare la generarea exportului CSV', 500);
    }
};
const exportPDF = async (req, res) => {
    try {
        const { userId } = req;
        const { startDate, endDate, categoryId } = req.query;
        const startDateStr = typeof startDate === 'string' ? startDate : undefined;
        const endDateStr = typeof endDate === 'string' ? endDate : undefined;
        const categoryIdStr = typeof categoryId === 'string' ? categoryId : undefined;
        const where = {
            user_id: Number(userId),
            ...(startDateStr && endDateStr && {
                date: {
                    gte: new Date(startDateStr),
                    lte: new Date(endDateStr)
                }
            }),
            ...(categoryIdStr && { category_id: categoryIdStr })
        };
        const expenses = await prisma_1.prisma.expense.findMany({
            where,
            include: {
                category: {
                    select: {
                        name: true
                    }
                }
            },
            orderBy: {
                date: 'desc'
            }
        });
        const user = await prisma_1.prisma.user.findUnique({
            where: { id: Number(userId) },
            select: { email: true, name: true }
        });
        res.status(501).json({ error: 'PDF export not yet implemented' });
        return;
        logger.info(`PDF export generat pentru utilizatorul ${userId}`, {
            userId,
            expenseCount: expenses.length,
            filters: { startDate, endDate, categoryId }
        });
    }
    catch (error) {
        logger.error('Eroare la exportul PDF:', error);
        throw new AppError('Eroare la generarea exportului PDF', 500);
    }
};
const exportExcel = async (req, res) => {
    try {
        const { userId } = req;
        const { startDate, endDate, categoryId } = req.query;
        const startDateStr = typeof startDate === 'string' ? startDate : undefined;
        const endDateStr = typeof endDate === 'string' ? endDate : undefined;
        const categoryIdStr = typeof categoryId === 'string' ? categoryId : undefined;
        const where = {
            user_id: Number(userId),
            ...(startDateStr && endDateStr && {
                date: {
                    gte: new Date(startDateStr),
                    lte: new Date(endDateStr)
                }
            }),
            ...(categoryIdStr && { category_id: categoryIdStr })
        };
        const expenses = await prisma_1.prisma.expense.findMany({
            where,
            include: {
                category: {
                    select: {
                        name: true
                    }
                }
            },
            orderBy: {
                date: 'desc'
            }
        });
        const user = await prisma_1.prisma.user.findUnique({
            where: { id: Number(userId) },
            select: { email: true, name: true }
        });
        res.status(501).json({ error: 'Excel export not yet implemented' });
        return;
        logger.info(`Excel export generat pentru utilizatorul ${userId}`, {
            userId,
            expenseCount: expenses.length,
            filters: { startDate, endDate, categoryId }
        });
    }
    catch (error) {
        logger.error('Eroare la exportul Excel:', error);
        throw new AppError('Eroare la generarea exportului Excel', 500);
    }
};
exports.exportController = {
    exportCSV,
    exportPDF,
    exportExcel
};
//# sourceMappingURL=exportController.js.map