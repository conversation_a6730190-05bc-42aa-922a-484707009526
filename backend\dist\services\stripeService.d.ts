interface CustomerPortalOptions {
    customerId: string;
    returnUrl: string;
}
interface PortalSession {
    url: string;
    id: string;
}
declare class StripeService {
    createCustomerPortal(options: CustomerPortalOptions): Promise<PortalSession>;
    getCheckoutSession(sessionId: string): Promise<any>;
    cancelSubscription(subscriptionId: string): Promise<any>;
    reactivateSubscription(subscriptionId: string): Promise<any>;
}
declare const _default: StripeService;
export default _default;
//# sourceMappingURL=stripeService.d.ts.map