{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/middleware/auth.ts"], "names": [], "mappings": ";;;;;;AACA,gEAA+B;AAC/B,6CAA0C;AAC1C,6DAAqC;AAkB9B,MAAM,iBAAiB,GAAG,KAAK,EACpC,GAAyB,EACzB,GAAa,EACb,IAAkB,EACH,EAAE;IACjB,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;QAC7C,MAAM,KAAK,GAAG,UAAU,IAAI,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAErD,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,0BAA0B;aACpC,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAGD,MAAM,OAAO,GAAG,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,UAAW,CAAe,CAAC;QAGzE,MAAM,IAAI,GAAG,MAAM,eAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YACxC,KAAK,EAAE,EAAE,EAAE,EAAE,OAAO,CAAC,MAAM,EAAE;YAC7B,MAAM,EAAE;gBACN,EAAE,EAAE,IAAI;gBACR,KAAK,EAAE,IAAI;gBACX,IAAI,EAAE,IAAI;gBACV,cAAc,EAAE,IAAI;gBACpB,UAAU,EAAE,IAAI;gBAChB,UAAU,EAAE,IAAI;aACjB;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,gCAAgC;aAC1C,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAGD,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;YACzB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,6BAA6B;aACvC,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAGD,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;QAChB,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,gBAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;QAE7C,IAAI,KAAK,YAAY,sBAAG,CAAC,iBAAiB,EAAE,CAAC;YAC3C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,eAAe;aACzB,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,IAAI,KAAK,YAAY,sBAAG,CAAC,iBAAiB,EAAE,CAAC;YAC3C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,eAAe;aACzB,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,uBAAuB;SACjC,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC;AA7EW,QAAA,iBAAiB,qBA6E5B;AAGK,MAAM,aAAa,GAAG,CAAC,MAAc,EAAU,EAAE;IACtD,OAAO,sBAAG,CAAC,IAAI,CACb,EAAE,MAAM,EAAE,EACV,OAAO,CAAC,GAAG,CAAC,UAAW,EACvB,EAAE,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,KAAK,EAAE,CACnD,CAAC;AACJ,CAAC,CAAC;AANW,QAAA,aAAa,iBAMxB;AAGK,MAAM,oBAAoB,GAAG,CAAC,MAAc,EAAU,EAAE;IAC7D,OAAO,sBAAG,CAAC,IAAI,CACb,EAAE,MAAM,EAAE,EACV,OAAO,CAAC,GAAG,CAAC,kBAAmB,EAC/B,EAAE,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,IAAI,EAAE,CAC1D,CAAC;AACJ,CAAC,CAAC;AANW,QAAA,oBAAoB,wBAM/B;AAGK,MAAM,kBAAkB,GAAG,CAAC,KAAa,EAAc,EAAE;IAC9D,OAAO,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAmB,CAAe,CAAC;AAC1E,CAAC,CAAC;AAFW,QAAA,kBAAkB,sBAE7B;AAEF,kBAAe,yBAAiB,CAAC"}