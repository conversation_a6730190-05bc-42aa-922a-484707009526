"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setDefaultCategory = exports.reorderCategories = exports.getCategoriesWithStats = exports.getCategoryStats = exports.deleteCategory = exports.updateCategory = exports.createCategory = exports.getCategory = exports.getCategories = void 0;
const prisma_1 = require("../config/prisma");
const getCategories = async (req, res) => {
    try {
        const { userId } = req;
        const { include_inactive = false } = req.query;
        if (!userId) {
            res.status(401).json({
                success: false,
                message: 'Unauthorized',
            });
            return;
        }
        const categories = await prisma_1.prisma.category.findMany({
            where: {
                user_id: userId,
                ...(include_inactive !== 'true' && { is_active: true }),
            },
            orderBy: [
                { sort_order: 'asc' },
                { name: 'asc' },
            ],
        });
        res.json({
            success: true,
            data: {
                categories,
            },
        });
    }
    catch (error) {
        console.error('Get categories error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error while fetching categories',
        });
    }
};
exports.getCategories = getCategories;
const getCategory = async (req, res) => {
    try {
        const { id } = req.params;
        const { userId } = req;
        if (!userId) {
            res.status(401).json({
                success: false,
                message: 'Unauthorized',
            });
            return;
        }
        const category = await prisma_1.prisma.category.findFirst({
            where: {
                id: parseInt(id),
                user_id: userId,
            },
        });
        if (!category) {
            res.status(404).json({
                success: false,
                message: 'Category not found',
            });
            return;
        }
        res.json({
            success: true,
            data: {
                category,
            },
        });
    }
    catch (error) {
        console.error('Get category error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error while fetching category',
        });
    }
};
exports.getCategory = getCategory;
const createCategory = async (req, res) => {
    try {
        const { userId } = req;
        if (!userId) {
            res.status(401).json({
                success: false,
                message: 'Unauthorized',
            });
            return;
        }
        const categoryData = {
            ...req.body,
            user_id: userId,
        };
        const existingCategory = await prisma_1.prisma.category.findFirst({
            where: {
                user_id: userId,
                name: categoryData.name,
            },
        });
        if (existingCategory) {
            res.status(409).json({
                success: false,
                message: 'A category with this name already exists',
            });
            return;
        }
        const category = await prisma_1.prisma.category.create({
            data: categoryData,
        });
        res.status(201).json({
            success: true,
            message: 'Category created successfully',
            data: {
                category,
            },
        });
    }
    catch (error) {
        console.error('Create category error:', error);
        if (error.code === 'P2002') {
            res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: [{ field: 'name', message: 'Category name must be unique' }],
            });
            return;
        }
        res.status(500).json({
            success: false,
            message: 'Internal server error while creating category',
        });
    }
};
exports.createCategory = createCategory;
const updateCategory = async (req, res) => {
    try {
        const { id } = req.params;
        const { userId } = req;
        const updateData = req.body;
        if (!userId) {
            res.status(401).json({
                success: false,
                message: 'Unauthorized',
            });
            return;
        }
        const category = await prisma_1.prisma.category.findFirst({
            where: {
                id: parseInt(id),
                user_id: userId,
            },
        });
        if (!category) {
            res.status(404).json({
                success: false,
                message: 'Category not found',
            });
            return;
        }
        if (updateData.name && updateData.name !== category.name) {
            const existingCategory = await prisma_1.prisma.category.findFirst({
                where: {
                    user_id: userId,
                    name: updateData.name,
                    NOT: {
                        id: category.id,
                    },
                },
            });
            if (existingCategory) {
                res.status(409).json({
                    success: false,
                    message: 'A category with this name already exists',
                });
                return;
            }
        }
        const updatedCategory = await prisma_1.prisma.category.update({
            where: { id: category.id },
            data: updateData,
        });
        res.json({
            success: true,
            message: 'Category updated successfully',
            data: {
                category: updatedCategory,
            },
        });
    }
    catch (error) {
        console.error('Update category error:', error);
        if (error.code === 'P2002') {
            res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: [{ field: 'name', message: 'Category name must be unique' }],
            });
            return;
        }
        res.status(500).json({
            success: false,
            message: 'Internal server error while updating category',
        });
    }
};
exports.updateCategory = updateCategory;
const deleteCategory = async (req, res) => {
    try {
        const { id } = req.params;
        const { userId } = req;
        if (!userId) {
            res.status(401).json({
                success: false,
                message: 'Unauthorized',
            });
            return;
        }
        const category = await prisma_1.prisma.category.findFirst({
            where: {
                id: parseInt(id),
                user_id: userId,
            },
        });
        if (!category) {
            res.status(404).json({
                success: false,
                message: 'Category not found',
            });
            return;
        }
        const expenseCount = await prisma_1.prisma.expense.count({
            where: {
                category_id: parseInt(id),
            },
        });
        if (expenseCount > 0) {
            res.status(400).json({
                success: false,
                message: `Cannot delete category. It has ${expenseCount} associated expenses. Please move or delete the expenses first.`,
                data: {
                    expense_count: expenseCount,
                },
            });
            return;
        }
        await prisma_1.prisma.category.delete({
            where: { id: category.id },
        });
        res.json({
            success: true,
            message: 'Category deleted successfully',
        });
    }
    catch (error) {
        console.error('Delete category error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error while deleting category',
        });
    }
};
exports.deleteCategory = deleteCategory;
const getCategoryStats = async (req, res) => {
    try {
        const { id } = req.params;
        const { userId } = req;
        const { period = 'monthly', start_date, end_date } = req.query;
        if (!userId) {
            res.status(401).json({
                success: false,
                message: 'Unauthorized',
            });
            return;
        }
        const category = await prisma_1.prisma.category.findFirst({
            where: {
                id: parseInt(id),
                user_id: userId,
            },
        });
        if (!category) {
            res.status(404).json({
                success: false,
                message: 'Category not found',
            });
            return;
        }
        let startDate, endDate;
        if (start_date && end_date) {
            startDate = new Date(start_date);
            endDate = new Date(end_date);
        }
        else {
            const now = new Date();
            switch (period) {
                case 'daily':
                    startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                    endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
                    break;
                case 'weekly':
                    const dayOfWeek = now.getDay();
                    startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - dayOfWeek);
                    endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + (7 - dayOfWeek));
                    break;
                case 'yearly':
                    startDate = new Date(now.getFullYear(), 0, 1);
                    endDate = new Date(now.getFullYear() + 1, 0, 1);
                    break;
                default:
                    startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                    endDate = new Date(now.getFullYear(), now.getMonth() + 1, 1);
            }
        }
        const expenseStats = await prisma_1.prisma.expense.aggregate({
            where: {
                category_id: parseInt(id),
                date: {
                    gte: startDate,
                    lt: endDate,
                },
            },
            _sum: {
                amount: true,
            },
            _count: {
                id: true,
            },
        });
        const totalExpenses = Number(expenseStats._sum.amount || 0);
        const expenseCount = expenseStats._count.id || 0;
        const budgetUsed = totalExpenses;
        const budgetLimit = Number(category.budget_limit || 0);
        const budgetRemaining = budgetLimit - budgetUsed;
        const budgetPercentage = budgetLimit > 0 ? (budgetUsed / budgetLimit) * 100 : 0;
        const budgetStatus = {
            limit: budgetLimit,
            used: budgetUsed,
            remaining: budgetRemaining,
            percentage: budgetPercentage,
            is_over_budget: budgetUsed > budgetLimit && budgetLimit > 0,
        };
        const recentExpenses = await prisma_1.prisma.expense.findMany({
            where: {
                category_id: parseInt(id),
                date: {
                    gte: startDate,
                    lt: endDate,
                },
            },
            orderBy: {
                date: 'desc',
            },
            take: 5,
            select: {
                id: true,
                amount: true,
                description: true,
                date: true,
            },
        });
        res.json({
            success: true,
            data: {
                category: {
                    id: category.id,
                    name: category.name,
                    color: category.color,
                    icon: category.icon,
                },
                period: {
                    type: period,
                    start_date: startDate,
                    end_date: endDate,
                },
                statistics: {
                    total_expenses: totalExpenses,
                    expense_count: expenseCount,
                    average_expense: expenseCount > 0 ? totalExpenses / expenseCount : 0,
                },
                budget: budgetStatus,
                recent_expenses: recentExpenses,
            },
        });
    }
    catch (error) {
        console.error('Get category stats error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error while fetching category statistics',
        });
    }
};
exports.getCategoryStats = getCategoryStats;
const getCategoriesWithStats = async (req, res) => {
    try {
        const { userId } = req;
        const { start_date, end_date, period = 'monthly' } = req.query;
        if (!userId) {
            res.status(401).json({
                success: false,
                message: 'Unauthorized',
            });
            return;
        }
        let startDate, endDate;
        if (start_date && end_date) {
            startDate = new Date(start_date);
            endDate = new Date(end_date);
        }
        else {
            const now = new Date();
            switch (period) {
                case 'daily':
                    startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                    endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
                    break;
                case 'weekly':
                    const dayOfWeek = now.getDay();
                    startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - dayOfWeek);
                    endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + (7 - dayOfWeek));
                    break;
                case 'yearly':
                    startDate = new Date(now.getFullYear(), 0, 1);
                    endDate = new Date(now.getFullYear() + 1, 0, 1);
                    break;
                default:
                    startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                    endDate = new Date(now.getFullYear(), now.getMonth() + 1, 1);
            }
        }
        const categories = await prisma_1.prisma.category.findMany({
            where: {
                user_id: userId,
                is_active: true,
            },
            orderBy: [
                { sort_order: 'asc' },
                { name: 'asc' },
            ],
        });
        const categoriesWithStats = await Promise.all(categories.map(async (category) => {
            const expenseStats = await prisma_1.prisma.expense.aggregate({
                where: {
                    category_id: category.id,
                    date: {
                        gte: startDate,
                        lt: endDate,
                    },
                },
                _sum: {
                    amount: true,
                },
                _count: {
                    id: true,
                },
            });
            const totalExpenses = Number(expenseStats._sum.amount || 0);
            const expenseCount = expenseStats._count.id || 0;
            const budgetUsed = totalExpenses;
            const budgetLimit = Number(category.budget_limit || 0);
            const budgetRemaining = budgetLimit - budgetUsed;
            const budgetPercentage = budgetLimit > 0 ? (budgetUsed / budgetLimit) * 100 : 0;
            const budgetStatus = {
                limit: budgetLimit,
                used: budgetUsed,
                remaining: budgetRemaining,
                percentage: budgetPercentage,
                is_over_budget: budgetUsed > budgetLimit && budgetLimit > 0,
            };
            return {
                id: category.id,
                name: category.name,
                description: category.description,
                color: category.color,
                icon: category.icon,
                budget_limit: category.budget_limit,
                budget_period: category.budget_period,
                is_default: category.is_default,
                sort_order: category.sort_order,
                statistics: {
                    total_expenses: totalExpenses,
                    expense_count: expenseCount,
                    average_expense: expenseCount > 0 ? totalExpenses / expenseCount : 0,
                },
                budget: budgetStatus,
            };
        }));
        res.json({
            success: true,
            data: {
                period: {
                    type: period,
                    start_date: startDate,
                    end_date: endDate,
                },
                categories: categoriesWithStats,
            },
        });
    }
    catch (error) {
        console.error('Get categories with stats error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error while fetching categories with statistics',
        });
    }
};
exports.getCategoriesWithStats = getCategoriesWithStats;
const reorderCategories = async (req, res) => {
    try {
        const { userId } = req;
        const { categories } = req.body;
        if (!userId) {
            res.status(401).json({
                success: false,
                message: 'Unauthorized',
            });
            return;
        }
        if (!Array.isArray(categories)) {
            res.status(400).json({
                success: false,
                message: 'Categories must be an array',
            });
            return;
        }
        const categoryIds = categories.map(cat => parseInt(cat.id));
        const userCategories = await prisma_1.prisma.category.findMany({
            where: {
                id: { in: categoryIds },
                user_id: userId,
            },
        });
        if (userCategories.length !== categoryIds.length) {
            res.status(400).json({
                success: false,
                message: 'Some categories do not belong to you or do not exist',
            });
            return;
        }
        await Promise.all(categories.map(async (cat, index) => {
            await prisma_1.prisma.category.update({
                where: { id: parseInt(cat.id) },
                data: { sort_order: index + 1 },
            });
        }));
        res.json({
            success: true,
            message: 'Categories reordered successfully',
        });
    }
    catch (error) {
        console.error('Reorder categories error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error while reordering categories',
        });
    }
};
exports.reorderCategories = reorderCategories;
const setDefaultCategory = async (req, res) => {
    try {
        const { id } = req.params;
        const { userId } = req;
        if (!userId) {
            res.status(401).json({
                success: false,
                message: 'Unauthorized',
            });
            return;
        }
        const category = await prisma_1.prisma.category.findFirst({
            where: {
                id: parseInt(id),
                user_id: userId,
                is_active: true,
            },
        });
        if (!category) {
            res.status(404).json({
                success: false,
                message: 'Category not found or inactive',
            });
            return;
        }
        await prisma_1.prisma.category.updateMany({
            where: {
                user_id: userId,
                is_default: true,
            },
            data: {
                is_default: false,
            },
        });
        const updatedCategory = await prisma_1.prisma.category.update({
            where: { id: category.id },
            data: { is_default: true },
        });
        res.json({
            success: true,
            message: 'Default category updated successfully',
            data: {
                category: updatedCategory,
            },
        });
    }
    catch (error) {
        console.error('Set default category error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error while setting default category',
        });
    }
};
exports.setDefaultCategory = setDefaultCategory;
//# sourceMappingURL=categoryController.js.map