"use strict";(self.webpackChunkexpense_tracker_frontend=self.webpackChunkexpense_tracker_frontend||[]).push([[633],{6215:(e,a,s)=>{s.d(a,{Ay:()=>x});var r=s(4848),t=s(2509),l=s(72),i=s(3956),n=s(7117),c=s(4015),d=s(6540),o=s(2392);const m=(0,d.forwardRef)(({label:e,type:a="text",placeholder:s,value:m,onChange:x,onBlur:p,onFocus:u,error:y,success:h,hint:f,required:g=!1,disabled:j=!1,readOnly:N=!1,size:b="md",leftIcon:w,rightIcon:v,className:A="",inputClassName:C="",labelClassName:k="",id:I,name:S,...M},R)=>{const[D,z]=(0,d.useState)(!1),[P,T]=(0,d.useState)(!1),E=I||`input-${Math.random().toString(36).substr(2,9)}`,F="password"===a&&D?"text":a,B={sm:"h-4 w-4",md:"h-5 w-5",lg:"h-6 w-6"},q=Boolean(y),_=Boolean(h)&&!q;return Boolean(w||v||"password"===a),(0,r.jsxs)("div",{className:(0,o.cn)("w-full",A),children:[e&&(0,r.jsxs)("label",{htmlFor:E,className:(0,o.cn)("block text-sm font-medium mb-2",q?"text-red-700":"text-gray-700",j&&"text-gray-400",k),children:[e,g&&(0,r.jsx)("span",{className:"text-red-500 ml-1","aria-label":"obligatoriu",children:"*"})]}),(0,r.jsxs)("div",{className:"relative",children:[w&&(0,r.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)("span",{className:(0,o.cn)(B[b],q?"text-red-400":"text-gray-400"),children:w})}),(0,r.jsx)("input",{ref:R,id:E,name:S,type:F,value:m,onChange:x,onFocus:e=>{T(!0),u?.(e)},onBlur:e=>{T(!1),p?.(e)},placeholder:s,required:g,disabled:j,readOnly:N,className:(0,o.cn)("block w-full border rounded-lg transition-all duration-200","focus:outline-none focus:ring-2 focus:ring-offset-0","placeholder:text-gray-400",{sm:"px-3 py-2 text-sm",md:"px-4 py-2.5 text-sm",lg:"px-4 py-3 text-base"}[b],w&&"pl-10",(v||"password"===a)&&"pr-10",!j&&!N&&[q?["border-red-300 text-red-900","focus:border-red-500 focus:ring-red-500"]:_?["border-green-300 text-green-900","focus:border-green-500 focus:ring-green-500"]:["border-gray-300 text-gray-900","focus:border-primary-500 focus:ring-primary-500","hover:border-gray-400"]],j&&["bg-gray-50 border-gray-200 text-gray-500","cursor-not-allowed"],N&&["bg-gray-50 border-gray-200","cursor-default"],C),...M}),(0,r.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:"password"===a?(0,r.jsx)("button",{type:"button",onClick:()=>{z(!D)},className:(0,o.cn)("text-gray-400 hover:text-gray-600 focus:outline-none",B[b]),"aria-label":D?"Ascunde parola":"Arată parola",children:D?(0,r.jsx)(t.A,{className:B[b]}):(0,r.jsx)(l.A,{className:B[b]})}):q?(0,r.jsx)(i.A,{className:(0,o.cn)(B[b],"text-red-400")}):_?(0,r.jsx)(n.A,{className:(0,o.cn)(B[b],"text-green-400")}):v?(0,r.jsx)("span",{className:(0,o.cn)(B[b],"text-gray-400"),children:v}):null})]}),(y||h||f)&&(0,r.jsxs)("div",{className:"mt-2 flex items-start space-x-1",children:[(y||h)&&(0,r.jsx)("span",{className:"flex-shrink-0 mt-0.5",children:y?(0,r.jsx)(i.A,{className:"h-4 w-4 text-red-400"}):(0,r.jsx)(n.A,{className:"h-4 w-4 text-green-400"})}),f&&!y&&!h&&(0,r.jsx)(c.A,{className:"h-4 w-4 text-gray-400 flex-shrink-0 mt-0.5"}),(0,r.jsx)("p",{className:(0,o.cn)("text-sm",y?"text-red-600":h?"text-green-600":"text-gray-600"),children:y||h||f})]})]})});m.displayName="Input",(0,d.forwardRef)((e,a)=>(0,r.jsx)(m,{ref:a,type:"email",placeholder:"<EMAIL>",...e})).displayName="EmailInput",(0,d.forwardRef)((e,a)=>(0,r.jsx)(m,{ref:a,type:"password",placeholder:"••••••••",...e})).displayName="PasswordInput",(0,d.forwardRef)(({min:e,max:a,step:s=1,...t},l)=>(0,r.jsx)(m,{ref:l,type:"number",min:e,max:a,step:s,...t})).displayName="NumberInput",(0,d.forwardRef)(({onSearch:e,...a},s)=>(0,r.jsx)(m,{ref:s,type:"search",placeholder:"Caută...",onKeyDown:a=>{"Enter"===a.key&&e&&e(a.currentTarget.value)},...a})).displayName="SearchInput",(0,d.forwardRef)((e,a)=>(0,r.jsx)(m,{ref:a,type:"tel",placeholder:"+40 123 456 789",...e})).displayName="PhoneInput",(0,d.forwardRef)((e,a)=>(0,r.jsx)(m,{ref:a,type:"url",placeholder:"https://exemplu.com",...e})).displayName="UrlInput",(0,d.forwardRef)((e,a)=>(0,r.jsx)(m,{ref:a,type:"date",...e})).displayName="DateInput",(0,d.forwardRef)((e,a)=>(0,r.jsx)(m,{ref:a,type:"time",...e})).displayName="TimeInput",(0,d.forwardRef)((e,a)=>(0,r.jsx)(m,{ref:a,type:"datetime-local",...e})).displayName="DateTimeInput";const x=m},6633:(e,a,s)=>{s.r(a),s.d(a,{default:()=>h});var r=s(8835),t=s(4403),l=s(3322),i=s(4175),n=s(3930),c=s(6540),d=s(6103),o=s(125),m=(s(6215),s(9264)),x=s(6821),p=s(2392),u=s(4848);const y=[{id:1,description:"Cumpărături Carrefour",amount:85.5,category:"Mâncare",date:"2024-01-15",paymentMethod:"Card"},{id:2,description:"Benzină",amount:120,category:"Transport",date:"2024-01-14",paymentMethod:"Card"},{id:3,description:"Factură electricitate",amount:75.25,category:"Utilități",date:"2024-01-13",paymentMethod:"Transfer bancar"},{id:4,description:"Cinema",amount:25,category:"Divertisment",date:"2024-01-12",paymentMethod:"Numerar"},{id:5,description:"Prânz restaurant",amount:45.75,category:"Mâncare",date:"2024-01-11",paymentMethod:"Card"}],h=()=>{const[e,a]=(0,c.useState)(""),[s,h]=(0,c.useState)(""),[f,g]=(0,c.useState)(!1),j=(0,x.Rj)(),{data:N,isLoading:b,error:w}=(0,n.I)({queryKey:["expenses",e,s],queryFn:async()=>{await new Promise(e=>setTimeout(e,500));let a=y;return e&&(a=a.filter(a=>a.description.toLowerCase().includes(e.toLowerCase()))),s&&(a=a.filter(e=>e.category===s)),a}}),v=async a=>{try{const r={search:e||void 0,category:""!==s?s:void 0};await j.mutateAsync({format:a,params:r})}catch(e){console.error("Export failed:",e)}};return w?(0,u.jsx)("div",{className:"text-center py-12",children:(0,u.jsx)("p",{className:"text-red-600",children:"Eroare la încărcarea cheltuielilor"})}):(0,u.jsxs)("div",{className:"space-y-6",children:[(0,u.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between",children:[(0,u.jsxs)("div",{children:[(0,u.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Cheltuieli"}),(0,u.jsx)("p",{className:"text-gray-600",children:"Gestionează și urmărește toate cheltuielile tale"})]}),(0,u.jsxs)("div",{className:"mt-4 sm:mt-0 flex flex-col sm:flex-row gap-3",children:[(0,u.jsxs)("div",{className:"flex gap-2",children:[(0,u.jsxs)(d.Ay,{variant:"outline",size:"sm",onClick:()=>v("csv"),disabled:j.isPending,className:"flex items-center",children:[(0,u.jsx)(r.A,{className:"h-4 w-4 mr-1"}),"CSV"]}),(0,u.jsxs)(d.Ay,{variant:"outline",size:"sm",onClick:()=>v("pdf"),disabled:j.isPending,className:"flex items-center",children:[(0,u.jsx)(r.A,{className:"h-4 w-4 mr-1"}),"PDF"]}),(0,u.jsxs)(d.Ay,{variant:"outline",size:"sm",onClick:()=>v("excel"),disabled:j.isPending,className:"flex items-center",children:[(0,u.jsx)(r.A,{className:"h-4 w-4 mr-1"}),"Excel"]})]}),(0,u.jsxs)(d.Ay,{onClick:()=>{console.log("Adaugă cheltuială nouă")},variant:"primary",className:"flex items-center",children:[(0,u.jsx)(t.A,{className:"h-5 w-5 mr-2"}),"Adaugă cheltuială"]})]})]}),(0,u.jsxs)(o.Ay,{className:"p-6",children:[(0,u.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,u.jsx)("div",{className:"flex-1",children:(0,u.jsxs)("div",{className:"relative",children:[(0,u.jsx)(l.A,{className:"h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"}),(0,u.jsx)("input",{type:"text",placeholder:"Caută cheltuieli...",value:e,onChange:e=>a(e.target.value),className:"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"})]})}),(0,u.jsxs)("div",{className:"flex gap-2",children:[(0,u.jsxs)("select",{value:s,onChange:e=>h(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500",children:[(0,u.jsx)("option",{value:"",children:"Toate categoriile"}),["Mâncare","Transport","Utilități","Divertisment","Sănătate","Educație"].map(e=>(0,u.jsx)("option",{value:e,children:e},e))]}),(0,u.jsxs)(d.Ay,{variant:"outline",onClick:()=>g(!f),className:"flex items-center",children:[(0,u.jsx)(i.A,{className:"h-5 w-5 mr-2"}),"Filtre"]})]})]}),f&&(0,u.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,u.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-4",children:[(0,u.jsxs)("div",{children:[(0,u.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Data de la"}),(0,u.jsx)("input",{type:"date",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"})]}),(0,u.jsxs)("div",{children:[(0,u.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Data până la"}),(0,u.jsx)("input",{type:"date",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"})]}),(0,u.jsxs)("div",{children:[(0,u.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Suma minimă"}),(0,u.jsx)("input",{type:"number",placeholder:"0.00",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"})]})]})})]}),(0,u.jsx)(o.Ay,{children:b?(0,u.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,u.jsx)(m.Ay,{size:"lg"})}):N&&N.length>0?(0,u.jsx)("div",{className:"overflow-x-auto",children:(0,u.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,u.jsx)("thead",{className:"bg-gray-50",children:(0,u.jsxs)("tr",{children:[(0,u.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Descriere"}),(0,u.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Categorie"}),(0,u.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Suma"}),(0,u.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Data"}),(0,u.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Metoda de plată"}),(0,u.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Acțiuni"})]})}),(0,u.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:N.map(e=>(0,u.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,u.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,u.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.description})}),(0,u.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,u.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:e.category})}),(0,u.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900",children:(0,p.vv)(e.amount)}),(0,u.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(0,p.Yq)(e.date)}),(0,u.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:e.paymentMethod}),(0,u.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:(0,u.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,u.jsx)("button",{className:"text-primary-600 hover:text-primary-900",children:"Editează"}),(0,u.jsx)("button",{className:"text-red-600 hover:text-red-900",children:"Șterge"})]})})]},e.id))})]})}):(0,u.jsx)("div",{className:"text-center py-12",children:(0,u.jsx)("p",{className:"text-gray-500",children:"Nu au fost găsite cheltuieli"})})})]})}}}]);