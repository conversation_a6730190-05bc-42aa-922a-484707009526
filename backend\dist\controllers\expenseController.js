"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.expenseController = void 0;
const prisma_1 = require("../config/prisma");
const getExpenses = async (req, res) => {
    try {
        const { userId } = req;
        const { page = 1, limit = 20, startDate, endDate, categoryId, paymentMethod, tags, minAmount, maxAmount, search, sortBy = 'date', sortOrder = 'DESC', } = req.query;
        const pageNum = typeof page === 'string' ? parseInt(page) : 1;
        const limitNum = typeof limit === 'string' ? parseInt(limit) : 20;
        const startDateStr = typeof startDate === 'string' ? startDate : undefined;
        const endDateStr = typeof endDate === 'string' ? endDate : undefined;
        const categoryIdStr = typeof categoryId === 'string' ? categoryId : undefined;
        const paymentMethodStr = typeof paymentMethod === 'string' ? paymentMethod : undefined;
        const tagsStr = typeof tags === 'string' ? tags : undefined;
        const minAmountStr = typeof minAmount === 'string' ? minAmount : undefined;
        const maxAmountStr = typeof maxAmount === 'string' ? maxAmount : undefined;
        const searchStr = typeof search === 'string' ? search : undefined;
        const sortByStr = typeof sortBy === 'string' ? sortBy : 'date';
        const sortOrderStr = typeof sortOrder === 'string' ? sortOrder : 'DESC';
        const offset = (pageNum - 1) * limitNum;
        const where = {
            user_id: userId,
        };
        if (startDateStr && endDateStr) {
            where.date = {
                gte: new Date(startDateStr),
                lte: new Date(endDateStr),
            };
        }
        if (categoryIdStr) {
            where.category_id = parseInt(categoryIdStr);
        }
        if (paymentMethodStr) {
            where.payment_method = paymentMethodStr;
        }
        if (minAmountStr !== undefined || maxAmountStr !== undefined) {
            where.amount = {};
            if (minAmountStr !== undefined)
                where.amount.gte = parseFloat(minAmountStr);
            if (maxAmountStr !== undefined)
                where.amount.lte = parseFloat(maxAmountStr);
        }
        if (searchStr) {
            where.OR = [
                { description: { contains: searchStr, mode: 'insensitive' } },
                { notes: { contains: searchStr, mode: 'insensitive' } },
            ];
        }
        if (tagsStr) {
            const tagsArray = tagsStr.split(',');
            where.tags = {
                hasSome: tagsArray,
            };
        }
        const count = await prisma_1.prisma.expense.count({ where });
        const expenses = await prisma_1.prisma.expense.findMany({
            where,
            include: {
                category: {
                    select: {
                        id: true,
                        name: true,
                        color: true,
                        icon: true,
                    },
                },
            },
            orderBy: {
                [sortByStr]: sortOrderStr.toLowerCase(),
            },
            skip: offset,
            take: limitNum,
        });
        const result = { rows: expenses, count };
        res.json({
            success: true,
            data: {
                expenses: result.rows,
                pagination: {
                    current_page: pageNum,
                    per_page: limitNum,
                    total_items: result.count,
                    total_pages: Math.ceil(result.count / limitNum),
                    has_next: pageNum * limitNum < result.count,
                    has_prev: pageNum > 1,
                },
            },
        });
    }
    catch (error) {
        console.error('Get expenses error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error while fetching expenses',
        });
    }
};
const getExpense = async (req, res) => {
    try {
        const { id } = req.params;
        const { userId } = req;
        const expense = await prisma_1.prisma.expense.findFirst({
            where: {
                id: parseInt(id),
                user_id: userId,
            },
            include: {
                category: {
                    select: {
                        id: true,
                        name: true,
                        color: true,
                        icon: true,
                    },
                },
            },
        });
        if (!expense) {
            return res.status(404).json({
                success: false,
                message: 'Expense not found',
            });
        }
        res.json({
            success: true,
            data: {
                expense,
            },
        });
    }
    catch (error) {
        console.error('Get expense error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error while fetching expense',
        });
    }
};
const createExpense = async (req, res) => {
    try {
        const { userId } = req;
        const expenseData = {
            ...req.body,
            user_id: userId,
        };
        const category = await prisma_1.prisma.category.findFirst({
            where: {
                id: expenseData.category_id,
                user_id: userId,
                is_active: true,
            },
        });
        if (!category) {
            return res.status(400).json({
                success: false,
                message: 'Invalid category or category does not belong to you',
            });
        }
        const expense = await prisma_1.prisma.expense.create({
            data: expenseData,
            include: {
                category: {
                    select: {
                        id: true,
                        name: true,
                        color: true,
                        icon: true,
                    },
                },
            },
        });
        const createdExpense = expense;
        res.status(201).json({
            success: true,
            message: 'Expense created successfully',
            data: {
                expense: createdExpense,
            },
        });
    }
    catch (error) {
        console.error('Create expense error:', error);
        if (error.name === 'SequelizeValidationError') {
            const errors = error.errors.map(err => ({
                field: err.path,
                message: err.message,
            }));
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors,
            });
        }
        res.status(500).json({
            success: false,
            message: 'Internal server error while creating expense',
        });
    }
};
const updateExpense = async (req, res) => {
    try {
        const { id } = req.params;
        const { userId } = req;
        const updateData = req.body;
        const expense = await prisma_1.prisma.expense.findFirst({
            where: {
                id: parseInt(id),
                user_id: userId,
            },
        });
        if (!expense) {
            return res.status(404).json({
                success: false,
                message: 'Expense not found',
            });
        }
        if (updateData.category_id) {
            const category = await prisma_1.prisma.category.findFirst({
                where: {
                    id: updateData.category_id,
                    user_id: userId,
                    is_active: true,
                },
            });
            if (!category) {
                return res.status(400).json({
                    success: false,
                    message: 'Invalid category or category does not belong to you',
                });
            }
        }
        const updatedExpense = await prisma_1.prisma.expense.update({
            where: { id: expense.id },
            data: updateData,
            include: {
                category: {
                    select: {
                        id: true,
                        name: true,
                        color: true,
                        icon: true,
                    },
                },
            },
        });
        res.json({
            success: true,
            message: 'Expense updated successfully',
            data: {
                expense: updatedExpense,
            },
        });
    }
    catch (error) {
        console.error('Update expense error:', error);
        if (error.name === 'SequelizeValidationError') {
            const errors = error.errors.map(err => ({
                field: err.path,
                message: err.message,
            }));
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors,
            });
        }
        res.status(500).json({
            success: false,
            message: 'Internal server error while updating expense',
        });
    }
};
const deleteExpense = async (req, res) => {
    try {
        const { id } = req.params;
        const { userId } = req;
        const expense = await prisma_1.prisma.expense.findFirst({
            where: {
                id: parseInt(id),
                user_id: userId,
            },
        });
        if (!expense) {
            return res.status(404).json({
                success: false,
                message: 'Expense not found',
            });
        }
        await prisma_1.prisma.expense.delete({
            where: { id: expense.id },
        });
        res.json({
            success: true,
            message: 'Expense deleted successfully',
        });
    }
    catch (error) {
        console.error('Delete expense error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error while deleting expense',
        });
    }
};
const getExpenseStats = async (req, res) => {
    try {
        const { userId } = req;
        const { period = 'monthly', start_date, end_date } = req.query;
        const periodStr = typeof period === 'string' ? period : 'monthly';
        const startDateStr = typeof start_date === 'string' ? start_date : undefined;
        const endDateStr = typeof end_date === 'string' ? end_date : undefined;
        let startDate, endDate;
        if (startDateStr && endDateStr) {
            startDate = new Date(startDateStr);
            endDate = new Date(endDateStr);
        }
        else {
            const now = new Date();
            switch (periodStr) {
                case 'daily':
                    startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                    endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
                    break;
                case 'weekly':
                    const dayOfWeek = now.getDay();
                    startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - dayOfWeek);
                    endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + (7 - dayOfWeek));
                    break;
                case 'yearly':
                    startDate = new Date(now.getFullYear(), 0, 1);
                    endDate = new Date(now.getFullYear() + 1, 0, 1);
                    break;
                default:
                    startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                    endDate = new Date(now.getFullYear(), now.getMonth() + 1, 1);
            }
        }
        const totalExpensesResult = await prisma_1.prisma.expense.aggregate({
            where: {
                user_id: userId,
                date: {
                    gte: startDate,
                    lte: endDate,
                },
            },
            _sum: {
                amount: true,
            },
            _count: {
                id: true,
            },
        });
        const totalExpenses = totalExpensesResult._sum.amount || 0;
        const expenseCount = totalExpensesResult._count.id;
        const expensesByCategory = await prisma_1.prisma.expense.groupBy({
            by: ['category_id'],
            where: {
                user_id: userId,
                date: {
                    gte: startDate,
                    lte: endDate,
                },
            },
            _sum: {
                amount: true,
            },
            _count: {
                id: true,
            },
        });
        const expensesByPaymentMethod = await prisma_1.prisma.expense.groupBy({
            by: ['payment_method'],
            where: {
                user_id: userId,
                date: {
                    gte: startDate,
                    lte: endDate,
                },
            },
            _sum: {
                amount: true,
            },
            _count: {
                id: true,
            },
            orderBy: {
                _sum: {
                    amount: 'desc',
                },
            },
        });
        const dailyExpenses = await prisma_1.prisma.expense.groupBy({
            by: ['date'],
            where: {
                user_id: userId,
                date: {
                    gte: startDate,
                    lte: endDate,
                },
            },
            _sum: {
                amount: true,
            },
            _count: {
                id: true,
            },
            orderBy: {
                date: 'asc',
            },
        });
        const expensesWithTags = await prisma_1.prisma.expense.findMany({
            where: {
                user_id: userId,
                tags: {
                    not: null,
                },
            },
            select: {
                tags: true,
            },
        });
        const tagCounts = {};
        expensesWithTags.forEach(expense => {
            if (expense.tags && Array.isArray(expense.tags)) {
                expense.tags.forEach(tag => {
                    tagCounts[tag] = (tagCounts[tag] || 0) + 1;
                });
            }
        });
        const popularTags = Object.entries(tagCounts)
            .map(([tag, count]) => ({ tag, count }))
            .sort((a, b) => b.count - a.count)
            .slice(0, 10);
        res.json({
            success: true,
            data: {
                period: {
                    type: periodStr,
                    start_date: startDate,
                    end_date: endDate,
                },
                summary: {
                    total_expenses: parseFloat(totalExpenses),
                    expense_count: expenseCount,
                    average_expense: expenseCount > 0 ? parseFloat(totalExpenses) / expenseCount : 0,
                    daily_average: dailyExpenses.length > 0 ? parseFloat(totalExpenses) / dailyExpenses.length : 0,
                },
                by_category: expensesByCategory,
                by_payment_method: expensesByPaymentMethod,
                daily_expenses: dailyExpenses,
                popular_tags: popularTags,
            },
        });
    }
    catch (error) {
        console.error('Get expense stats error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error while fetching expense statistics',
        });
    }
};
const getMonthlyTrends = async (req, res) => {
    try {
        const { userId } = req;
        const { months = 12 } = req.query;
        const monthsNum = typeof months === 'string' ? parseInt(months) : 12;
        const endDate = new Date();
        const startDate = new Date();
        startDate.setMonth(startDate.getMonth() - monthsNum);
        const trends = await prisma_1.prisma.$queryRaw `
      SELECT 
        DATE_TRUNC('month', date) as month,
        SUM(amount) as total_amount,
        COUNT(*) as expense_count,
        AVG(amount) as average_amount
      FROM "Expense"
      WHERE user_id = ${userId}
        AND date >= ${startDate}
        AND date <= ${endDate}
      GROUP BY DATE_TRUNC('month', date)
      ORDER BY month ASC
    `;
        res.json({
            success: true,
            data: {
                trends,
            },
        });
    }
    catch (error) {
        console.error('Get monthly trends error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error while fetching monthly trends',
        });
    }
};
const addTag = async (req, res) => {
    try {
        const { id } = req.params;
        const { tag } = req.body;
        const { userId } = req;
        if (!tag || typeof tag !== 'string' || tag.trim().length === 0) {
            return res.status(400).json({
                success: false,
                message: 'Tag is required and must be a non-empty string',
            });
        }
        const expense = await prisma_1.prisma.expense.findFirst({
            where: {
                id: parseInt(id),
                user_id: userId,
            },
        });
        if (!expense) {
            return res.status(404).json({
                success: false,
                message: 'Expense not found',
            });
        }
        const currentTags = expense.tags || [];
        const trimmedTag = tag.trim().toLowerCase();
        if (!currentTags.includes(trimmedTag)) {
            const updatedTags = [...currentTags, trimmedTag];
            await prisma_1.prisma.expense.update({
                where: { id: expense.id },
                data: { tags: updatedTags },
            });
        }
        const updatedExpense = await prisma_1.prisma.expense.findUnique({
            where: { id: expense.id },
            select: { tags: true },
        });
        res.json({
            success: true,
            message: 'Tag added successfully',
            data: {
                tags: updatedExpense.tags,
            },
        });
    }
    catch (error) {
        console.error('Add tag error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error while adding tag',
        });
    }
};
const removeTag = async (req, res) => {
    try {
        const { id } = req.params;
        const { tag } = req.body;
        const { userId } = req;
        if (!tag || typeof tag !== 'string') {
            return res.status(400).json({
                success: false,
                message: 'Tag is required and must be a string',
            });
        }
        const expense = await prisma_1.prisma.expense.findFirst({
            where: {
                id: parseInt(id),
                user_id: userId,
            },
        });
        if (!expense) {
            return res.status(404).json({
                success: false,
                message: 'Expense not found',
            });
        }
        const currentTags = expense.tags || [];
        const trimmedTag = tag.trim().toLowerCase();
        const updatedTags = currentTags.filter(t => t !== trimmedTag);
        await prisma_1.prisma.expense.update({
            where: { id: expense.id },
            data: { tags: updatedTags },
        });
        const updatedExpense = await prisma_1.prisma.expense.findUnique({
            where: { id: expense.id },
            select: { tags: true },
        });
        res.json({
            success: true,
            message: 'Tag removed successfully',
            data: {
                tags: updatedExpense.tags,
            },
        });
    }
    catch (error) {
        console.error('Remove tag error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error while removing tag',
        });
    }
};
const getPopularTags = async (req, res) => {
    try {
        const { userId } = req;
        const { limit = 20 } = req.query;
        const limitNum = typeof limit === 'string' ? parseInt(limit) : 20;
        const expensesWithTags = await prisma_1.prisma.expense.findMany({
            where: {
                user_id: userId,
                tags: {
                    not: null,
                },
            },
            select: {
                tags: true,
            },
        });
        const tagCounts = {};
        expensesWithTags.forEach(expense => {
            if (expense.tags && Array.isArray(expense.tags)) {
                expense.tags.forEach(tag => {
                    tagCounts[tag] = (tagCounts[tag] || 0) + 1;
                });
            }
        });
        const tags = Object.entries(tagCounts)
            .map(([tag, count]) => ({ tag, count }))
            .sort((a, b) => b.count - a.count)
            .slice(0, limitNum);
        res.json({
            success: true,
            data: {
                tags,
            },
        });
    }
    catch (error) {
        console.error('Get popular tags error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error while fetching popular tags',
        });
    }
};
const bulkDeleteExpenses = async (req, res) => {
    try {
        const { expense_ids } = req.body;
        const { userId } = req;
        if (!Array.isArray(expense_ids) || expense_ids.length === 0) {
            return res.status(400).json({
                success: false,
                message: 'expense_ids must be a non-empty array',
            });
        }
        const expenseIds = expense_ids.map(id => parseInt(id));
        const expenses = await prisma_1.prisma.expense.findMany({
            where: {
                id: { in: expenseIds },
                user_id: userId,
            },
        });
        if (expenses.length !== expense_ids.length) {
            return res.status(400).json({
                success: false,
                message: 'Some expenses do not exist or do not belong to you',
            });
        }
        const deleteResult = await prisma_1.prisma.expense.deleteMany({
            where: {
                id: { in: expenseIds },
                user_id: userId,
            },
        });
        const deletedCount = deleteResult.count;
        res.json({
            success: true,
            message: `${deletedCount} expenses deleted successfully`,
            data: {
                deleted_count: deletedCount,
            },
        });
    }
    catch (error) {
        console.error('Bulk delete expenses error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error while deleting expenses',
        });
    }
};
exports.expenseController = {
    getExpenses,
    getExpense,
    createExpense,
    updateExpense,
    deleteExpense,
    getExpenseStats,
    getMonthlyTrends,
    addTag,
    removeTag,
    getPopularTags,
    bulkDeleteExpenses,
};
//# sourceMappingURL=expenseController.js.map