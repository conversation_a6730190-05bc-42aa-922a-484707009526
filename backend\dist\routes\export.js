"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = require("../middleware/auth");
const subscriptionLimits_1 = require("../middleware/subscriptionLimits");
const exportController_1 = require("../controllers/exportController");
const router = express_1.default.Router();
router.use(auth_1.authenticateToken);
router.get('/csv', (0, subscriptionLimits_1.checkExportPermission)('csv'), exportController_1.exportController.exportCSV);
router.get('/pdf', (0, subscriptionLimits_1.checkExportPermission)('pdf'), exportController_1.exportController.exportPDF);
router.get('/excel', (0, subscriptionLimits_1.checkExportPermission)('excel'), exportController_1.exportController.exportExcel);
exports.default = router;
//# sourceMappingURL=export.js.map