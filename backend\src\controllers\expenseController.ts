import { prisma } from '../config/prisma';
import { Request, Response } from 'express';
// Note: subscriptionService and usageService imports will be added when those services are created
// import { subscriptionService } from '../services/subscriptionService';
import usageService from '../services/usageService';
// import { logger } from '../utils/logger';

interface AuthenticatedRequest extends Request {
  user?: {
    id: number;
    email: string;
    role?: string;
    subscription_plan?: string;
  };
  userId?: number;
}

// Helper function to get userId safely
const getUserId = (req: AuthenticatedRequest): number => {
  const userId = req.userId || req.user?.id;
  if (!userId) {
    throw new Error('User not authenticated');
  }
  return userId;
};

// Get all expenses for the authenticated user
const getExpenses = async (req: AuthenticatedRequest, res: Response): Promise<Response> => {
  try {
    const userId = getUserId(req);
    const {
      page = 1,
      limit = 20,
      startDate,
      endDate,
      categoryId,
      paymentMethod,
      tags,
      minAmount,
      maxAmount,
      search,
      sortBy = 'date',
      sortOrder = 'DESC',
    } = req.query;

    // Convert query parameters to proper types
    const pageNum = typeof page === 'string' ? parseInt(page) : 1;
    const limitNum = typeof limit === 'string' ? parseInt(limit) : 20;
    const startDateStr = typeof startDate === 'string' ? startDate : undefined;
    const endDateStr = typeof endDate === 'string' ? endDate : undefined;
    const categoryIdStr = typeof categoryId === 'string' ? categoryId : undefined;
    const paymentMethodStr = typeof paymentMethod === 'string' ? paymentMethod : undefined;
    const tagsStr = typeof tags === 'string' ? tags : undefined;
    const minAmountStr = typeof minAmount === 'string' ? minAmount : undefined;
    const maxAmountStr = typeof maxAmount === 'string' ? maxAmount : undefined;
    const searchStr = typeof search === 'string' ? search : undefined;
    const sortByStr = typeof sortBy === 'string' ? sortBy : 'date';
    const sortOrderStr = typeof sortOrder === 'string' ? sortOrder : 'DESC';

    const offset = (pageNum - 1) * limitNum;

    // Build where clause
    const where: any = {
      user_id: userId,
    };

    if (startDateStr && endDateStr) {
      where.date = {
        gte: new Date(startDateStr),
        lte: new Date(endDateStr),
      };
    }

    if (categoryIdStr) {
      where.category_id = parseInt(categoryIdStr);
    }

    if (paymentMethodStr) {
      where.payment_method = paymentMethodStr;
    }

    if (minAmountStr !== undefined || maxAmountStr !== undefined) {
      where.amount = {};
      if (minAmountStr !== undefined) where.amount.gte = parseFloat(minAmountStr);
      if (maxAmountStr !== undefined) where.amount.lte = parseFloat(maxAmountStr);
    }

    if (searchStr) {
      where.OR = [
        { description: { contains: searchStr, mode: 'insensitive' } },
        { notes: { contains: searchStr, mode: 'insensitive' } },
      ];
    }

    if (tagsStr) {
      const tagsArray = tagsStr.split(',');
      where.tags = {
        hasSome: tagsArray,
      };
    }

    // Get total count
    const count = await prisma.expense.count({ where });

    // Get expenses with pagination
    const expenses = await prisma.expense.findMany({
      where,
      include: {
        category: {
          select: {
            id: true,
            name: true,
            color: true,
            icon: true,
          },
        },
      },
      orderBy: {
        [sortByStr]: sortOrderStr.toLowerCase(),
      },
      skip: offset,
      take: limitNum,
    });

    const result = { rows: expenses, count };

    return res.json({
      success: true,
      data: {
        expenses: result.rows,
        pagination: {
          current_page: pageNum,
          per_page: limitNum,
          total_items: result.count,
          total_pages: Math.ceil(result.count / limitNum),
          has_next: pageNum * limitNum < result.count,
          has_prev: pageNum > 1,
        },
      },
    });
  } catch (error) {
    console.error('Get expenses error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error while fetching expenses',
    });
  }
};

// Get a single expense by ID
const getExpense = async (req: AuthenticatedRequest, res: Response): Promise<Response> => {
  try {
    const { id } = req.params;
    const userId = getUserId(req);

    const expense = await prisma.expense.findFirst({
      where: {
        id: parseInt(id!),
        user_id: userId,
      },
      include: {
        category: {
          select: {
            id: true,
            name: true,
            color: true,
            icon: true,
          },
        },
      },
    });

    if (!expense) {
      return res.status(404).json({
        success: false,
        message: 'Expense not found',
      });
    }

    return res.json({
      success: true,
      data: {
        expense,
      },
    });
  } catch (error) {
    console.error('Get expense error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error while fetching expense',
    });
  }
};

// Create a new expense
const createExpense = async (req: AuthenticatedRequest, res: Response): Promise<Response> => {
  try {
    const userId = getUserId(req);

    // TODO: Implement subscription service check
    // Check if user can create expenses based on their subscription
    // const canCreate = await subscriptionService.canUserPerformAction(userId, 'create_expense');
    // if (!canCreate) {
    //   const usageProgress = await usageService.getUsageProgress(userId);
    //   return res.status(403).json({
    //     success: false,
    //     message: 'Monthly expense limit reached. Upgrade your plan to create more expenses.',
    //     code: 'EXPENSE_LIMIT_REACHED',
    //     data: {
    //       usage: usageProgress,
    //     },
    //   });
    // }

    const expenseData = {
      ...req.body,
      user_id: userId,
    };

    // Verify that the category belongs to the user
    const category = await prisma.category.findFirst({
      where: {
        id: expenseData.category_id,
        user_id: userId,
        is_active: true,
      } as any, // Type assertion to handle Prisma strict types
    });

    if (!category) {
      return res.status(400).json({
        success: false,
        message: 'Invalid category or category does not belong to you',
      });
    }

    const expense = await prisma.expense.create({
      data: expenseData,
      include: {
        category: {
          select: {
            id: true,
            name: true,
            color: true,
            icon: true,
          },
        },
      },
    });

    // Increment usage counter
    // await usageService.incrementExpenseCount(userId);

    const createdExpense = expense;

    return res.status(201).json({
      success: true,
      message: 'Expense created successfully',
      data: {
        expense: createdExpense,
      },
    });
  } catch (error: any) {
    console.error('Create expense error:', error);

    if (error.code === 'P2002') {
      return res.status(400).json({
        success: false,
        message: 'Validation failed - duplicate entry',
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Internal server error while creating expense',
    });
  }
};

// Update an expense
const updateExpense = async (req: AuthenticatedRequest, res: Response): Promise<Response> => {
  try {
    const { id } = req.params;
    const userId = getUserId(req);
    const updateData = req.body;

    const expense = await prisma.expense.findFirst({
      where: {
        id: parseInt(id!),
        user_id: userId,
      },
    });

    if (!expense) {
      return res.status(404).json({
        success: false,
        message: 'Expense not found',
      });
    }

    // If category is being updated, verify it belongs to the user
    if (updateData.category_id) {
      const category = await prisma.category.findFirst({
        where: {
          id: updateData.category_id,
          user_id: userId,
          is_active: true,
        },
      });

      if (!category) {
        return res.status(400).json({
          success: false,
          message: 'Invalid category or category does not belong to you',
        });
      }
    }

    const updatedExpense = await prisma.expense.update({
      where: { id: expense.id },
      data: updateData,
      include: {
        category: {
          select: {
            id: true,
            name: true,
            color: true,
            icon: true,
          },
        },
      },
    });

    return res.json({
      success: true,
      message: 'Expense updated successfully',
      data: {
        expense: updatedExpense,
      },
    });
  } catch (error: any) {
    console.error('Update expense error:', error);

    if (error.code === 'P2002') {
      return res.status(400).json({
        success: false,
        message: 'Validation failed - duplicate entry',
      });
    }

    return res.status(500).json({
      success: false,
      message: 'Internal server error while updating expense',
    });
  }
};

// Delete an expense
const deleteExpense = async (req: AuthenticatedRequest, res: Response): Promise<Response> => {
  try {
    const { id } = req.params;
    const userId = getUserId(req);

    const expense = await prisma.expense.findFirst({
      where: {
        id: parseInt(id!),
        user_id: userId,
      },
    });

    if (!expense) {
      return res.status(404).json({
        success: false,
        message: 'Expense not found',
      });
    }

    await prisma.expense.delete({
      where: { id: expense.id },
    });

    // Decrement usage counter
    // await usageService.decrementExpenseCount(userId);

    return res.json({
      success: true,
      message: 'Expense deleted successfully',
    });
  } catch (error) {
    console.error('Delete expense error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error while deleting expense',
    });
  }
};

// Get expense statistics
const getExpenseStats = async (req: AuthenticatedRequest, res: Response): Promise<Response> => {
  try {
    const userId = getUserId(req);
    const { period = 'monthly', start_date, end_date } = req.query;

    // Convert query parameters to proper types
    const periodStr = typeof period === 'string' ? period : 'monthly';
    const startDateStr = typeof start_date === 'string' ? start_date : undefined;
    const endDateStr = typeof end_date === 'string' ? end_date : undefined;

    let startDate, endDate;

    if (startDateStr && endDateStr) {
      startDate = new Date(startDateStr);
      endDate = new Date(endDateStr);
    } else {
      const now = new Date();
      switch (periodStr) {
        case 'daily':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
          endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
          break;
        case 'weekly':
          const dayOfWeek = now.getDay();
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - dayOfWeek);
          endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + (7 - dayOfWeek));
          break;
        case 'yearly':
          startDate = new Date(now.getFullYear(), 0, 1);
          endDate = new Date(now.getFullYear() + 1, 0, 1);
          break;
        default: // monthly
          startDate = new Date(now.getFullYear(), now.getMonth(), 1);
          endDate = new Date(now.getFullYear(), now.getMonth() + 1, 1);
      }
    }

    // Get total expenses and count
    const totalExpensesResult = await prisma.expense.aggregate({
      where: {
        user_id: userId,
        date: {
          gte: startDate,
          lte: endDate,
        },
      } as any,
      _sum: {
        amount: true,
      },
      _count: {
        id: true,
      },
    });

    const totalExpenses = totalExpensesResult._sum?.amount || 0;
    const expenseCount = totalExpensesResult._count?.id || 0;

    // Get expenses by category
    const expensesByCategory = await prisma.expense.groupBy({
      by: ['category_id'],
      where: {
        user_id: userId,
        date: {
          gte: startDate,
          lte: endDate,
        },
      } as any,
      _sum: {
        amount: true,
      },
      _count: {
        id: true,
      },
    });

    // Get expenses by payment method
    const expensesByPaymentMethod = await prisma.expense.groupBy({
      by: ['payment_method'],
      where: {
        user_id: userId,
        date: {
          gte: startDate,
          lte: endDate,
        },
      },
      _sum: {
        amount: true,
      },
      _count: {
        id: true,
      },
      orderBy: {
        _sum: {
          amount: 'desc',
        },
      },
    });

    // Get daily expenses for the period
    const dailyExpenses = await prisma.expense.groupBy({
      by: ['date'],
      where: {
        user_id: userId,
        date: {
          gte: startDate,
          lte: endDate,
        },
      } as any,
      _sum: {
        amount: true,
      },
      _count: {
        id: true,
      },
      orderBy: {
        date: 'asc',
      },
    });

    // Get popular tags - simplified version
    const expensesWithTags = await prisma.expense.findMany({
      where: {
        user_id: userId,
        tags: {
          not: null as any,
        },
      } as any,
      select: {
        tags: true,
      },
    });

    // Process tags to get popular ones
    const tagCounts: Record<string, number> = {};
    expensesWithTags.forEach(expense => {
      if (expense.tags && Array.isArray(expense.tags)) {
        (expense.tags as string[]).forEach((tag: string) => {
          if (typeof tag === 'string') {
            tagCounts[tag] = (tagCounts[tag] || 0) + 1;
          }
        });
      }
    });

    const popularTags = Object.entries(tagCounts)
      .map(([tag, count]) => ({ tag, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    return res.json({
      success: true,
      data: {
        period: {
          type: periodStr,
          start_date: startDate,
          end_date: endDate,
        },
        summary: {
          total_expenses: parseFloat(totalExpenses.toString()),
          expense_count: expenseCount,
          average_expense: expenseCount > 0 ? parseFloat(totalExpenses.toString()) / expenseCount : 0,
          daily_average: dailyExpenses.length > 0 ? parseFloat(totalExpenses.toString()) / dailyExpenses.length : 0,
        },
        by_category: expensesByCategory,
        by_payment_method: expensesByPaymentMethod,
        daily_expenses: dailyExpenses,
        popular_tags: popularTags,
      },
    });
  } catch (error) {
    console.error('Get expense stats error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error while fetching expense statistics',
    });
  }
};

// Get monthly trends
const getMonthlyTrends = async (req: AuthenticatedRequest, res: Response): Promise<Response> => {
  try {
    const userId = getUserId(req);
    const { months = 12 } = req.query;

    // Convert query parameters to proper types
    const monthsNum = typeof months === 'string' ? parseInt(months) : 12;

    // Get monthly trends
    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - monthsNum);

    const trends = await prisma.$queryRaw`
      SELECT 
        DATE_TRUNC('month', date) as month,
        SUM(amount) as total_amount,
        COUNT(*) as expense_count,
        AVG(amount) as average_amount
      FROM "Expense"
      WHERE user_id = ${userId}
        AND date >= ${startDate}
        AND date <= ${endDate}
      GROUP BY DATE_TRUNC('month', date)
      ORDER BY month ASC
    `;

    return res.json({
      success: true,
      data: {
        trends,
      },
    });
  } catch (error) {
    console.error('Get monthly trends error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error while fetching monthly trends',
    });
  }
};

// Add tag to expense
const addTag = async (req: AuthenticatedRequest, res: Response): Promise<Response> => {
  try {
    const { id } = req.params;
    const { tag } = req.body;
    const userId = getUserId(req);

    if (!tag || typeof tag !== 'string' || tag.trim().length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Tag is required and must be a non-empty string',
      });
    }

    const expense = await prisma.expense.findFirst({
      where: {
        id: parseInt(id!),
        user_id: userId,
      },
    });

    if (!expense) {
      return res.status(404).json({
        success: false,
        message: 'Expense not found',
      });
    }

    // Add tag to expense
    const currentTags = Array.isArray(expense.tags) ? expense.tags : [];
    const trimmedTag = tag.trim().toLowerCase();

    const tagExists = currentTags.some((t: any) => typeof t === 'string' && t === trimmedTag);
    if (!tagExists) {
      const updatedTags = [...currentTags, trimmedTag];
      await prisma.expense.update({
        where: { id: expense.id },
        data: { tags: updatedTags },
      });
    }

    // Get updated expense
    const updatedExpense = await prisma.expense.findUnique({
      where: { id: expense.id },
      select: { tags: true },
    });

    return res.json({
      success: true,
      message: 'Tag added successfully',
      data: {
        tags: updatedExpense?.tags || [],
      },
    });
  } catch (error) {
    console.error('Add tag error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error while adding tag',
    });
  }
};

// Remove tag from expense
const removeTag = async (req: AuthenticatedRequest, res: Response): Promise<Response> => {
  try {
    const { id } = req.params;
    const { tag } = req.body;
    const userId = getUserId(req);

    if (!tag || typeof tag !== 'string') {
      return res.status(400).json({
        success: false,
        message: 'Tag is required and must be a string',
      });
    }

    if (!id) {
      return res.status(400).json({
        success: false,
        message: 'Expense ID is required',
      });
    }

    const expense = await prisma.expense.findFirst({
      where: {
        id: parseInt(id!),
        user_id: userId,
      },
    });

    if (!expense) {
      return res.status(404).json({
        success: false,
        message: 'Expense not found',
      });
    }

    // Remove tag from expense
    const currentTags = Array.isArray(expense.tags) ? expense.tags : [];
    const trimmedTag = tag.trim().toLowerCase();
    const updatedTags = currentTags.filter((t: any) => typeof t === 'string' && t !== trimmedTag);

    await prisma.expense.update({
      where: { id: expense.id },
      data: { tags: updatedTags },
    });

    // Get updated expense
    const updatedExpense = await prisma.expense.findUnique({
      where: { id: expense.id },
      select: { tags: true },
    });

    return res.json({
      success: true,
      message: 'Tag removed successfully',
      data: {
        tags: updatedExpense?.tags || [],
      },
    });
  } catch (error) {
    console.error('Remove tag error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error while removing tag',
    });
  }
};

// Get popular tags
const getPopularTags = async (req: AuthenticatedRequest, res: Response): Promise<Response> => {
  try {
    const userId = getUserId(req);
    const { limit = 20 } = req.query;

    // Convert query parameters to proper types
    const limitNum = typeof limit === 'string' ? parseInt(limit) : 20;

    // Get popular tags
    const expensesWithTags = await prisma.expense.findMany({
      where: {
        user_id: userId,
        tags: {
          not: null,
        },
      },
      select: {
        tags: true,
      },
    });

    // Process tags to get popular ones
    const tagCounts: Record<string, number> = {};
    expensesWithTags.forEach(expense => {
      if (expense.tags && Array.isArray(expense.tags)) {
        expense.tags.forEach((tag: string) => {
          tagCounts[tag] = (tagCounts[tag] || 0) + 1;
        });
      }
    });

    const tags = Object.entries(tagCounts)
      .map(([tag, count]) => ({ tag, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limitNum);

    return res.json({
      success: true,
      data: {
        tags,
      },
    });
  } catch (error) {
    console.error('Get popular tags error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error while fetching popular tags',
    });
  }
};

// Bulk delete expenses
const bulkDeleteExpenses = async (req: AuthenticatedRequest, res: Response): Promise<Response> => {
  try {
    const { expense_ids } = req.body;
    const userId = getUserId(req);

    if (!Array.isArray(expense_ids) || expense_ids.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'expense_ids must be a non-empty array',
      });
    }

    // Convert expense_ids to integers
    const expenseIds = expense_ids.map(id => parseInt(id));

    // Verify all expenses belong to the user
    const expenses = await prisma.expense.findMany({
      where: {
        id: { in: expenseIds },
        user_id: userId!,
      },
    });

    if (expenses.length !== expense_ids.length) {
      return res.status(400).json({
        success: false,
        message: 'Some expenses do not exist or do not belong to you',
      });
    }

    // Delete expenses
    const deleteResult = await prisma.expense.deleteMany({
      where: {
        id: { in: expenseIds },
        user_id: userId!,
      },
    });

    const deletedCount = deleteResult.count;

    return res.json({
      success: true,
      message: `${deletedCount} expenses deleted successfully`,
      data: {
        deleted_count: deletedCount,
      },
    });
  } catch (error) {
    console.error('Bulk delete expenses error:', error);
    return res.status(500).json({
      success: false,
      message: 'Internal server error while deleting expenses',
    });
  }
};

export const expenseController = {
  getExpenses,
  getExpense,
  createExpense,
  updateExpense,
  deleteExpense,
  getExpenseStats,
  getMonthlyTrends,
  addTag,
  removeTag,
  getPopularTags,
  bulkDeleteExpenses,
};
