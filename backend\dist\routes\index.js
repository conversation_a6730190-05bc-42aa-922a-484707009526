"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const router = express_1.default.Router();
const auth_1 = __importDefault(require("./auth"));
const categories_1 = __importDefault(require("./categories"));
const expenses_1 = __importDefault(require("./expenses"));
const usage_1 = __importDefault(require("./usage"));
const subscription_1 = __importDefault(require("./subscription"));
const webhooks_1 = __importDefault(require("./webhooks"));
const export_1 = __importDefault(require("./export"));
router.get('/health', (req, res) => {
    res.json({
        success: true,
        message: 'API is running',
        timestamp: new Date().toISOString(),
        version: process.env.API_VERSION || '1.0.0',
    });
});
router.get('/docs', (req, res) => {
    res.json({
        success: true,
        message: 'Expense Tracker API Documentation',
        version: process.env.API_VERSION || '1.0.0',
        endpoints: {
            auth: {
                'POST /api/auth/register': 'Register a new user',
                'POST /api/auth/login': 'Login user',
                'POST /api/auth/refresh': 'Refresh access token',
                'POST /api/auth/logout': 'Logout user',
                'GET /api/auth/profile': 'Get user profile',
                'PUT /api/auth/profile': 'Update user profile',
                'POST /api/auth/change-password': 'Change user password',
                'POST /api/auth/forgot-password': 'Request password reset',
                'POST /api/auth/reset-password': 'Reset password with token',
                'POST /api/auth/verify-email': 'Verify email address',
                'POST /api/auth/resend-verification': 'Resend email verification',
            },
            categories: {
                'GET /api/categories': 'Get all categories',
                'GET /api/categories/stats': 'Get categories with statistics',
                'GET /api/categories/:id': 'Get single category',
                'GET /api/categories/:id/stats': 'Get category statistics',
                'POST /api/categories': 'Create new category',
                'PUT /api/categories/:id': 'Update category',
                'DELETE /api/categories/:id': 'Delete category',
                'POST /api/categories/reorder': 'Reorder categories',
                'POST /api/categories/:id/set-default': 'Set default category',
            },
            expenses: {
                'GET /api/expenses': 'Get all expenses with filtering',
                'GET /api/expenses/stats': 'Get expense statistics',
                'GET /api/expenses/trends': 'Get monthly trends',
                'GET /api/expenses/tags': 'Get popular tags',
                'GET /api/expenses/:id': 'Get single expense',
                'POST /api/expenses': 'Create new expense',
                'PUT /api/expenses/:id': 'Update expense',
                'DELETE /api/expenses/:id': 'Delete expense',
                'POST /api/expenses/:id/tags': 'Add tag to expense',
                'DELETE /api/expenses/:id/tags': 'Remove tag from expense',
                'DELETE /api/expenses/bulk': 'Bulk delete expenses',
            },
            usage: {
                'GET /api/usage/current': 'Get current usage information',
                'GET /api/usage/stats': 'Get detailed usage statistics',
                'POST /api/usage/check-action': 'Check if user can perform action',
                'GET /api/usage/upgrade-recommendations': 'Get upgrade recommendations',
            },
            subscriptions: {
                'GET /api/subscriptions/plans': 'Get available subscription plans',
                'GET /api/subscriptions/current': 'Get current subscription',
                'POST /api/subscriptions/checkout': 'Create checkout session',
                'POST /api/subscriptions/portal': 'Create customer portal',
                'POST /api/subscriptions/cancel': 'Cancel subscription',
                'POST /api/subscriptions/reactivate': 'Reactivate subscription',
                'GET /api/subscriptions/checkout/:sessionId': 'Check checkout session status',
                'GET /api/subscriptions/usage': 'Get usage statistics',
                'GET /api/subscriptions/permission/:action': 'Check action permission',
            },
            webhooks: {
                'POST /api/webhooks/stripe': 'Stripe webhook endpoint',
                'GET /api/webhooks/health': 'Webhook health check',
            },
            export: {
                'GET /api/export/csv': 'Export expenses to CSV (Basic/Premium)',
                'GET /api/export/pdf': 'Export expenses to PDF (Premium)',
                'GET /api/export/excel': 'Export expenses to Excel (Premium)',
            },
        },
        authentication: {
            type: 'Bearer Token',
            header: 'Authorization: Bearer <token>',
            note: 'Most endpoints require authentication except registration, login, and password reset',
        },
        response_format: {
            success: {
                success: true,
                message: 'Success message',
                data: 'Response data',
            },
            error: {
                success: false,
                message: 'Error message',
                errors: 'Validation errors (optional)',
            },
        },
    });
});
router.use('/auth', auth_1.default);
router.use('/categories', categories_1.default);
router.use('/expenses', expenses_1.default);
router.use('/usage', usage_1.default);
router.use('/subscriptions', subscription_1.default);
router.use('/webhooks', webhooks_1.default);
router.use('/export', export_1.default);
router.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        message: `API endpoint ${req.originalUrl} not found`,
        available_endpoints: [
            'GET /api/health',
            'GET /api/docs',
            'POST /api/auth/register',
            'POST /api/auth/login',
            'GET /api/categories',
            'GET /api/expenses',
        ],
    });
});
exports.default = router;
//# sourceMappingURL=index.js.map