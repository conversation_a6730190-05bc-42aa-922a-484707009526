import { prisma } from '../../config/prisma';
export { prisma, };
export declare const User: import(".prisma/client").Prisma.UserDelegate<import("@prisma/client/runtime/library").DefaultArgs>;
export declare const Category: import(".prisma/client").Prisma.CategoryDelegate<import("@prisma/client/runtime/library").DefaultArgs>;
export declare const Expense: import(".prisma/client").Prisma.ExpenseDelegate<import("@prisma/client/runtime/library").DefaultArgs>;
//# sourceMappingURL=index.d.ts.map