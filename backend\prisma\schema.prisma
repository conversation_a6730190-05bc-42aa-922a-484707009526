generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum UserRole {
  user
  admin
}

enum SubscriptionStatus {
  active
  canceled
  incomplete
  incomplete_expired
  past_due
  trialing
  unpaid
}

enum PlanType {
  free
  basic
  premium
}

model User {
  id                        Int       @id @default(autoincrement())
  email                     String    @unique
  password                  String
  name                      String
  avatar                    String?
  role                      UserRole  @default(user)
  currency                  String    @default("USD") @db.VarChar(3)
  timezone                  String    @default("UTC")
  is_active                 Boolean   @default(true)
  email_verified            <PERSON>olean   @default(false)
  email_verification_token  String?
  password_reset_token      String?
  password_reset_expires    DateTime?
  refresh_token             String?
  last_login                DateTime?
  login_count               Int       @default(0)
  preferences               Json?     @default("{\"theme\": \"light\", \"notifications\": {\"email\": true, \"push\": false, \"weekly_summary\": true, \"monthly_report\": true}, \"dashboard\": {\"default_period\": \"month\", \"show_categories\": true, \"show_trends\": true}}")
  created_at                DateTime  @default(now())
  updated_at                DateTime  @updatedAt

  // Stripe fields
  stripe_customer_id String? @unique
  subscription_id    String? @unique
  plan_type         PlanType @default(free)
  subscription_status SubscriptionStatus?
  subscription_current_period_start DateTime?
  subscription_current_period_end   DateTime?
  trial_ends_at     DateTime?
  
  // Usage tracking
  monthly_expense_count Int @default(0)
  monthly_expense_limit Int @default(50) // Free plan limit
  last_usage_reset     DateTime @default(now())

  // Relations
  categories   Category[]
  expenses     Expense[]
  subscription Subscription?
  usage_logs   UsageLog[]

  @@map("users")
  @@index([is_active])
  @@index([email_verified])
}

enum BudgetPeriod {
  daily
  weekly
  monthly
  yearly
}

model Category {
  id            Int          @id @default(autoincrement())
  name          String
  description   String?
  color         String       @default("#3B82F6") @db.VarChar(7)
  icon          String       @default("shopping-bag")
  budget_limit  Decimal?     @db.Decimal(10, 2)
  budget_period BudgetPeriod @default(monthly)
  is_active     Boolean      @default(true)
  is_default    Boolean      @default(false)
  sort_order    Int          @default(0)
  user_id       Int
  created_at    DateTime     @default(now())
  updated_at    DateTime     @updatedAt

  // Relations
  user     User      @relation(fields: [user_id], references: [id], onDelete: Cascade)
  expenses Expense[]

  @@map("categories")
  @@unique([user_id, name])
  @@index([user_id])
  @@index([is_active])
  @@index([sort_order])
}

enum PaymentMethod {
  cash
  card
  bank_transfer
  digital_wallet
  check
  other
}

enum RecurringFrequency {
  daily
  weekly
  monthly
  yearly
}

model Expense {
  id                   Int                  @id @default(autoincrement())
  amount               Decimal              @db.Decimal(10, 2)
  description          String
  date                 DateTime             @db.Date
  notes                String?
  payment_method       PaymentMethod        @default(card)
  location             String?
  receipt_url          String?
  tags                 Json?                @default("[]")
  is_recurring         Boolean              @default(false)
  recurring_frequency  RecurringFrequency?
  recurring_end_date   DateTime?            @db.Date
  original_expense_id  Int?
  user_id              Int
  category_id          Int
  created_at           DateTime             @default(now())
  updated_at           DateTime             @updatedAt

  // Relations
  user             User      @relation(fields: [user_id], references: [id], onDelete: Cascade)
  category         Category  @relation(fields: [category_id], references: [id], onDelete: Restrict)
  original_expense Expense?  @relation("ExpenseRecurring", fields: [original_expense_id], references: [id], onDelete: SetNull)
  recurring_expenses Expense[] @relation("ExpenseRecurring")

  @@map("expenses")
  @@index([user_id])
  @@index([category_id])
  @@index([date])
  @@index([user_id, date])
  @@index([user_id, category_id])
  @@index([payment_method])
  @@index([is_recurring])
  @@index([amount])
}

model Plan {
  id          Int      @id @default(autoincrement())
  stripe_id   String   @unique
  name        String
  description String?
  price       Decimal  @db.Decimal(10, 2)
  currency    String   @default("USD") @db.VarChar(3)
  interval    String   // month, year
  features    Json     @default("{}")
  limits      Json     @default("{}")
  is_active   Boolean  @default(true)
  sort_order  Int      @default(0)
  created_at  DateTime @default(now())
  updated_at  DateTime @updatedAt

  // Relations
  subscriptions Subscription[]

  @@map("plans")
  @@index([is_active])
  @@index([sort_order])
}

model Subscription {
  id                    Int                @id @default(autoincrement())
  stripe_id             String             @unique
  user_id               Int                @unique
  plan_id               Int
  status                SubscriptionStatus
  current_period_start  DateTime
  current_period_end    DateTime
  trial_start           DateTime?
  trial_end             DateTime?
  canceled_at           DateTime?
  ended_at              DateTime?
  metadata              Json?              @default("{}")
  created_at            DateTime           @default(now())
  updated_at            DateTime           @updatedAt

  // Relations
  user User @relation(fields: [user_id], references: [id], onDelete: Cascade)
  plan Plan @relation(fields: [plan_id], references: [id], onDelete: Restrict)

  @@map("subscriptions")
  @@index([user_id])
  @@index([plan_id])
  @@index([status])
  @@index([current_period_end])
}

model UsageLog {
  id         Int      @id @default(autoincrement())
  user_id    Int
  action     String   // expense_created, expense_updated, etc.
  resource   String   // expense, category, etc.
  resource_id Int?
  metadata   Json?    @default("{}")
  created_at DateTime @default(now())

  // Relations
  user User @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@map("usage_logs")
  @@index([user_id])
  @@index([action])
  @@index([created_at])
  @@index([user_id, created_at])
}

model WebhookEvent {
  id           Int      @id @default(autoincrement())
  stripe_id    String   @unique
  type         String
  data         Json
  processed    Boolean  @default(false)
  processed_at DateTime?
  error        String?
  retry_count  Int      @default(0)
  created_at   DateTime @default(now())
  updated_at   DateTime @updatedAt

  @@map("webhook_events")
  @@index([type])
  @@index([processed])
  @@index([created_at])
  @@index([stripe_id])
}