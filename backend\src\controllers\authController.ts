import crypto from 'crypto';
import bcryptjs from 'bcryptjs';
import { Request, Response } from 'express';
import { prisma } from '../config/prisma';
import { generateToken, generateRefreshToken, verifyRefreshToken } from '../middleware/auth';

interface AuthenticatedRequest extends Request {
  user?: any;
}

// Register a new user
const register = async (req: Request, res: Response): Promise<void> => {
  try {
    const { email, password, name, currency = 'USD' } = req.body;

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: email.toLowerCase().trim() },
    });
    if (existingUser) {
      res.status(409).json({
        success: false,
        message: 'User with this email already exists',
      });
      return;
    }

    // Hash password
    const hashedPassword = await bcryptjs.hash(password, 12);

    // Create new user
    const user = await prisma.user.create({
      data: {
        email: email.toLowerCase().trim(),
        password: hashedPassword,
        name: name.trim(),
        currency: currency.toUpperCase(),
      },
    });

    // Create default categories for the user
    const defaultCategories = [
      {
        name: 'Alimentație',
        description: 'Cheltuieli pentru mâncare și băuturi',
        color: '#FF6B6B',
        icon: 'utensils',
        is_default: true,
        sort_order: 1,
        user_id: user.id,
      },
      {
        name: 'Transport',
        description: 'Cheltuieli pentru transport',
        color: '#4ECDC4',
        icon: 'car',
        sort_order: 2,
        user_id: user.id,
      },
      {
        name: 'Utilități',
        description: 'Facturi și utilități',
        color: '#45B7D1',
        icon: 'home',
        sort_order: 3,
        user_id: user.id,
      },
    ];

    await prisma.category.createMany({
      data: defaultCategories,
    });

    // Generate tokens
    const accessToken = generateToken(user.id);
    const refreshToken = generateRefreshToken(user.id);

    // Save refresh token to database
    await prisma.user.update({
      where: { id: user.id },
      data: { refresh_token: refreshToken },
    });

    // Remove password from response
    const { password: _, ...userResponse } = user;

    res.status(201).json({
      success: true,
      message: 'User registered successfully',
      data: {
        user: userResponse,
        tokens: {
          accessToken,
          refreshToken,
        },
      },
    });
  } catch (error: any) {
    console.error('Registration error:', error);

    if (error.name === 'SequelizeValidationError') {
      const errors = error.errors.map((err: any) => ({
        field: err.path,
        message: err.message,
      }));

      res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors,
      });
      return;
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error during registration',
    });
  }
};

// Login user
const login = async (req: Request, res: Response): Promise<void> => {
  try {
    const { email, password } = req.body;

    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email: email.toLowerCase().trim() },
    });
    if (!user) {
      res.status(401).json({
        success: false,
        message: 'Invalid email or password',
      });
      return;
    }

    // Check if user is active
    if (!user.is_active) {
      res.status(401).json({
        success: false,
        message: 'Account is deactivated. Please contact support.',
      });
      return;
    }

    // Verify password
    const isPasswordValid = await bcryptjs.compare(password, user.password);
    if (!isPasswordValid) {
      res.status(401).json({
        success: false,
        message: 'Invalid email or password',
      });
      return;
    }

    // Generate tokens
    const accessToken = generateToken(user.id);
    const refreshToken = generateRefreshToken(user.id);

    // Update user login details and save refresh token
    await prisma.user.update({
      where: { id: user.id },
      data: {
        last_login: new Date(),
        login_count: { increment: 1 },
        refresh_token: refreshToken,
      },
    });

    // Remove sensitive data from response
    const { password: _, password_reset_token, password_reset_expires, email_verification_token, ...userResponse } = user;

    // Format subscription data for frontend compatibility
    const formattedUser = {
      ...userResponse,
      subscription: userResponse.plan_type ? {
        plan: {
          name: userResponse.plan_type,
        },
        status: userResponse.subscription_status || 'free',
        currentPeriodEnd: userResponse.subscription_current_period_end,
      } : null,
    };

    res.json({
      success: true,
      message: 'Login successful',
      data: {
        user: formattedUser,
        tokens: {
          accessToken,
          refreshToken,
        },
      },
    });
  } catch (error: any) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error during login',
    });
  }
};

// Refresh access token
const refreshToken = async (req: Request, res: Response): Promise<void> => {
  try {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      res.status(401).json({
        success: false,
        message: 'Refresh token is required',
      });
      return;
    }

    // Verify refresh token
    const decoded = verifyRefreshToken(refreshToken);

    // Find user and verify refresh token
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
    });
    if (!user || user.refresh_token !== refreshToken) {
      res.status(401).json({
        success: false,
        message: 'Invalid refresh token',
      });
      return;
    }

    // Check if user is still active
    if (!user.is_active) {
      res.status(401).json({
        success: false,
        message: 'Account is deactivated',
      });
      return;
    }

    // Generate new tokens
    const newAccessToken = generateToken(user.id);
    const newRefreshToken = generateRefreshToken(user.id);

    // Update refresh token in database
    await prisma.user.update({
      where: { id: user.id },
      data: { refresh_token: newRefreshToken },
    });

    res.json({
      success: true,
      message: 'Token refreshed successfully',
      data: {
        tokens: {
          accessToken: newAccessToken,
          refreshToken: newRefreshToken,
        },
      },
    });
  } catch (error: any) {
    console.error('Token refresh error:', error);

    if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
      res.status(401).json({
        success: false,
        message: 'Invalid or expired refresh token',
      });
      return;
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error during token refresh',
    });
  }
};

// Logout user
const logout = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const {user} = req;

    // Clear refresh token
    await prisma.user.update({
      where: { id: (req as any).user.id },
      data: { refresh_token: null },
    });

    res.json({
      success: true,
      message: 'Logout successful',
    });
  } catch (error: any) {
    console.error('Logout error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error during logout',
    });
  }
};

// Get current user profile
const getProfile = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const {user} = req;

    // Remove sensitive data
    const { password: _, refresh_token, password_reset_token, password_reset_expires, email_verification_token, ...userResponse } = req.user;

    // Format subscription data for frontend compatibility
    const formattedUser = {
      ...userResponse,
      subscription: userResponse.plan_type ? {
        plan: {
          name: userResponse.plan_type,
        },
        status: userResponse.subscription_status || 'free',
        currentPeriodEnd: userResponse.subscription_current_period_end,
      } : null,
    };

    res.json({
      success: true,
      data: {
        user: formattedUser,
      },
    });
  } catch (error: any) {
    console.error('Get profile error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while fetching profile',
    });
  }
};

// Update user profile
const updateProfile = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const {user} = req;
    const { name, currency, timezone } = req.body;

    const updateData: any = {};
    if (name !== undefined) updateData.name = name.trim();
    if (currency !== undefined) updateData.currency = currency.toUpperCase();
    if (timezone !== undefined) updateData.timezone = timezone;

    const updatedUser = await prisma.user.update({
      where: { id: (req as any).user.id },
      data: updateData,
    });

    // Remove sensitive data from response
    const { password: _, refresh_token, password_reset_token, password_reset_expires, email_verification_token, ...userResponse } = updatedUser;

    // Format subscription data for frontend compatibility
    const formattedUser = {
      ...userResponse,
      subscription: userResponse.plan_type ? {
        plan: {
          name: userResponse.plan_type,
        },
        status: userResponse.subscription_status || 'free',
        currentPeriodEnd: userResponse.subscription_current_period_end,
      } : null,
    };

    res.json({
      success: true,
      message: 'Profile updated successfully',
      data: {
        user: formattedUser,
      },
    });
  } catch (error: any) {
    console.error('Update profile error:', error);

    if (error.name === 'SequelizeValidationError') {
      const errors = error.errors.map((err: any) => ({
        field: err.path,
        message: err.message,
      }));

      res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors,
      });
      return;
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error while updating profile',
    });
  }
};

// Change password
const changePassword = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const {user} = req;
    const { currentPassword, newPassword } = req.body;

    // Verify current password
    const isCurrentPasswordValid = await bcryptjs.compare(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      res.status(400).json({
        success: false,
        message: 'Current password is incorrect',
      });
      return;
    }

    // Check if new password is different from current
    const isSamePassword = await bcryptjs.compare(newPassword, user.password);
    if (isSamePassword) {
      res.status(400).json({
        success: false,
        message: 'New password must be different from current password',
      });
      return;
    }

    // Hash new password and update user
    const hashedNewPassword = await bcryptjs.hash(newPassword, 12);

    await prisma.user.update({
      where: { id: user.id },
      data: {
        password: hashedNewPassword,
        refresh_token: null, // Clear all sessions
      },
    });

    res.json({
      success: true,
      message: 'Password changed successfully. Please login again.',
    });
  } catch (error: any) {
    console.error('Change password error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while changing password',
    });
  }
};

// Forgot password
const forgotPassword = async (req: Request, res: Response): Promise<void> => {
  try {
    const { email } = req.body;

    const user = await prisma.user.findUnique({
      where: { email: email.toLowerCase().trim() },
    });
    if (!user) {
      // Don't reveal if email exists or not
      res.json({
        success: true,
        message: 'If an account with that email exists, a password reset link has been sent.',
      });
      return;
    }

    // Generate reset token
    const resetToken = crypto.randomBytes(32).toString('hex');
    const resetTokenExpires = new Date(Date.now() + 3600000); // 1 hour

    await prisma.user.update({
      where: { id: user.id },
      data: {
        password_reset_token: resetToken,
        password_reset_expires: resetTokenExpires,
      },
    });

    // TODO: Send email with reset link
    // For now, we'll just return the token (remove this in production)
    console.log(`Password reset token for ${email}: ${resetToken}`);

    res.json({
      success: true,
      message: 'If an account with that email exists, a password reset link has been sent.',
      // Remove this in production:
      resetToken: process.env.NODE_ENV === 'development' ? resetToken : undefined,
    });
  } catch (error: any) {
    console.error('Forgot password error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while processing password reset request',
    });
  }
};

// Reset password
const resetPassword = async (req: Request, res: Response): Promise<void> => {
  try {
    const { token, password } = req.body;

    const user = await prisma.user.findFirst({
      where: {
        password_reset_token: token,
        password_reset_expires: {
          gt: new Date(),
        },
      },
    });
    if (!user) {
      res.status(400).json({
        success: false,
        message: 'Invalid or expired reset token',
      });
      return;
    }

    // Hash new password and update user
    const hashedPassword = await bcryptjs.hash(password, 12);

    await prisma.user.update({
      where: { id: user.id },
      data: {
        password: hashedPassword,
        password_reset_token: null,
        password_reset_expires: null,
        refresh_token: null, // Clear all sessions
      },
    });

    res.json({
      success: true,
      message: 'Password reset successfully. Please login with your new password.',
    });
  } catch (error: any) {
    console.error('Reset password error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while resetting password',
    });
  }
};

// Verify email (placeholder for future implementation)
const verifyEmail = async (req: Request, res: Response): Promise<void> => {
  try {
    const { token } = req.params;

    const user = await prisma.user.findFirst({
      where: {
        email_verification_token: token || null,
      },
    });
    if (!user) {
      res.status(400).json({
        success: false,
        message: 'Invalid or expired verification token',
      });
      return;
    }

    await prisma.user.update({
      where: { id: user.id },
      data: {
        email_verified: true,
        email_verification_token: null,
      },
    });

    res.json({
      success: true,
      message: 'Email verified successfully',
    });
  } catch (error: any) {
    console.error('Email verification error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error during email verification',
    });
  }
};

// Resend verification email (placeholder for future implementation)
const resendVerification = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
  try {
    const {user} = req;

    if (user.email_verified) {
      res.status(400).json({
        success: false,
        message: 'Email is already verified',
      });
      return;
    }

    // Generate new verification token
    const verificationToken = crypto.randomBytes(32).toString('hex');
    await prisma.user.update({
      where: { id: user.id },
      data: { email_verification_token: verificationToken },
    });

    // TODO: Send verification email
    console.log(`Verification token for ${user.email}: ${verificationToken}`);

    res.json({
      success: true,
      message: 'Verification email sent successfully',
    });
  } catch (error: any) {
    console.error('Resend verification error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while sending verification email',
    });
  }
};

export {
  register,
  login,
  refreshToken,
  logout,
  getProfile,
  updateProfile,
  changePassword,
  forgotPassword,
  resetPassword,
  verifyEmail,
  resendVerification,
};

export default {
  register,
  login,
  refreshToken,
  logout,
  getProfile,
  updateProfile,
  changePassword,
  forgotPassword,
  resetPassword,
  verifyEmail,
  resendVerification,
};
