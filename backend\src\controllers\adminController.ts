import { prisma } from '../config/prisma';
import logger from '../utils/logger';
import { usageService } from '../services/usageService';
import { Request, Response } from 'express';

interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    [key: string]: any;
  };
}

class AdminController {
  /**
   * Obține statisticile dashboard-ului admin
   */
  async getDashboardStats(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {

      // Obține statisticile utilizatorilor
      const totalUsers = await prisma.user.count();
      const activeUsers = await prisma.user.count({
        where: {
          is_active: true,
        },
      });
      const newUsersThisMonth = await prisma.user.count({
        where: {
          created_at: {
            gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
          },
        },
      });

      // Obține statisticile abonamentelor
      const totalSubscriptions = await prisma.subscription.count();
      const activeSubscriptions = await prisma.subscription.count({
        where: {
          status: 'active',
        },
      });
      const cancelledSubscriptions = await prisma.subscription.count({
        where: {
          status: 'canceled',
        },
      });

      // Obține statisticile cheltuielilor
      const totalExpenses = await prisma.expense.count();
      const expensesThisMonth = await prisma.expense.count({
        where: {
          date: {
            gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
          },
        },
      });

      // Calculează venitul total (estimativ)
      const monthlyRevenue = activeSubscriptions * 9.99; // Presupunem un preț mediu

      res.json({
        success: true,
        data: {
          users: {
            total: totalUsers,
            active: activeUsers,
            newThisMonth: newUsersThisMonth,
          },
          subscriptions: {
            total: totalSubscriptions,
            active: activeSubscriptions,
            cancelled: cancelledSubscriptions,
          },
          expenses: {
            total: totalExpenses,
            thisMonth: expensesThisMonth,
          },
          revenue: {
            monthly: monthlyRevenue,
            annual: monthlyRevenue * 12,
          },
        },
      });
    } catch (error) {
      logger.error('Error getting dashboard stats:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  }

  /**
   * Obține alertele de sistem
   */
  async getSystemAlerts(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {

      // Pentru moment, returnăm alerte mock
      // În viitor, acestea pot fi stocate în baza de date
      const alerts = [
        {
          id: 1,
          type: 'warning',
          title: 'Server Load High',
          message: 'Server load is above 80%',
          timestamp: new Date().toISOString(),
          read: false,
        },
        {
          id: 2,
          type: 'info',
          title: 'New User Registrations',
          message: '5 new users registered today',
          timestamp: new Date(Date.now() - 3600000).toISOString(),
          read: false,
        },
      ];

      res.json({
        success: true,
        data: alerts,
      });
    } catch (error) {
      logger.error('Error getting system alerts:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  }

  /**
   * Marchează o alertă ca citită
   */
  async markAlertAsRead(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {

      const { alertId } = req.params;

      // Pentru moment, simulăm marcarea ca citită
      // În viitor, aceasta va actualiza baza de date
      logger.info(`Alert ${alertId} marked as read by admin ${req.user.id}`);

      res.json({
        success: true,
        message: 'Alert marked as read',
      });
    } catch (error) {
      logger.error('Error marking alert as read:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  }

  /**
   * Obține lista utilizatorilor cu paginare
   */
  async getUsers(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {

      const { page = '1', limit = '10', search = '', status = 'all', planType } = req.query;
      const offset = (parseInt(page as string) - 1) * parseInt(limit as string);

      const whereClause: any = {};
      
      if (search && typeof search === 'string') {
        whereClause.OR = [
          { name: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } },
        ];
      }
      
      if (status !== 'all') {
        whereClause.is_active = status === 'active';
      }

      const [users, total] = await Promise.all([
        prisma.user.findMany({
          where: whereClause,
          select: {
            id: true,
            name: true,
            email: true,
            role: true,
            is_active: true,
            created_at: true,
            last_login: true,
            login_count: true,
            plan_type: true,
            subscription_status: true,
            stripe_customer_id: true,
            subscription_current_period_start: true,
            subscription_current_period_end: true,
            monthly_expense_count: true,
            monthly_expense_limit: true,
            currency: true,
            timezone: true,
            email_verified: true,
            preferences: true,
            subscription: {
              select: {
                id: true,
                status: true,
                current_period_start: true,
                current_period_end: true,
                trial_start: true,
                trial_end: true,
                plan: {
                  select: {
                    id: true,
                    name: true,
                    price: true,
                    currency: true,
                    interval: true,
                    features: true,
                  },
                },
              },
            },
            _count: {
              select: {
                expenses: true,
                categories: true,
                usage_logs: true,
              },
            },
          },
          orderBy: { created_at: 'desc' },
          skip: offset,
          take: parseInt(limit as string),
        }),
        prisma.user.count({ where: whereClause }),
      ]);

      // Optimizare: Calculează statistici suplimentare folosind query-uri SQL
      const usersWithStats = await Promise.all(
        users.map(async (user) => {
          // Calculează veniturile totale folosind query SQL optimizat
          let totalRevenue = 0;
          if (user.subscription && user.subscription.plan) {
            const revenueResult = await prisma.$queryRaw<Array<{revenue: number}>>`
              SELECT 
                CASE 
                  WHEN p.interval = 'month' THEN 
                    p.price * GREATEST(1, EXTRACT(EPOCH FROM (NOW() - s.current_period_start)) / 2592000)
                  WHEN p.interval = 'year' THEN 
                    p.price * GREATEST(1, EXTRACT(EPOCH FROM (NOW() - s.current_period_start)) / 31536000)
                  ELSE 0
                END as revenue
              FROM subscriptions s
              JOIN plans p ON s.plan_id = p.id
              WHERE s.user_id = ${user.id} AND s.status = 'active'
              LIMIT 1
            `;
            
            totalRevenue = revenueResult[0]?.revenue || 0;
          }

          // Mapează câmpurile pentru compatibilitate cu frontend
          return {
            ...user,
            // Mapări pentru compatibilitate
            createdAt: user.created_at,
            lastActiveAt: user.last_login,
            loginCount: user.login_count,
            totalExpenses: user._count.expenses,
            totalCategories: user._count.categories,
            totalUsageLogs: user._count.usage_logs,
            totalRevenue: parseFloat(totalRevenue) || 0,
            // Status bazat pe is_active și subscription_status
            status: !user.is_active ? 'blocked' : 
                    user.subscription_status === 'active' ? 'active' : 
                    user.subscription_status === 'trialing' ? 'trial' : 'inactive',
            // Plan type pentru filtrare
            planType: user.plan_type,
          };
        })
      );

      res.json({
        success: true,
        data: {
          users: usersWithStats,
          pagination: {
            page: parseInt(page as string),
            limit: parseInt(limit as string),
            total,
            pages: Math.ceil(total / parseInt(limit as string)),
          },
        },
      });
    } catch (error) {
      logger.error('Error getting users:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  }

  /**
   * Obține detaliile unui utilizator
   */
  async getUserDetails(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {

      const { userId } = req.params;

      const user = await prisma.user.findUnique({
        where: { id: parseInt(userId) },
        include: {
          subscription: {
            include: {
              plan: true,
            },
          },
          expenses: {
            orderBy: { created_at: 'desc' },
            take: 10,
            include: {
              category: true,
            },
          },
          categories: {
            where: { is_active: true },
            orderBy: { sort_order: 'asc' },
          },
          usage_logs: {
            orderBy: { created_at: 'desc' },
            take: 20,
          },
          _count: {
            select: {
              expenses: true,
              categories: true,
              usage_logs: true,
            },
          },
        },
      });

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found',
        });
      }

      // Exclude password from response
      const { password, ...userWithoutPassword } = user;

      // Calculează statistici suplimentare
      let totalRevenue = 0;
      if (user.subscription && user.subscription.plan) {
        const plan = user.subscription.plan;
        const startDate = new Date(user.subscription.current_period_start);
        const now = new Date();
        
        // Calculează numărul de perioade de facturare
        let periods = 0;
        if (plan.interval === 'month') {
          periods = Math.floor((now.getTime() - startDate.getTime()) / (30 * 24 * 60 * 60 * 1000)) + 1;
        } else if (plan.interval === 'year') {
          periods = Math.floor((now.getTime() - startDate.getTime()) / (365 * 24 * 60 * 60 * 1000)) + 1;
        }
        
        totalRevenue = parseFloat(plan.price.toString()) * Math.max(periods, 0);
      }

      // Calculează suma totală a cheltuielilor
      const totalExpenseAmount = await prisma.expense.aggregate({
        where: { user_id: parseInt(userId as string) },
        _sum: { amount: true },
      });

      // Adaugă informații suplimentare
      const enrichedUser = {
        ...userWithoutPassword,
        // Mapări pentru compatibilitate cu frontend
        createdAt: user.created_at,
        lastActiveAt: user.last_login,
        loginCount: user.login_count,
        totalExpenses: user._count.expenses,
        totalCategories: user._count.categories,
        totalUsageLogs: user._count.usage_logs,
        totalRevenue,
        totalExpenseAmount: parseFloat(totalExpenseAmount._sum.amount?.toString() || '0'),
        // Status bazat pe is_active și subscription_status
        status: !user.is_active ? 'blocked' : 
                user.subscription_status === 'active' ? 'active' : 
                user.subscription_status === 'trialing' ? 'trial' : 'inactive',
        // Plan type pentru filtrare
        planType: user.plan_type,
      };

      res.json({
        success: true,
        data: enrichedUser,
      });
    } catch (error) {
      logger.error('Error getting user details:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  }

  /**
   * Obține statisticile de utilizare globale
   */
  async getUsageStats(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {

      const { startDate, endDate } = req.query;
      const stats = await usageService.getGlobalUsageStats(startDate as string, endDate as string);

      res.json({
        success: true,
        data: stats,
      });
    } catch (error) {
      logger.error('Error getting usage stats:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get usage statistics',
      });
    }
  }

  /**
   * Obține datele de venituri pentru perioada specificată
   */
  async getRevenueData(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {

      const { period = '12months' } = req.query;
      
      // Calculează perioada de timp
      let startDate = new Date();
      switch (period) {
        case '7days':
          startDate.setDate(startDate.getDate() - 7);
          break;
        case '30days':
          startDate.setDate(startDate.getDate() - 30);
          break;
        case '3months':
          startDate.setMonth(startDate.getMonth() - 3);
          break;
        case '6months':
          startDate.setMonth(startDate.getMonth() - 6);
          break;
        case '12months':
        default:
          startDate.setFullYear(startDate.getFullYear() - 1);
          break;
      }

      // Obține datele de venituri din abonamente
      const subscriptions = await prisma.subscription.findMany({
        where: {
          created_at: {
            gte: startDate,
          },
          status: 'active',
        },
        include: {
          plan: true,
        },
        orderBy: {
          created_at: 'asc',
        },
      });

      // Grupează veniturile pe luni
      const revenueByMonth: { [key: string]: number } = {};
      let totalRevenue = 0;

      subscriptions.forEach(subscription => {
        const monthKey = subscription.created_at.toISOString().substring(0, 7); // YYYY-MM
        if (!revenueByMonth[monthKey]) {
          revenueByMonth[monthKey] = 0;
        }
        const price = parseFloat(subscription.plan.price.toString());
        revenueByMonth[monthKey] += price;
        totalRevenue += price;
      });

      // Convertește în format pentru grafice
      const chartData = Object.entries(revenueByMonth).map(([month, revenue]) => ({
        month,
        revenue,
        date: new Date(month + '-01'),
      })).sort((a, b) => a.date.getTime() - b.date.getTime());

      // Calculează statistici suplimentare
      const currentMonth = new Date().toISOString().substring(0, 7);
      const lastMonth = new Date(new Date().setMonth(new Date().getMonth() - 1)).toISOString().substring(0, 7);
      
      const currentMonthRevenue = revenueByMonth[currentMonth] || 0;
      const lastMonthRevenue = revenueByMonth[lastMonth] || 0;
      const growthRate = lastMonthRevenue > 0 ? ((currentMonthRevenue - lastMonthRevenue) / lastMonthRevenue) * 100 : 0;

      res.json({
        success: true,
        data: {
          chartData,
          totalRevenue,
          currentMonthRevenue,
          lastMonthRevenue,
          growthRate: Math.round(growthRate * 100) / 100,
          period,
        },
      });
    } catch (error) {
      logger.error('Error getting revenue data:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get revenue data',
      });
    }
  }

  /**
   * Obține activitatea recentă a utilizatorilor
   */
  async getActivity(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { page = '1', limit = '50', timeRange = '24h' } = req.query;
      const offset = (parseInt(page as string) - 1) * parseInt(limit as string);

      // Calculează perioada de timp
      let startDate = new Date();
      switch (timeRange) {
        case '1h':
          startDate.setHours(startDate.getHours() - 1);
          break;
        case '24h':
        default:
          startDate.setDate(startDate.getDate() - 1);
          break;
        case '7d':
          startDate.setDate(startDate.getDate() - 7);
          break;
        case '30d':
          startDate.setDate(startDate.getDate() - 30);
          break;
      }

      // Obține activitatea din usage_logs
      const [activities, total] = await Promise.all([
        prisma.usageLog.findMany({
          where: {
            created_at: {
              gte: startDate,
            },
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: { created_at: 'desc' },
          skip: offset,
          take: parseInt(limit as string),
        }),
        prisma.usageLog.count({
          where: {
            created_at: {
              gte: startDate,
            },
          },
        }),
      ]);

      res.json({
        success: true,
        data: {
          activities,
          pagination: {
            page: parseInt(page as string),
            limit: parseInt(limit as string),
            total,
            pages: Math.ceil(total / parseInt(limit as string)),
          },
          timeRange,
        },
      });
    } catch (error) {
      logger.error('Error getting activity:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get activity data',
      });
    }
  }
}

export default new AdminController();
