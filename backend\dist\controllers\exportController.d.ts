import { Request, Response } from 'express';
interface AuthenticatedRequest extends Request {
    user?: any;
    userId?: string;
}
export declare const exportController: {
    exportCSV: (req: AuthenticatedRequest, res: Response) => Promise<void>;
    exportPDF: (req: AuthenticatedRequest, res: Response) => Promise<void>;
    exportExcel: (req: AuthenticatedRequest, res: Response) => Promise<void>;
};
export {};
//# sourceMappingURL=exportController.d.ts.map