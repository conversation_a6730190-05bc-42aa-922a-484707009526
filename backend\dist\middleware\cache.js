"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.closeCache = exports.generateCacheKey = exports.cleanupExpiredCache = exports.getCacheStats = exports.warmupFunctions = exports.cacheWarmer = exports.invalidators = exports.invalidateCache = exports.publicCache = exports.reportCache = exports.expenseCache = exports.categoryCache = exports.userCache = exports.cache = exports.initializeRedis = exports.invalidateExpenseCache = exports.invalidateCategoryCache = exports.invalidateUserCache = void 0;
const redis_1 = __importDefault(require("redis"));
const logger_1 = __importDefault(require("../utils/logger"));
const crypto_1 = __importDefault(require("crypto"));
let redisClient = null;
let isRedisConnected = false;
const initializeRedis = async () => {
    try {
        if (process.env.REDIS_URL) {
            redisClient = redis_1.default.createClient({
                url: process.env.REDIS_URL,
                socket: {
                    reconnectStrategy: (retries) => {
                        if (retries > 10) {
                            logger_1.default.error('Redis max retry attempts reached');
                            return new Error('Max retry attempts reached');
                        }
                        return Math.min(retries * 100, 3000);
                    }
                }
            });
            redisClient.on('error', (err) => {
                logger_1.default.error('Redis Client Error:', err);
                isRedisConnected = false;
            });
            redisClient.on('connect', () => {
                logger_1.default.info('Redis Client Connected');
                isRedisConnected = true;
            });
            redisClient.on('disconnect', () => {
                logger_1.default.warn('Redis Client Disconnected');
                isRedisConnected = false;
            });
            await redisClient.connect();
        }
        else {
            logger_1.default.warn('REDIS_URL not configured, caching will be disabled');
        }
    }
    catch (error) {
        logger_1.default.error('Failed to initialize Redis:', error);
        isRedisConnected = false;
    }
};
exports.initializeRedis = initializeRedis;
const generateCacheKey = (prefix, req, additionalParams = {}) => {
    const baseKey = {
        url: req.originalUrl,
        method: req.method,
        userId: req.user?.id || 'anonymous',
        query: req.query,
        params: req.params,
        ...additionalParams
    };
    const keyString = JSON.stringify(baseKey);
    const hash = crypto_1.default.createHash('md5').update(keyString).digest('hex');
    return `${prefix}:${hash}`;
};
exports.generateCacheKey = generateCacheKey;
const cache = (options = {}) => {
    const { ttl = 300, prefix = 'cache', skipCache = false, varyBy = [], condition = () => true } = options;
    return async (req, res, next) => {
        if (!isRedisConnected || skipCache || !condition(req)) {
            return next();
        }
        try {
            const additionalParams = {};
            varyBy.forEach(param => {
                if (req[param]) {
                    additionalParams[param] = req[param];
                }
            });
            const cacheKey = generateCacheKey(prefix, req, additionalParams);
            const cachedData = await redisClient.get(cacheKey);
            if (cachedData) {
                const parsed = JSON.parse(cachedData);
                res.set({
                    'X-Cache': 'HIT',
                    'X-Cache-Key': cacheKey,
                    'X-Cache-TTL': ttl.toString()
                });
                logger_1.default.logPerformance('cache_hit', 0, {
                    cacheKey,
                    url: req.originalUrl,
                    userId: req.user?.id
                });
                res.status(parsed.statusCode || 200).json(parsed.data);
                return;
            }
            const originalJson = res.json;
            const originalSend = res.send;
            res.json = function (data) {
                if (res.statusCode >= 200 && res.statusCode < 300) {
                    const cacheData = {
                        data,
                        statusCode: res.statusCode,
                        timestamp: new Date().toISOString()
                    };
                    redisClient.setEx(cacheKey, ttl, JSON.stringify(cacheData))
                        .catch(err => logger_1.default.error('Cache save error:', err));
                    logger_1.default.logPerformance('cache_miss', 0, {
                        cacheKey,
                        url: req.originalUrl,
                        userId: req.user?.id,
                        ttl
                    });
                }
                res.set({
                    'X-Cache': 'MISS',
                    'X-Cache-Key': cacheKey,
                    'X-Cache-TTL': ttl.toString()
                });
                return originalJson.call(this, data);
            };
            res.send = function (data) {
                if (res.statusCode >= 200 && res.statusCode < 300) {
                    const cacheData = {
                        data,
                        statusCode: res.statusCode,
                        timestamp: new Date().toISOString()
                    };
                    redisClient.setEx(cacheKey, ttl, JSON.stringify(cacheData))
                        .catch(err => logger_1.default.error('Cache save error:', err));
                }
                res.set({
                    'X-Cache': 'MISS',
                    'X-Cache-Key': cacheKey,
                    'X-Cache-TTL': ttl.toString()
                });
                return originalSend.call(this, data);
            };
            next();
        }
        catch (error) {
            logger_1.default.error('Cache middleware error:', error);
            next();
        }
    };
};
exports.cache = cache;
const userCache = cache({
    ttl: 900,
    prefix: 'user',
    condition: (req) => req.method === 'GET'
});
exports.userCache = userCache;
const categoryCache = cache({
    ttl: 1800,
    prefix: 'categories',
    condition: (req) => req.method === 'GET'
});
exports.categoryCache = categoryCache;
const expenseCache = cache({
    ttl: 300,
    prefix: 'expenses',
    condition: (req) => req.method === 'GET' && !req.query.realtime
});
exports.expenseCache = expenseCache;
const reportCache = cache({
    ttl: 3600,
    prefix: 'reports',
    condition: (req) => req.method === 'GET'
});
exports.reportCache = reportCache;
const publicCache = cache({
    ttl: 7200,
    prefix: 'public',
    condition: (req) => req.method === 'GET'
});
exports.publicCache = publicCache;
const invalidateCache = async (pattern) => {
    if (!isRedisConnected || !redisClient)
        return;
    try {
        const keys = await redisClient.keys(pattern);
        if (keys.length > 0) {
            await redisClient.del(keys);
            logger_1.default.logPerformance('cache_invalidated', 0, {
                pattern,
                keysCount: keys.length
            });
        }
    }
    catch (error) {
        logger_1.default.error('Cache invalidation error:', error);
    }
};
exports.invalidateCache = invalidateCache;
const cacheInvalidator = (patterns) => {
    return (req, res, next) => {
        const originalJson = res.json;
        const originalSend = res.send;
        res.json = function (data) {
            if (res.statusCode >= 200 && res.statusCode < 300) {
                patterns.forEach(pattern => {
                    const resolvedPattern = typeof pattern === 'function'
                        ? pattern(req, res)
                        : pattern;
                    invalidateCache(resolvedPattern);
                });
            }
            return originalJson.call(this, data);
        };
        res.send = function (data) {
            if (res.statusCode >= 200 && res.statusCode < 300) {
                patterns.forEach(pattern => {
                    const resolvedPattern = typeof pattern === 'function'
                        ? pattern(req, res)
                        : pattern;
                    invalidateCache(resolvedPattern);
                });
            }
            return originalSend.call(this, data);
        };
        next();
    };
};
const invalidators = {
    user: cacheInvalidator([
        (req) => `user:*${req.user?.id}*`,
        (req) => `expenses:*${req.user?.id}*`,
        (req) => `reports:*${req.user?.id}*`
    ]),
    categories: cacheInvalidator([
        (req) => `categories:*${req.user?.id}*`,
        (req) => `expenses:*${req.user?.id}*`,
        (req) => `reports:*${req.user?.id}*`
    ]),
    expenses: cacheInvalidator([
        (req) => `expenses:*${req.user?.id}*`,
        (req) => `reports:*${req.user?.id}*`
    ]),
    all: cacheInvalidator([
        (req) => `*:*${req.user?.id}*`
    ])
};
exports.invalidators = invalidators;
const cacheWarmer = (warmupFunctions) => {
    return async (req, res, next) => {
        setImmediate(async () => {
            for (const warmupFn of warmupFunctions) {
                try {
                    await warmupFn(req);
                }
                catch (error) {
                    logger_1.default.error('Cache warming error:', error);
                }
            }
        });
        next();
    };
};
exports.cacheWarmer = cacheWarmer;
const warmupFunctions = {
    userCategories: async (req) => {
        if (!req.user?.id || !redisClient)
            return;
        const cacheKey = generateCacheKey('categories', {
            originalUrl: `/api/categories`,
            method: 'GET',
            user: { id: req.user.id },
            query: {},
            params: {}
        });
        const exists = await redisClient.exists(cacheKey);
        if (!exists) {
            logger_1.default.logPerformance('cache_warmup', 0, {
                type: 'userCategories',
                userId: req.user.id,
                cacheKey
            });
        }
    }
};
exports.warmupFunctions = warmupFunctions;
const getCacheStats = async () => {
    if (!isRedisConnected || !redisClient) {
        return { error: 'Redis not connected' };
    }
    try {
        const info = await redisClient.info('memory');
        const keyspace = await redisClient.info('keyspace');
        return {
            connected: isRedisConnected,
            memory: info,
            keyspace: keyspace
        };
    }
    catch (error) {
        logger_1.default.error('Error getting cache stats:', error);
        return { error: error.message };
    }
};
exports.getCacheStats = getCacheStats;
const cleanupExpiredCache = async () => {
    if (!isRedisConnected || !redisClient)
        return;
    try {
        const keys = await redisClient.keys('*');
        let expiredCount = 0;
        for (const key of keys) {
            const ttl = await redisClient.ttl(key);
            if (ttl === -1) {
                await redisClient.expire(key, 3600);
                expiredCount++;
            }
        }
        logger_1.default.logPerformance('cache_cleanup', 0, {
            totalKeys: keys.length,
            keysWithoutTTL: expiredCount
        });
    }
    catch (error) {
        logger_1.default.error('Cache cleanup error:', error);
    }
};
exports.cleanupExpiredCache = cleanupExpiredCache;
const closeCache = async () => {
    try {
        if (redisClient && redisClient.isOpen) {
            await redisClient.quit();
            logger_1.default.info('Redis connection closed successfully');
        }
    }
    catch (error) {
        logger_1.default.error('Error closing Redis connection:', error);
        throw error;
    }
};
exports.closeCache = closeCache;
exports.invalidateUserCache = invalidators.user;
exports.invalidateCategoryCache = invalidators.categories;
exports.invalidateExpenseCache = invalidators.expenses;
//# sourceMappingURL=cache.js.map