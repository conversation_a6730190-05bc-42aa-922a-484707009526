"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.headerSecurity = void 0;
const crypto_1 = require("crypto");
const logger_1 = __importDefault(require("../utils/logger"));
const headerSecurity = (req, res, next) => {
    const userAgent = req.get('User-Agent');
    if (!userAgent || userAgent.length < 10 || userAgent.length > 500) {
        logger_1.default.warn('Suspicious user agent detected', {
            ip: req.ip,
            url: req.originalUrl,
            userAgent: userAgent || 'missing',
            userId: req.user?.id
        });
    }
    const suspiciousHeaders = ['x-forwarded-host', 'x-original-url', 'x-rewrite-url'];
    for (const header of suspiciousHeaders) {
        if (req.get(header)) {
            logger_1.default.warn('Suspicious header detected', {
                ip: req.ip,
                url: req.originalUrl,
                header,
                value: req.get(header),
                userId: req.user?.id
            });
        }
    }
    res.set({
        'X-Request-ID': req.id || (0, crypto_1.randomUUID)(),
        'X-Response-Time': Date.now().toString(),
        'X-Content-Type-Options': 'nosniff',
        'X-Download-Options': 'noopen',
        'X-Permitted-Cross-Domain-Policies': 'none'
    });
    next();
};
exports.headerSecurity = headerSecurity;
exports.default = exports.headerSecurity;
//# sourceMappingURL=headerSecurity.js.map