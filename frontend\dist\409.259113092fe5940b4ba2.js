"use strict";(self.webpackChunkexpense_tracker_frontend=self.webpackChunkexpense_tracker_frontend||[]).push([[409],{125:(e,a,r)=>{r.d(a,{Ay:()=>n});var t=r(4848),s=(r(6540),r(2392));r(9264);const n=({children:e,className:a="",shadow:r=!0,border:n=!0,rounded:o=!0,padding:i=!0,hover:c=!1,clickable:u=!1,onClick:l,...d})=>{const m=u||<PERSON><PERSON>an(l);return(0,t.jsx)("div",{className:(0,s.cn)("bg-white",r&&"shadow-sm",n&&"border border-gray-200",o&&"rounded-lg",i&&"p-6",c&&"hover:shadow-md transition-shadow duration-200",m&&["cursor-pointer","hover:shadow-md hover:border-gray-300","transition-all duration-200","focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"],a),onClick:l,role:m?"button":void 0,tabIndex:m?0:void 0,onKeyDown:m?e=>{"Enter"!==e.key&&" "!==e.key||(e.preventDefault(),l?.(e))}:void 0,...d,children:e})}},4477:(e,a,r)=>{r.d(a,{Bq:()=>k,MD:()=>u,o4:()=>h,Xi:()=>E,hU:()=>I,gm:()=>d,l4:()=>f,wr:()=>p,fZ:()=>v,cV:()=>l,Q2:()=>w,d:()=>S,gI:()=>A,U2:()=>R,b4:()=>g,aZ:()=>m,RB:()=>b,kp:()=>y});var t=r(3930),s=r(7665),n=r(7097),o=r(888),i=r(6668);const c=new class{async getDashboardStats(){try{return(await i.A.get("/admin/dashboard/stats")).data.data}catch(e){throw console.error("Eroare la obținerea statisticilor dashboard:",e),e}}async getSubscriptionStats(){try{return(await i.A.get("/admin/subscriptions/stats")).data.data}catch(e){throw console.error("Eroare la obținerea statisticilor de abonament:",e),e}}async getPlanStats(){try{return(await i.A.get("/admin/plans/stats")).data.data}catch(e){throw console.error("Eroare la obținerea statisticilor planurilor:",e),e}}async getUsageStats(){try{return(await i.A.get("/admin/usage/stats")).data.data}catch(e){throw console.error("Eroare la obținerea statisticilor de utilizare:",e),e}}async getRevenueData(e="12months"){try{return(await i.A.get(`/admin/revenue/data?period=${e}`)).data.data}catch(e){throw console.error("Eroare la obținerea datelor de venituri:",e),e}}async getUsers(e={}){try{const{page:a=1,limit:r=10,search:t="",status:s="",plan:n="",sortBy:o="created_at",sortOrder:c="desc"}=e,u=new URLSearchParams({page:a.toString(),limit:r.toString(),...t&&{search:t},...s&&{status:s},...n&&{plan:n},sortBy:o,sortOrder:c});return(await i.A.get(`/admin/users?${u}`)).data.data}catch(e){throw console.error("Eroare la obținerea utilizatorilor:",e),e}}async getUserDetails(e){try{return(await i.A.get(`/admin/users/${e}`)).data.data}catch(e){throw console.error("Eroare la obținerea detaliilor utilizatorului:",e),e}}async blockUser(e,a=""){try{return(await i.A.post(`/admin/users/${e}/block`,{reason:a})).data.data}catch(e){throw console.error("Eroare la blocarea utilizatorului:",e),e}}async unblockUser(e){try{return(await i.A.post(`/admin/users/${e}/unblock`)).data.data}catch(e){throw console.error("Eroare la deblocarea utilizatorului:",e),e}}async getSubscriptions(e={}){try{const{page:a=1,limit:r=10,status:t="",plan:s="",sortBy:n="created_at",sortOrder:o="desc"}=e,c=new URLSearchParams({page:a.toString(),limit:r.toString(),...t&&{status:t},...s&&{plan:s},sortBy:n,sortOrder:o});return(await i.A.get(`/admin/subscriptions?${c}`)).data.data}catch(e){throw console.error("Eroare la obținerea abonamentelor:",e),e}}async getSubscriptionDetails(e){try{return(await i.A.get(`/admin/subscriptions/${e}`)).data.data}catch(e){throw console.error("Eroare la obținerea detaliilor abonamentului:",e),e}}async suspendSubscription(e,a=""){try{return(await i.A.post(`/admin/subscriptions/${e}/suspend`,{reason:a})).data.data}catch(e){throw console.error("Eroare la suspendarea abonamentului:",e),e}}async reactivateSubscription(e){try{return(await i.A.post(`/admin/subscriptions/${e}/reactivate`)).data.data}catch(e){throw console.error("Eroare la reactivarea abonamentului:",e),e}}async cancelSubscription(e,a=""){try{return(await i.A.post(`/admin/subscriptions/${e}/cancel`,{reason:a})).data.data}catch(e){throw console.error("Eroare la anularea abonamentului:",e),e}}async syncSubscriptionWithStripe(e){try{return(await i.A.post(`/admin/subscriptions/${e}/sync`)).data.data}catch(e){throw console.error("Eroare la sincronizarea cu Stripe:",e),e}}async getActivityFeed(e={}){try{const{page:a=1,limit:r=20,type:t="",timeRange:s="7d"}=e,n=new URLSearchParams({page:a.toString(),limit:r.toString(),...t&&{type:t},timeRange:s});return(await i.A.get(`/admin/activity?${n}`)).data.data}catch(e){throw console.error("Eroare la obținerea activității:",e),e}}async getActivityStats(e="7d"){try{return(await i.A.get(`/admin/activity/stats?timeRange=${e}`)).data.data}catch(e){throw console.error("Eroare la obținerea statisticilor activității:",e),e}}async exportData(e,a={}){try{const r=new URLSearchParams(a);return(await i.A.get(`/admin/export/${e}?${r}`,{responseType:"blob"})).data}catch(e){throw console.error("Eroare la exportul datelor:",e),e}}async getSystemAlerts(){try{return(await i.A.get("/admin/alerts")).data.data}catch(e){throw console.error("Eroare la obținerea alertelor:",e),e}}async markAlertAsRead(e){try{return(await i.A.post(`/admin/alerts/${e}/read`)).data.data}catch(e){throw console.error("Eroare la marcarea alertei:",e),e}}async getSystemConfig(){try{return(await i.A.get("/admin/config")).data.data}catch(e){throw console.error("Eroare la obținerea configurărilor:",e),e}}async updateSystemConfig(e){try{return(await i.A.put("/admin/config",e)).data.data}catch(e){throw console.error("Eroare la actualizarea configurărilor:",e),e}}};function u(){return(0,t.I)({queryKey:["admin","dashboard","stats"],queryFn:c.getDashboardStats,staleTime:3e5,cacheTime:6e5})}function l(){return(0,t.I)({queryKey:["admin","subscriptions","stats"],queryFn:c.getSubscriptionStats,staleTime:3e5,cacheTime:6e5})}function d(){return(0,t.I)({queryKey:["admin","plans","stats"],queryFn:c.getPlanStats,staleTime:3e5,cacheTime:6e5})}function m(){return(0,t.I)({queryKey:["admin","usage","stats"],queryFn:c.getUsageStats,staleTime:3e5,cacheTime:6e5})}function p(e="12months"){return(0,t.I)({queryKey:["admin","revenue","data",e],queryFn:()=>c.getRevenueData(e),staleTime:6e5,cacheTime:18e5})}function y(e={}){return(0,t.I)({queryKey:["admin","users",e],queryFn:()=>c.getUsers(e),keepPreviousData:!0,staleTime:12e4})}function b(e){return(0,t.I)({queryKey:["admin","users",e],queryFn:()=>c.getUserDetails(e),enabled:!!e,staleTime:3e5})}function h(){const e=(0,s.jE)();return(0,n.n)({mutationFn:({userId:e,reason:a})=>c.blockUser(e,a),onSuccess:(a,{userId:r})=>{e.invalidateQueries(["admin","users"]),e.invalidateQueries(["admin","users",r]),o.oR.success("Utilizatorul a fost blocat cu succes")},onError:e=>{o.oR.error(e.response?.data?.message||"Eroare la blocarea utilizatorului")}})}function g(){const e=(0,s.jE)();return(0,n.n)({mutationFn:e=>c.unblockUser(e),onSuccess:(a,r)=>{e.invalidateQueries(["admin","users"]),e.invalidateQueries(["admin","users",r]),o.oR.success("Utilizatorul a fost deblocat cu succes")},onError:e=>{o.oR.error(e.response?.data?.message||"Eroare la deblocarea utilizatorului")}})}function w(e={}){return(0,t.I)({queryKey:["admin","subscriptions",e],queryFn:()=>c.getSubscriptions(e),keepPreviousData:!0,staleTime:12e4})}function v(e){return(0,t.I)({queryKey:["admin","subscriptions",e],queryFn:()=>c.getSubscriptionDetails(e),enabled:!!e,staleTime:3e5})}function S(){const e=(0,s.jE)();return(0,n.n)({mutationFn:({subscriptionId:e,reason:a})=>c.suspendSubscription(e,a),onSuccess:(a,{subscriptionId:r})=>{e.invalidateQueries(["admin","subscriptions"]),e.invalidateQueries(["admin","subscriptions",r]),e.invalidateQueries(["admin","subscriptions","stats"]),o.oR.success("Abonamentul a fost suspendat cu succes")},onError:e=>{o.oR.error(e.response?.data?.message||"Eroare la suspendarea abonamentului")}})}function f(){const e=(0,s.jE)();return(0,n.n)({mutationFn:e=>c.reactivateSubscription(e),onSuccess:(a,r)=>{e.invalidateQueries(["admin","subscriptions"]),e.invalidateQueries(["admin","subscriptions",r]),e.invalidateQueries(["admin","subscriptions","stats"]),o.oR.success("Abonamentul a fost reactivat cu succes")},onError:e=>{o.oR.error(e.response?.data?.message||"Eroare la reactivarea abonamentului")}})}function E(){const e=(0,s.jE)();return(0,n.n)({mutationFn:({subscriptionId:e,reason:a})=>c.cancelSubscription(e,a),onSuccess:(a,{subscriptionId:r})=>{e.invalidateQueries(["admin","subscriptions"]),e.invalidateQueries(["admin","subscriptions",r]),e.invalidateQueries(["admin","subscriptions","stats"]),o.oR.success("Abonamentul a fost anulat cu succes")},onError:e=>{o.oR.error(e.response?.data?.message||"Eroare la anularea abonamentului")}})}function A(){const e=(0,s.jE)();return(0,n.n)({mutationFn:e=>c.syncSubscriptionWithStripe(e),onSuccess:(a,r)=>{e.invalidateQueries(["admin","subscriptions"]),e.invalidateQueries(["admin","subscriptions",r]),o.oR.success("Sincronizarea cu Stripe a fost completă")},onError:e=>{o.oR.error(e.response?.data?.message||"Eroare la sincronizarea cu Stripe")}})}function k(e={}){return(0,t.I)({queryKey:["admin","activity",e],queryFn:async()=>{const a=await c.getActivityFeed(e);return a.data?.activities||[]},keepPreviousData:!0,staleTime:6e4,refetchInterval:3e5})}function R(){return(0,t.I)({queryKey:["admin","alerts"],queryFn:c.getSystemAlerts,staleTime:6e4,refetchInterval:12e4})}function I(){const e=(0,s.jE)();return(0,n.n)({mutationFn:e=>c.markAlertAsRead(e),onSuccess:()=>{e.invalidateQueries(["admin","alerts"])},onError:e=>{o.oR.error(e.response?.data?.message||"Eroare la marcarea alertei")}})}},6668:(e,a,r)=>{r.d(a,{A:()=>o});var t=r(1083);const s=("undefined"!=typeof process&&process.env,"http://localhost:3000/api"),n=t.A.create({baseURL:s,timeout:1e4,headers:{"Content-Type":"application/json"}});n.interceptors.request.use(e=>{const a=localStorage.getItem("accessToken");return a&&(e.headers.Authorization=`Bearer ${a}`),e},e=>Promise.reject(e)),n.interceptors.response.use(e=>e,e=>(401===e.response?.status&&(localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("user"),window.location.href="/login"),403===e.response?.status&&console.error("Acces interzis:",e.response.data?.message),e.response?.status>=500&&console.error("Eroare de server:",e.response.data?.message),Promise.reject(e)));const o=n}}]);