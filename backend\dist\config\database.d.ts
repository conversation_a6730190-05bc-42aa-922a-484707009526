import { Sequelize } from 'sequelize';
interface DatabaseConfig {
    dialect: 'sqlite' | 'postgres';
    storage?: string;
    host?: string;
    port?: number;
    database?: string;
    username?: string;
    password?: string;
    logging?: boolean | ((sql: string, timing?: number) => void);
    define: {
        timestamps: boolean;
        underscored: boolean;
        freezeTableName: boolean;
    };
    pool?: {
        max: number;
        min: number;
        acquire: number;
        idle: number;
    };
    dialectOptions?: {
        ssl?: {
            require: boolean;
            rejectUnauthorized: boolean;
        } | false;
    };
}
declare const dbConfig: DatabaseConfig;
declare let sequelize: Sequelize;
declare const testConnection: () => Promise<boolean>;
declare const initializeDatabase: () => Promise<boolean>;
declare const closeConnection: () => Promise<void>;
export { sequelize, testConnection, initializeDatabase, closeConnection, dbConfig as config, };
export default sequelize;
//# sourceMappingURL=database.d.ts.map