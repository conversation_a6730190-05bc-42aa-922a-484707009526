"use strict";(self.webpackChunkexpense_tracker_frontend=self.webpackChunkexpense_tracker_frontend||[]).push([[291],{1291:(e,r,a)=>{a.r(r),a.d(r,{default:()=>g});var s=a(4985),t=a(893),l=a(6540),o=a(9785),n=a(888),i=a(4976),d=a(7767),c=a(8952),m=a(6103),p=a(6215),x=a(4848);const u=c.Ik({password:c.Yj().min(1,"Parola este obligatorie").min(8,"Parola trebuie să aibă cel puțin 8 caractere").regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,"Parola trebuie să conțină cel puțin o literă mică, o literă mare și o cifră"),confirmPassword:c.Yj().min(1,"Confirmarea parolei este obligatorie")}).refine(e=>e.password===e.confirmPassword,{message:"Parolele nu se potrivesc",path:["confirmPassword"]}),g=()=>{const[e,r]=(0,l.useState)(!1),[a]=(0,i.ok)(),c=(0,d.Zp)(),g=a.get("token"),{register:y,handleSubmit:f,formState:{errors:h}}=(0,o.mN)({resolver:(0,t.u)(u)});return(0,l.useEffect)(()=>{g||(n.oR.error("Token invalid sau lipsă"),c("/forgot-password"))},[g,c]),g?(0,x.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,x.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,x.jsxs)("div",{children:[(0,x.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Setează parola nouă"}),(0,x.jsx)("p",{className:"mt-2 text-center text-sm text-gray-600",children:"Introdu noua parolă pentru contul tău."})]}),(0,x.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:f(async e=>{r(!0);try{await new Promise(e=>setTimeout(e,2e3)),n.oR.success("Parola a fost resetată cu succes!"),c("/login")}catch(e){n.oR.error(e.message||"Eroare la resetarea parolei")}finally{r(!1)}}),children:[(0,x.jsxs)("div",{className:"space-y-4",children:[(0,x.jsx)("div",{children:(0,x.jsx)(p.Ay,{...y("password"),type:"password",label:"Parola nouă",placeholder:"Introdu parola nouă",error:h.password?.message,autoComplete:"new-password"})}),(0,x.jsx)("div",{children:(0,x.jsx)(p.Ay,{...y("confirmPassword"),type:"password",label:"Confirmă parola nouă",placeholder:"Confirmă parola nouă",error:h.confirmPassword?.message,autoComplete:"new-password"})})]}),(0,x.jsx)("div",{children:(0,x.jsx)(m.Ay,{type:"submit",variant:"primary",size:"lg",className:"w-full",loading:e,disabled:e,children:e?"Se resetează...":"Resetează parola"})}),(0,x.jsx)("div",{className:"text-center",children:(0,x.jsxs)(i.N_,{to:"/login",className:"font-medium text-primary-600 hover:text-primary-500 flex items-center justify-center",children:[(0,x.jsx)(s.A,{className:"h-4 w-4 mr-1"}),"Înapoi la conectare"]})})]})]})}):(0,x.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,x.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,x.jsxs)("div",{className:"text-center",children:[(0,x.jsx)("div",{className:"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100",children:(0,x.jsx)("svg",{className:"h-6 w-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,x.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}),(0,x.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Link invalid"}),(0,x.jsx)("p",{className:"mt-2 text-center text-sm text-gray-600",children:"Link-ul de resetare a parolei este invalid sau a expirat."})]}),(0,x.jsx)("div",{className:"mt-8",children:(0,x.jsx)(i.N_,{to:"/forgot-password",className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-primary-600 bg-primary-50 hover:bg-primary-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500",children:"Solicită un nou link"})})]})})}},6103:(e,r,a)=>{a.d(r,{Ay:()=>o});var s=a(4848),t=(a(6540),a(2392)),l=a(9264);const o=({children:e,variant:r="primary",size:a="md",loading:o=!1,disabled:n=!1,fullWidth:i=!1,leftIcon:d=null,rightIcon:c=null,className:m="",onClick:p,type:x="button",...u})=>{const g={primary:["bg-primary-600 text-white border-primary-600","hover:bg-primary-700 hover:border-primary-700","focus:ring-primary-500","disabled:bg-primary-300 disabled:border-primary-300"].join(" "),secondary:["bg-gray-600 text-white border-gray-600","hover:bg-gray-700 hover:border-gray-700","focus:ring-gray-500","disabled:bg-gray-300 disabled:border-gray-300"].join(" "),outline:["bg-transparent text-primary-600 border-primary-600","hover:bg-primary-50 hover:text-primary-700","focus:ring-primary-500","disabled:text-primary-300 disabled:border-primary-300"].join(" "),ghost:["bg-transparent text-gray-700 border-transparent","hover:bg-gray-100 hover:text-gray-900","focus:ring-gray-500","disabled:text-gray-400"].join(" "),danger:["bg-red-600 text-white border-red-600","hover:bg-red-700 hover:border-red-700","focus:ring-red-500","disabled:bg-red-300 disabled:border-red-300"].join(" "),success:["bg-green-600 text-white border-green-600","hover:bg-green-700 hover:border-green-700","focus:ring-green-500","disabled:bg-green-300 disabled:border-green-300"].join(" "),warning:["bg-yellow-600 text-white border-yellow-600","hover:bg-yellow-700 hover:border-yellow-700","focus:ring-yellow-500","disabled:bg-yellow-300 disabled:border-yellow-300"].join(" "),white:["bg-white text-gray-900 border-white","hover:bg-gray-50 hover:text-gray-900","focus:ring-gray-500","disabled:bg-gray-100 disabled:text-gray-400"].join(" ")},y={xs:"h-3 w-3",sm:"h-4 w-4",md:"h-4 w-4",lg:"h-5 w-5",xl:"h-6 w-6"},f=n||o;return(0,s.jsxs)("button",{type:x,onClick:e=>{f?e.preventDefault():p?.(e)},disabled:f,className:(0,t.cn)("inline-flex items-center justify-center","border font-medium rounded-lg","transition-all duration-200 ease-in-out","focus:outline-none focus:ring-2 focus:ring-offset-2","disabled:cursor-not-allowed disabled:opacity-60",g[r],{xs:"px-2 py-1 text-xs",sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base",xl:"px-8 py-4 text-lg"}[a],i&&"w-full",m),...u,children:[d&&!o&&(0,s.jsx)("span",{className:(0,t.cn)(y[a],e&&"mr-2"),children:d}),o&&(0,s.jsx)("span",{className:(0,t.cn)(e&&"mr-2"),children:(0,s.jsx)(l.Ay,{size:"xs"===a||"sm"===a?"sm":"md",color:"currentColor"})}),e&&(0,s.jsx)("span",{className:o?"opacity-70":"",children:e}),c&&!o&&(0,s.jsx)("span",{className:(0,t.cn)(y[a],e&&"ml-2"),children:c})]})}},6215:(e,r,a)=>{a.d(r,{Ay:()=>p});var s=a(4848),t=a(2509),l=a(72),o=a(3956),n=a(7117),i=a(4015),d=a(6540),c=a(2392);const m=(0,d.forwardRef)(({label:e,type:r="text",placeholder:a,value:m,onChange:p,onBlur:x,onFocus:u,error:g,success:y,hint:f,required:h=!1,disabled:b=!1,readOnly:w=!1,size:j="md",leftIcon:N,rightIcon:v,className:k="",inputClassName:A="",labelClassName:C="",id:I,name:P,...R},S)=>{const[z,B]=(0,d.useState)(!1),[_,L]=(0,d.useState)(!1),T=I||`input-${Math.random().toString(36).substr(2,9)}`,D="password"===r&&z?"text":r,E={sm:"h-4 w-4",md:"h-5 w-5",lg:"h-6 w-6"},F=Boolean(g),M=Boolean(y)&&!F;return Boolean(N||v||"password"===r),(0,s.jsxs)("div",{className:(0,c.cn)("w-full",k),children:[e&&(0,s.jsxs)("label",{htmlFor:T,className:(0,c.cn)("block text-sm font-medium mb-2",F?"text-red-700":"text-gray-700",b&&"text-gray-400",C),children:[e,h&&(0,s.jsx)("span",{className:"text-red-500 ml-1","aria-label":"obligatoriu",children:"*"})]}),(0,s.jsxs)("div",{className:"relative",children:[N&&(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)("span",{className:(0,c.cn)(E[j],F?"text-red-400":"text-gray-400"),children:N})}),(0,s.jsx)("input",{ref:S,id:T,name:P,type:D,value:m,onChange:p,onFocus:e=>{L(!0),u?.(e)},onBlur:e=>{L(!1),x?.(e)},placeholder:a,required:h,disabled:b,readOnly:w,className:(0,c.cn)("block w-full border rounded-lg transition-all duration-200","focus:outline-none focus:ring-2 focus:ring-offset-0","placeholder:text-gray-400",{sm:"px-3 py-2 text-sm",md:"px-4 py-2.5 text-sm",lg:"px-4 py-3 text-base"}[j],N&&"pl-10",(v||"password"===r)&&"pr-10",!b&&!w&&[F?["border-red-300 text-red-900","focus:border-red-500 focus:ring-red-500"]:M?["border-green-300 text-green-900","focus:border-green-500 focus:ring-green-500"]:["border-gray-300 text-gray-900","focus:border-primary-500 focus:ring-primary-500","hover:border-gray-400"]],b&&["bg-gray-50 border-gray-200 text-gray-500","cursor-not-allowed"],w&&["bg-gray-50 border-gray-200","cursor-default"],A),...R}),(0,s.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:"password"===r?(0,s.jsx)("button",{type:"button",onClick:()=>{B(!z)},className:(0,c.cn)("text-gray-400 hover:text-gray-600 focus:outline-none",E[j]),"aria-label":z?"Ascunde parola":"Arată parola",children:z?(0,s.jsx)(t.A,{className:E[j]}):(0,s.jsx)(l.A,{className:E[j]})}):F?(0,s.jsx)(o.A,{className:(0,c.cn)(E[j],"text-red-400")}):M?(0,s.jsx)(n.A,{className:(0,c.cn)(E[j],"text-green-400")}):v?(0,s.jsx)("span",{className:(0,c.cn)(E[j],"text-gray-400"),children:v}):null})]}),(g||y||f)&&(0,s.jsxs)("div",{className:"mt-2 flex items-start space-x-1",children:[(g||y)&&(0,s.jsx)("span",{className:"flex-shrink-0 mt-0.5",children:g?(0,s.jsx)(o.A,{className:"h-4 w-4 text-red-400"}):(0,s.jsx)(n.A,{className:"h-4 w-4 text-green-400"})}),f&&!g&&!y&&(0,s.jsx)(i.A,{className:"h-4 w-4 text-gray-400 flex-shrink-0 mt-0.5"}),(0,s.jsx)("p",{className:(0,c.cn)("text-sm",g?"text-red-600":y?"text-green-600":"text-gray-600"),children:g||y||f})]})]})});m.displayName="Input",(0,d.forwardRef)((e,r)=>(0,s.jsx)(m,{ref:r,type:"email",placeholder:"<EMAIL>",...e})).displayName="EmailInput",(0,d.forwardRef)((e,r)=>(0,s.jsx)(m,{ref:r,type:"password",placeholder:"••••••••",...e})).displayName="PasswordInput",(0,d.forwardRef)(({min:e,max:r,step:a=1,...t},l)=>(0,s.jsx)(m,{ref:l,type:"number",min:e,max:r,step:a,...t})).displayName="NumberInput",(0,d.forwardRef)(({onSearch:e,...r},a)=>(0,s.jsx)(m,{ref:a,type:"search",placeholder:"Caută...",onKeyDown:r=>{"Enter"===r.key&&e&&e(r.currentTarget.value)},...r})).displayName="SearchInput",(0,d.forwardRef)((e,r)=>(0,s.jsx)(m,{ref:r,type:"tel",placeholder:"+40 123 456 789",...e})).displayName="PhoneInput",(0,d.forwardRef)((e,r)=>(0,s.jsx)(m,{ref:r,type:"url",placeholder:"https://exemplu.com",...e})).displayName="UrlInput",(0,d.forwardRef)((e,r)=>(0,s.jsx)(m,{ref:r,type:"date",...e})).displayName="DateInput",(0,d.forwardRef)((e,r)=>(0,s.jsx)(m,{ref:r,type:"time",...e})).displayName="TimeInput",(0,d.forwardRef)((e,r)=>(0,s.jsx)(m,{ref:r,type:"datetime-local",...e})).displayName="DateTimeInput";const p=m}}]);