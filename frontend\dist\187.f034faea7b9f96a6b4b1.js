"use strict";(self.webpackChunkexpense_tracker_frontend=self.webpackChunkexpense_tracker_frontend||[]).push([[187],{125:(e,r,o)=>{o.d(r,{Ay:()=>s});var t=o(4848),a=(o(6540),o(2392));o(9264);const s=({children:e,className:r="",shadow:o=!0,border:s=!0,rounded:n=!0,padding:i=!0,hover:c=!1,clickable:l=!1,onClick:d,...p})=>{const g=l||Bo<PERSON>an(d);return(0,t.jsx)("div",{className:(0,a.cn)("bg-white",o&&"shadow-sm",s&&"border border-gray-200",n&&"rounded-lg",i&&"p-6",c&&"hover:shadow-md transition-shadow duration-200",g&&["cursor-pointer","hover:shadow-md hover:border-gray-300","transition-all duration-200","focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"],r),onClick:d,role:g?"button":void 0,tabIndex:g?0:void 0,onKeyDown:g?e=>{"Enter"!==e.key&&" "!==e.key||(e.preventDefault(),d?.(e))}:void 0,...p,children:e})}},6103:(e,r,o)=>{o.d(r,{Ay:()=>n});var t=o(4848),a=(o(6540),o(2392)),s=o(9264);const n=({children:e,variant:r="primary",size:o="md",loading:n=!1,disabled:i=!1,fullWidth:c=!1,leftIcon:l=null,rightIcon:d=null,className:p="",onClick:g,type:h="button",...u})=>{const b={primary:["bg-primary-600 text-white border-primary-600","hover:bg-primary-700 hover:border-primary-700","focus:ring-primary-500","disabled:bg-primary-300 disabled:border-primary-300"].join(" "),secondary:["bg-gray-600 text-white border-gray-600","hover:bg-gray-700 hover:border-gray-700","focus:ring-gray-500","disabled:bg-gray-300 disabled:border-gray-300"].join(" "),outline:["bg-transparent text-primary-600 border-primary-600","hover:bg-primary-50 hover:text-primary-700","focus:ring-primary-500","disabled:text-primary-300 disabled:border-primary-300"].join(" "),ghost:["bg-transparent text-gray-700 border-transparent","hover:bg-gray-100 hover:text-gray-900","focus:ring-gray-500","disabled:text-gray-400"].join(" "),danger:["bg-red-600 text-white border-red-600","hover:bg-red-700 hover:border-red-700","focus:ring-red-500","disabled:bg-red-300 disabled:border-red-300"].join(" "),success:["bg-green-600 text-white border-green-600","hover:bg-green-700 hover:border-green-700","focus:ring-green-500","disabled:bg-green-300 disabled:border-green-300"].join(" "),warning:["bg-yellow-600 text-white border-yellow-600","hover:bg-yellow-700 hover:border-yellow-700","focus:ring-yellow-500","disabled:bg-yellow-300 disabled:border-yellow-300"].join(" "),white:["bg-white text-gray-900 border-white","hover:bg-gray-50 hover:text-gray-900","focus:ring-gray-500","disabled:bg-gray-100 disabled:text-gray-400"].join(" ")},y={xs:"h-3 w-3",sm:"h-4 w-4",md:"h-4 w-4",lg:"h-5 w-5",xl:"h-6 w-6"},m=i||n;return(0,t.jsxs)("button",{type:h,onClick:e=>{m?e.preventDefault():g?.(e)},disabled:m,className:(0,a.cn)("inline-flex items-center justify-center","border font-medium rounded-lg","transition-all duration-200 ease-in-out","focus:outline-none focus:ring-2 focus:ring-offset-2","disabled:cursor-not-allowed disabled:opacity-60",b[r],{xs:"px-2 py-1 text-xs",sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base",xl:"px-8 py-4 text-lg"}[o],c&&"w-full",p),...u,children:[l&&!n&&(0,t.jsx)("span",{className:(0,a.cn)(y[o],e&&"mr-2"),children:l}),n&&(0,t.jsx)("span",{className:(0,a.cn)(e&&"mr-2"),children:(0,t.jsx)(s.Ay,{size:"xs"===o||"sm"===o?"sm":"md",color:"currentColor"})}),e&&(0,t.jsx)("span",{className:n?"opacity-70":"",children:e}),d&&!n&&(0,t.jsx)("span",{className:(0,a.cn)(y[o],e&&"ml-2"),children:d})]})}},6668:(e,r,o)=>{o.d(r,{A:()=>n});var t=o(1083);const a=("undefined"!=typeof process&&process.env,"http://localhost:3000/api"),s=t.A.create({baseURL:a,timeout:1e4,headers:{"Content-Type":"application/json"}});s.interceptors.request.use(e=>{const r=localStorage.getItem("accessToken");return r&&(e.headers.Authorization=`Bearer ${r}`),e},e=>Promise.reject(e)),s.interceptors.response.use(e=>e,e=>(401===e.response?.status&&(localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("user"),window.location.href="/login"),403===e.response?.status&&console.error("Acces interzis:",e.response.data?.message),e.response?.status>=500&&console.error("Eroare de server:",e.response.data?.message),Promise.reject(e)));const n=s},6821:(e,r,o)=>{o.d(r,{Rj:()=>i});var t=o(7097),a=o(888),s=o(6668);const n=new class{async exportExpenses(e,r={}){try{const o=new URLSearchParams(r);return(await s.A.get(`/export/${e}?${o}`,{responseType:"blob"})).data}catch(e){throw console.error("Eroare la exportul cheltuielilor:",e),e}}async getExpenses(e={}){try{const r=new URLSearchParams(e);return(await s.A.get(`/expenses?${r}`)).data}catch(e){throw console.error("Eroare la obținerea cheltuielilor:",e),e}}async createExpense(e){try{return(await s.A.post("/expenses",e)).data}catch(e){throw console.error("Eroare la crearea cheltuielii:",e),e}}async updateExpense(e,r){try{return(await s.A.put(`/expenses/${e}`,r)).data}catch(e){throw console.error("Eroare la actualizarea cheltuielii:",e),e}}async deleteExpense(e){try{await s.A.delete(`/expenses/${e}`)}catch(e){throw console.error("Eroare la ștergerea cheltuielii:",e),e}}async getExpenseStats(e={}){try{const r=new URLSearchParams(e);return(await s.A.get(`/expenses/stats?${r}`)).data}catch(e){throw console.error("Eroare la obținerea statisticilor:",e),e}}};function i(){return(0,t.n)({mutationFn:({format:e,params:r})=>n.exportExpenses(e,r),onSuccess:(e,{format:r})=>{let o="csv",t="text/csv";"pdf"===r?(o="pdf",t="application/pdf"):"excel"===r&&(o="xlsx",t="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");const s=window.URL.createObjectURL(new Blob([e],{type:t})),n=document.createElement("a");n.href=s,n.download=`cheltuieli_export_${(new Date).toISOString().split("T")[0]}.${o}`,document.body.appendChild(n),n.click(),document.body.removeChild(n),window.URL.revokeObjectURL(s),a.oR.success(`Exportul ${r.toUpperCase()} a fost descărcat cu succes`)},onError:e=>{const r=e.response?.data?.message||"Eroare la exportul cheltuielilor";a.oR.error(r)}})}}}]);