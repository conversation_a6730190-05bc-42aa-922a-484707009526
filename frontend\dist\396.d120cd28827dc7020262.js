"use strict";(self.webpackChunkexpense_tracker_frontend=self.webpackChunkexpense_tracker_frontend||[]).push([[396],{2396:(e,a,s)=>{s.r(a),s.d(a,{default:()=>f});var t=s(2792),i=s(2171),l=s(4425),n=s(4576),r=s(3211),c=s(9654),d=s(6156),x=s(6540),o=s(9582),m=s(6103),u=s(125),h=s(8724),v=(s(6215),s(9264)),p=s(2552),g=s(1235),j=s(5191),y=s(4477),b=s(2392),N=s(4848);const f=()=>{const[e,a]=(0,x.useState)(""),[s,f]=(0,x.useState)("all"),[A,k]=(0,x.useState)("all"),[C,S]=(0,x.useState)(1),[w,D]=(0,x.useState)(null),[z,L]=(0,x.useState)(!1),[P,I]=(0,x.useState)(!1),[R,O]=(0,x.useState)(""),{data:B,isLoading:_,error:U}=(0,y.Q2)({page:C,search:e,status:"all"!==s?s:void 0,plan:"all"!==A?A:void 0,limit:10}),{data:E}=(0,y.fZ)(w?.id,{enabled:!!w?.id}),V=(0,y.d)(),W=(0,y.l4)(),F=(0,y.Xi)(),M=(0,y.gI)(),Q=(e,a)=>{D(e),O(a),I(!0)},T=e=>{const a={active:{variant:"success",label:"Activ"},inactive:{variant:"secondary",label:"Inactiv"},canceled:{variant:"error",label:"Anulat"},paused:{variant:"warning",label:"Suspendat"},past_due:{variant:"error",label:"Întârziat"},trialing:{variant:"info",label:"Perioadă de probă"}},s=a[e]||a.inactive;return(0,N.jsx)(o.Wh,{variant:s.variant,children:s.label})},G=e=>{const a={basic:{variant:"primary",label:"Basic",color:"bg-blue-100 text-blue-800"},premium:{variant:"success",label:"Premium",color:"bg-green-100 text-green-800"},enterprise:{variant:"warning",label:"Enterprise",color:"bg-yellow-100 text-yellow-800"}}[e?.toLowerCase()]||{variant:"secondary",label:e||"Unknown"};return(0,N.jsx)(o.Wh,{variant:a.variant,children:a.label})},X=[{key:"user",label:"Utilizator",render:e=>(0,N.jsx)("div",{className:"flex items-center",children:(0,N.jsxs)("div",{className:"ml-0",children:[(0,N.jsxs)("div",{className:"text-sm font-medium text-gray-900",children:[e.user?.firstName," ",e.user?.lastName]}),(0,N.jsx)("div",{className:"text-sm text-gray-500",children:e.user?.email})]})})},{key:"plan",label:"Plan",render:e=>(0,N.jsxs)("div",{children:[G(e.plan?.name),(0,N.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:[(0,b.vv)(e.plan?.price||0),"/lună"]})]})},{key:"status",label:"Status",render:e=>T(e.status)},{key:"billing",label:"Facturare",render:e=>(0,N.jsxs)("div",{className:"text-sm",children:[(0,N.jsx)("div",{className:"font-medium text-gray-900",children:"monthly"===e.billingCycle?"Lunar":"Anual"}),(0,N.jsxs)("div",{className:"text-gray-500",children:["Următoarea: ",e.nextBillingDate?new Date(e.nextBillingDate).toLocaleDateString("ro-RO"):"N/A"]})]})},{key:"revenue",label:"Venituri",render:e=>(0,N.jsxs)("div",{className:"text-sm",children:[(0,N.jsx)("div",{className:"font-medium text-gray-900",children:(0,b.vv)(e.totalRevenue||0)}),(0,N.jsxs)("div",{className:"text-gray-500",children:[e.paymentsCount||0," plăți"]})]})},{key:"dates",label:"Perioada",render:e=>(0,N.jsxs)("div",{className:"text-sm",children:[(0,N.jsxs)("div",{className:"text-gray-900",children:["Început: ",new Date(e.startDate).toLocaleDateString("ro-RO")]}),e.endDate&&(0,N.jsxs)("div",{className:"text-gray-500",children:["Sfârșit: ",new Date(e.endDate).toLocaleDateString("ro-RO")]})]})},{key:"actions",label:"Acțiuni",render:e=>{const a="active"===e.status,s="paused"===e.status,d=["active","paused"].includes(e.status),x=[{label:"Vezi detalii",icon:t.A,onClick:()=>{D(e),L(!0)}}];return a&&x.push({label:"Suspendă",icon:i.A,onClick:()=>Q(e,"suspend"),className:"text-yellow-600"}),s&&x.push({label:"Reactivează",icon:l.A,onClick:()=>Q(e,"reactivate"),className:"text-green-600"}),d&&x.push({label:"Anulează",icon:n.A,onClick:()=>Q(e,"cancel"),className:"text-red-600"}),x.push({label:"Sincronizează cu Stripe",icon:r.A,onClick:()=>Q(e,"sync")}),(0,N.jsx)(h.Wy,{trigger:(0,N.jsx)(m.Ay,{variant:"ghost",size:"sm",children:(0,N.jsx)(c.A,{className:"h-4 w-4"})}),items:x})}}];if(_)return(0,N.jsx)(u.Ay,{className:"p-6",children:(0,N.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,N.jsx)(v.Ay,{size:"lg"})})});if(U)return(0,N.jsx)(u.Ay,{className:"p-6",children:(0,N.jsx)("div",{className:"text-center text-red-600",children:"Eroare la încărcarea abonamentelor"})});const Z=B?.subscriptions||[],q=Math.ceil((B?.total||0)/10);return(0,N.jsxs)(N.Fragment,{children:[(0,N.jsxs)(u.Ay,{className:"p-6",children:[(0,N.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,N.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Gestionarea abonamentelor"}),(0,N.jsxs)("div",{className:"text-sm text-gray-500",children:[B?.total||0," abonamente"]})]}),(0,N.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,N.jsxs)("div",{className:"flex gap-2",children:[(0,N.jsx)("input",{type:"text",placeholder:"Caută utilizatori...",value:e,onChange:e=>a(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500"}),(0,N.jsxs)("select",{value:s,onChange:e=>f(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500",children:[(0,N.jsx)("option",{value:"all",children:"Toate statusurile"}),(0,N.jsx)("option",{value:"active",children:"Activ"}),(0,N.jsx)("option",{value:"paused",children:"Suspendat"}),(0,N.jsx)("option",{value:"canceled",children:"Anulat"}),(0,N.jsx)("option",{value:"past_due",children:"Întârziat"})]}),(0,N.jsxs)("select",{value:A,onChange:e=>k(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500",children:[(0,N.jsx)("option",{value:"all",children:"Toate planurile"}),(0,N.jsx)("option",{value:"basic",children:"Basic"}),(0,N.jsx)("option",{value:"premium",children:"Premium"}),(0,N.jsx)("option",{value:"enterprise",children:"Enterprise"})]})]}),(0,N.jsxs)("div",{className:"flex gap-4 text-sm",children:[(0,N.jsxs)("div",{className:"text-center",children:[(0,N.jsx)("div",{className:"font-semibold text-green-600",children:B?.stats?.active||0}),(0,N.jsx)("div",{className:"text-gray-500",children:"Active"})]}),(0,N.jsxs)("div",{className:"text-center",children:[(0,N.jsx)("div",{className:"font-semibold text-yellow-600",children:B?.stats?.paused||0}),(0,N.jsx)("div",{className:"text-gray-500",children:"Suspendate"})]}),(0,N.jsxs)("div",{className:"text-center",children:[(0,N.jsx)("div",{className:"font-semibold text-red-600",children:B?.stats?.canceled||0}),(0,N.jsx)("div",{className:"text-gray-500",children:"Anulate"})]})]})]}),(0,N.jsx)(j.bQ,{columns:X,data:Z,emptyMessage:"Nu au fost găsite abonamente"}),q>1&&(0,N.jsx)("div",{className:"mt-6",children:(0,N.jsx)(g.nw,{currentPage:C,totalPages:q,onPageChange:S})})]}),(0,N.jsx)(p.Ay,{isOpen:z,onClose:()=>L(!1),title:"Detalii abonament",size:"lg",children:(E||w)&&(0,N.jsxs)("div",{className:"space-y-6",children:[(0,N.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,N.jsxs)("div",{children:[(0,N.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Utilizator"}),(0,N.jsxs)("p",{className:"text-sm text-gray-900",children:[(E||w).user?.firstName," ",(E||w).user?.lastName]}),(0,N.jsx)("p",{className:"text-xs text-gray-500",children:(E||w).user?.email})]}),(0,N.jsxs)("div",{children:[(0,N.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Plan"}),(0,N.jsxs)("div",{className:"flex items-center space-x-2",children:[G((E||w).plan?.name),(0,N.jsxs)("span",{className:"text-sm text-gray-600",children:[(0,b.vv)((E||w).plan?.price||0),"/lună"]})]})]}),(0,N.jsxs)("div",{children:[(0,N.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),T((E||w).status)]}),(0,N.jsxs)("div",{children:[(0,N.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Ciclu de facturare"}),(0,N.jsx)("p",{className:"text-sm text-gray-900",children:"monthly"===(E||w).billingCycle?"Lunar":"Anual"})]}),(0,N.jsxs)("div",{children:[(0,N.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Data începerii"}),(0,N.jsx)("p",{className:"text-sm text-gray-900",children:new Date((E||w).startDate).toLocaleDateString("ro-RO")})]}),(0,N.jsxs)("div",{children:[(0,N.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Următoarea facturare"}),(0,N.jsx)("p",{className:"text-sm text-gray-900",children:(E||w).nextBillingDate?new Date((E||w).nextBillingDate).toLocaleDateString("ro-RO"):"N/A"})]})]}),(0,N.jsxs)("div",{className:"border-t pt-4",children:[(0,N.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Statistici financiare"}),(0,N.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-center",children:[(0,N.jsxs)("div",{children:[(0,N.jsx)("p",{className:"text-lg font-semibold text-gray-900",children:(0,b.vv)((E||w).totalRevenue||0)}),(0,N.jsx)("p",{className:"text-xs text-gray-500",children:"Venituri totale"})]}),(0,N.jsxs)("div",{children:[(0,N.jsx)("p",{className:"text-lg font-semibold text-gray-900",children:(E||w).paymentsCount||0}),(0,N.jsx)("p",{className:"text-xs text-gray-500",children:"Plăți efectuate"})]}),(0,N.jsxs)("div",{children:[(0,N.jsx)("p",{className:"text-lg font-semibold text-gray-900",children:(E||w).failedPayments||0}),(0,N.jsx)("p",{className:"text-xs text-gray-500",children:"Plăți eșuate"})]})]})]}),(E||w).stripeSubscriptionId&&(0,N.jsxs)("div",{className:"border-t pt-4",children:[(0,N.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Informații Stripe"}),(0,N.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,N.jsxs)("p",{children:["ID Abonament: ",(E||w).stripeSubscriptionId]}),(E||w).stripeCustomerId&&(0,N.jsxs)("p",{children:["ID Client: ",(E||w).stripeCustomerId]})]})]})]})}),(0,N.jsx)(p.Ay,{isOpen:P,onClose:()=>I(!1),title:"Confirmă acțiunea",children:(0,N.jsxs)("div",{className:"space-y-4",children:[(0,N.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,N.jsx)(d.A,{className:"h-6 w-6 text-yellow-600"}),(0,N.jsxs)("p",{className:"text-sm text-gray-700",children:["Ești sigur că vrei să ","suspend"===R?"suspenzi":"reactivate"===R?"reactivezi":"cancel"===R?"anulezi":"sync"===R?"sincronizezi":"modifici"," acest abonament?"]})]}),(0,N.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,N.jsx)(m.Ay,{variant:"secondary",onClick:()=>I(!1),children:"Anulează"}),(0,N.jsx)(m.Ay,{variant:"cancel"===R?"error":"primary",onClick:async()=>{if(w&&R)try{switch(R){case"suspend":await V.mutateAsync(w.id);break;case"reactivate":await W.mutateAsync(w.id);break;case"cancel":await F.mutateAsync(w.id);break;case"sync":await M.mutateAsync(w.id)}I(!1),D(null)}catch(e){console.error("Action failed:",e)}},disabled:V.isLoading||W.isLoading||F.isLoading||M.isLoading,children:V.isLoading||W.isLoading||F.isLoading||M.isLoading?"Se procesează...":"Confirmă"})]})]})})]})}}}]);