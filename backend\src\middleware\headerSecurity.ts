import { Request, Response, NextFunction } from 'express';
import { randomUUID } from 'crypto';
import logger from '../utils/logger';

// Interface for authenticated request
interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    [key: string]: any;
  };
  id?: string;
}

/**
 * Middleware pentru validarea și sanitizarea header-elor
 */
export const headerSecurity = (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
  // Verifică User-Agent suspect
  const userAgent = req.get('User-Agent');
  if (!userAgent || userAgent.length < 10 || userAgent.length > 500) {
    logger.warn('Suspicious user agent detected', {
      ip: req.ip,
      url: req.originalUrl,
      userAgent: userAgent || 'missing',
      userId: req.user?.id
    });
  }

  // Verifică header-e personalizate suspecte
  const suspiciousHeaders = ['x-forwarded-host', 'x-original-url', 'x-rewrite-url'];
  for (const header of suspiciousHeaders) {
    if (req.get(header)) {
      logger.warn('Suspicious header detected', {
        ip: req.ip,
        url: req.originalUrl,
        header,
        value: req.get(header),
        userId: req.user?.id
      });
    }
  }

  // Adaugă header-e de securitate personalizate
  res.set({
    'X-Request-ID': req.id || randomUUID(),
    'X-Response-Time': Date.now().toString(),
    'X-Content-Type-Options': 'nosniff',
    'X-Download-Options': 'noopen',
    'X-Permitted-Cross-Domain-Policies': 'none'
  });

  next();
};

export default headerSecurity;