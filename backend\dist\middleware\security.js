"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.botDetection = exports.antiClickjacking = exports.headerSecurity = exports.attackDetection = exports.createSlowDown = exports.createRateLimiter = exports.helmetConfig = exports.corsOptions = exports.setupSecurity = void 0;
const helmet_1 = __importDefault(require("helmet"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const express_slow_down_1 = __importDefault(require("express-slow-down"));
const hpp_1 = __importDefault(require("hpp"));
const express_mongo_sanitize_1 = __importDefault(require("express-mongo-sanitize"));
const xss_clean_1 = __importDefault(require("xss-clean"));
const cors_1 = __importDefault(require("cors"));
const crypto_1 = __importDefault(require("crypto"));
const logger_1 = __importDefault(require("../utils/logger"));
const corsOptions = {
    origin: function (origin, callback) {
        const allowedOrigins = [
            'http://localhost:3000',
            'http://localhost:3001',
            'https://finance-app.com',
            'https://www.finance-app.com'
        ];
        if (!origin)
            return callback(null, true);
        if (allowedOrigins.indexOf(origin) !== -1) {
            callback(null, true);
        }
        else {
            logger_1.default.logSecurity('cors_violation', {
                origin,
                timestamp: new Date().toISOString()
            });
            callback(new Error('Nu este permis de politica CORS'));
        }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: [
        'Origin',
        'X-Requested-With',
        'Content-Type',
        'Accept',
        'Authorization',
        'X-API-Key',
        'X-Request-ID'
    ],
    exposedHeaders: ['X-Total-Count', 'X-Page-Count'],
    maxAge: 86400
};
exports.corsOptions = corsOptions;
const helmetConfig = {
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
            fontSrc: ["'self'", 'https://fonts.gstatic.com'],
            imgSrc: ["'self'", 'data:', 'https:'],
            scriptSrc: ["'self'"],
            connectSrc: ["'self'", 'https://api.stripe.com'],
            frameSrc: ["'none'"],
            objectSrc: ["'none'"],
            upgradeInsecureRequests: [],
        },
    },
    crossOriginEmbedderPolicy: false,
    hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true
    },
    noSniff: true,
    frameguard: { action: 'deny' },
    xssFilter: true,
    referrerPolicy: { policy: 'strict-origin-when-cross-origin' }
};
exports.helmetConfig = helmetConfig;
const createRateLimiter = (windowMs, max, message, skipSuccessfulRequests = false) => {
    return (0, express_rate_limit_1.default)({
        windowMs,
        max,
        message: {
            success: false,
            error: {
                message,
                retryAfter: Math.ceil(windowMs / 1000)
            }
        },
        standardHeaders: true,
        legacyHeaders: false,
        skipSuccessfulRequests,
        handler: (req, res) => {
            logger_1.default.logSecurity('rate_limit_exceeded', {
                ip: req.ip,
                url: req.originalUrl,
                method: req.method,
                userAgent: req.get('User-Agent'),
                userId: req.user?.id
            });
            res.status(429).json({
                success: false,
                error: {
                    message,
                    retryAfter: Math.ceil(windowMs / 1000)
                }
            });
        }
    });
};
exports.createRateLimiter = createRateLimiter;
const createSlowDown = (windowMs, delayAfter, delayMs) => {
    return (0, express_slow_down_1.default)({
        windowMs,
        delayAfter,
        delayMs,
        maxDelayMs: delayMs * 10
    });
};
exports.createSlowDown = createSlowDown;
const attackDetection = (req, res, next) => {
    const suspiciousPatterns = [
        /('|(\-\-)|(;)|(\||\|)|(\*|\*))/i,
        /(<script[^>]*>.*?<\/script>)|(<iframe[^>]*>.*?<\/iframe>)|(<object[^>]*>.*?<\/object>)/i,
        /(\.\.[\/\\])|(\.\.\.)/,
        /(;\s*(rm|del|format|shutdown|reboot))/i
    ];
    const checkForAttacks = (data, source) => {
        if (typeof data === 'string') {
            for (const pattern of suspiciousPatterns) {
                if (pattern.test(data)) {
                    logger_1.default.logSecurity('potential_attack_detected', {
                        ip: req.ip,
                        url: req.originalUrl,
                        method: req.method,
                        userAgent: req.get('User-Agent'),
                        userId: req.user?.id,
                        source,
                        pattern: pattern.toString(),
                        data: data.substring(0, 100)
                    });
                    return true;
                }
            }
        }
        else if (typeof data === 'object' && data !== null) {
            for (const key in data) {
                if (checkForAttacks(data[key], `${source}.${key}`)) {
                    return true;
                }
            }
        }
        return false;
    };
    checkForAttacks(req.query, 'query');
    checkForAttacks(req.body, 'body');
    checkForAttacks(req.params, 'params');
    next();
};
exports.attackDetection = attackDetection;
const headerSecurity = (req, res, next) => {
    const userAgent = req.get('User-Agent');
    if (!userAgent || userAgent.length < 10 || userAgent.length > 500) {
        logger_1.default.logSecurity('suspicious_user_agent', {
            ip: req.ip,
            url: req.originalUrl,
            userAgent: userAgent || 'missing',
            userId: req.user?.id
        });
    }
    const suspiciousHeaders = ['x-forwarded-host', 'x-original-url', 'x-rewrite-url'];
    for (const header of suspiciousHeaders) {
        if (req.get(header)) {
            logger_1.default.logSecurity('suspicious_header_detected', {
                ip: req.ip,
                url: req.originalUrl,
                header,
                value: req.get(header),
                userId: req.user?.id
            });
        }
    }
    res.set({
        'X-Request-ID': req.id || crypto_1.default.randomUUID(),
        'X-Response-Time': Date.now().toString(),
        'X-Content-Type-Options': 'nosniff',
        'X-Download-Options': 'noopen',
        'X-Permitted-Cross-Domain-Policies': 'none'
    });
    next();
};
exports.headerSecurity = headerSecurity;
const antiClickjacking = (req, res, next) => {
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('Content-Security-Policy', "frame-ancestors 'none'");
    next();
};
exports.antiClickjacking = antiClickjacking;
const botDetection = (req, res, next) => {
    const userAgent = req.get('User-Agent') || '';
    const botPatterns = [
        /bot/i,
        /crawler/i,
        /spider/i,
        /scraper/i,
        /curl/i,
        /wget/i,
        /python-requests/i,
        /postman/i
    ];
    const isBot = botPatterns.some(pattern => pattern.test(userAgent));
    if (isBot) {
        logger_1.default.logSecurity('bot_detected', {
            ip: req.ip,
            url: req.originalUrl,
            method: req.method,
            userAgent,
            userId: req.user?.id
        });
        const allowedBotRoutes = ['/health', '/api/docs', '/robots.txt'];
        if (!allowedBotRoutes.some(route => req.path.startsWith(route))) {
            res.status(403).json({
                success: false,
                error: {
                    message: 'Accesul pentru bot-uri nu este permis pe această rută'
                }
            });
            return;
        }
    }
    next();
};
exports.botDetection = botDetection;
const setupSecurity = (app) => {
    app.use((0, cors_1.default)(corsOptions));
    app.use((0, helmet_1.default)(helmetConfig));
    app.use((0, hpp_1.default)({
        whitelist: ['tags', 'categories']
    }));
    app.use((0, express_mongo_sanitize_1.default)());
    app.use((0, xss_clean_1.default)());
    app.use(headerSecurity);
    app.use(attackDetection);
    app.use(antiClickjacking);
    app.use(botDetection);
    app.use(createRateLimiter(15 * 60 * 1000, 1000, 'Prea multe cereri din această adresă IP. Încercați din nou mai târziu.'));
    app.use(createSlowDown(15 * 60 * 1000, 100, 500));
};
exports.setupSecurity = setupSecurity;
//# sourceMappingURL=security.js.map