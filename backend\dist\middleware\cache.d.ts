import { Request, Response, NextFunction } from 'express';
interface AuthenticatedRequest extends Request {
    user?: {
        id: string;
        [key: string]: any;
    };
}
interface CacheOptions {
    ttl?: number;
    prefix?: string;
    skipCache?: boolean;
    varyBy?: string[];
    condition?: (req: AuthenticatedRequest) => boolean;
}
type WarmupFunction = (req: AuthenticatedRequest) => Promise<void>;
declare const initializeRedis: () => Promise<void>;
declare const generateCacheKey: (prefix: string, req: AuthenticatedRequest, additionalParams?: Record<string, any>) => string;
declare const cache: (options?: CacheOptions) => (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
declare const userCache: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
declare const categoryCache: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
declare const expenseCache: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
declare const reportCache: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
declare const publicCache: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
declare const invalidateCache: (pattern: string) => Promise<void>;
declare const invalidators: {
    user: (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
    categories: (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
    expenses: (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
    all: (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
};
declare const cacheWarmer: (warmupFunctions: WarmupFunction[]) => (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
declare const warmupFunctions: {
    userCategories: (req: AuthenticatedRequest) => Promise<void>;
};
declare const getCacheStats: () => Promise<any>;
declare const cleanupExpiredCache: () => Promise<void>;
declare const closeCache: () => Promise<void>;
export declare const invalidateUserCache: (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
export declare const invalidateCategoryCache: (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
export declare const invalidateExpenseCache: (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
export { initializeRedis, cache, userCache, categoryCache, expenseCache, reportCache, publicCache, invalidateCache, invalidators, cacheWarmer, warmupFunctions, getCacheStats, cleanupExpiredCache, generateCacheKey, closeCache };
//# sourceMappingURL=cache.d.ts.map