const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkAdminUsers() {
  try {
    console.log('Conectare la baza de date...');
    
    // Găsește toți utilizatorii cu rolul de admin
    const adminUsers = await prisma.user.findMany({
      where: {
        role: 'admin'
      },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        is_active: true,
        created_at: true,
        last_login: true
      }
    });

    console.log('\n=== UTILIZATORI CU ROLUL DE ADMIN ===');
    console.log(`Total utilizatori admin: ${adminUsers.length}`);
    
    if (adminUsers.length === 0) {
      console.log('❌ Nu există utilizatori cu rolul de admin!');
    } else {
      adminUsers.forEach((user, index) => {
        console.log(`\n${index + 1}. Utilizator Admin:`);
        console.log(`   ID: ${user.id}`);
        console.log(`   Email: ${user.email}`);
        console.log(`   Nume: ${user.name}`);
        console.log(`   Rol: ${user.role}`);
        console.log(`   Activ: ${user.is_active}`);
        console.log(`   Creat la: ${user.created_at}`);
        console.log(`   Ultima autentificare: ${user.last_login || 'Niciodată'}`);
      });
    }
    
    // Verifică și <NAME_EMAIL>
    const specificAdmin = await prisma.user.findUnique({
      where: {
        email: '<EMAIL>'
      },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        is_active: true,
        created_at: true,
        last_login: true
      }
    });
    
    console.log('\n=== <NAME_EMAIL> ===');
    if (specificAdmin) {
      console.log('✅ Utilizatorul <EMAIL> există!');
      console.log(`   ID: ${specificAdmin.id}`);
      console.log(`   Nume: ${specificAdmin.name}`);
      console.log(`   Rol: ${specificAdmin.role}`);
      console.log(`   Activ: ${specificAdmin.is_active}`);
      
      if (specificAdmin.role === 'admin') {
        console.log('✅ Utilizatorul are rolul de admin!');
      } else {
        console.log(`❌ Utilizatorul are rolul '${specificAdmin.role}' în loc de 'admin'!`);
      }
    } else {
      console.log('❌ Utilizatorul <EMAIL> nu există!');
    }
    
  } catch (error) {
    console.error('❌ Eroare la verificarea utilizatorilor admin:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkAdminUsers();