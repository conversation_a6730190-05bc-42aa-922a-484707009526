import { PrismaClient } from '@prisma/client';
declare const prisma: PrismaClient<{
    log: ("info" | "error" | "query" | "warn")[];
    errorFormat: "pretty";
}, never, import("@prisma/client/runtime/library").DefaultArgs>;
declare const testConnection: () => Promise<boolean>;
declare const initializeDatabase: () => Promise<boolean>;
declare const closeConnection: () => Promise<void>;
export { prisma, testConnection, initializeDatabase, closeConnection, };
//# sourceMappingURL=prisma.d.ts.map