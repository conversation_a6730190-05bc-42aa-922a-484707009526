import { validationResult } from 'express-validator';
import { Request, Response } from 'express';
import { prisma } from '../config/prisma';
import stripeService from '../services/stripeService';
import subscriptionService from '../services/subscriptionService';
import usageService from '../services/usageService';
import logger from '../utils/logger';

interface AuthenticatedRequest extends Request {
  user: {
    id: string;
    email: string;
    stripe_customer_id?: string;
  };
}

class SubscriptionController {
  /**
   * Obține planurile disponibile
   */
  async getPlans(req: Request, res: Response) {
    try {
      const plans = await subscriptionService.getAvailablePlans();

      res.json({
        success: true,
        data: plans,
      });
    } catch (error) {
      logger.error('Error getting plans:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get plans',
      });
    }
  }

  /**
   * Obține abonamentul curent al utilizatorului
   */
  async getCurrentSubscription(req: Request, res: Response) {
    try {
      const userId = (req as any).user.id;

      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          plan_type: true,
          subscription_status: true,
          subscription_id: true,
          subscription_current_period_start: true,
          subscription_current_period_end: true,
          monthly_expense_count: true,
          monthly_expense_limit: true,
          subscription: {
            include: {
              plan: true,
            },
          },
        },
      });

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found',
        });
      }

      // Get plan from subscription or default to free
      const plan =
        user.subscription?.plan ||
        (await prisma.plan.findFirst({
          where: { stripe_id: 'free_plan' },
        }));

      if (!plan) {
        return res.status(500).json({
          success: false,
          message: 'No plan found',
        });
      }

      const permissions = plan.features;
      const usageStats = await usageService.getUserUsageStats(userId);

      return res.json({
        success: true,
        data: {
          plan: {
            id: plan.id,
            name: plan.name,
            description: plan.description,
            price: plan.price,
            currency: plan.currency,
            interval: plan.interval,
          },
          subscription_status: user.subscription_status,
          subscription_id: user.subscription_id,
          current_period_start: user.subscription_current_period_start,
          current_period_end: user.subscription_current_period_end,
          usage: usageStats.current_period,
          permissions,
          limits: plan.limits,
        },
      });
    } catch (error) {
      logger.error('Error getting current subscription:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to get subscription',
      });
    }
  }

  /**
   * Creează o sesiune de checkout pentru abonament
   */
  async createCheckoutSession(req: Request, res: Response) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array(),
        });
      }

      const { planId } = req.body;
      const userId = (req as any).user.id;
      const userEmail = (req as any).user.email;

      // Verifică dacă planul există
      const selectedPlan = await prisma.plan.findUnique({
        where: { id: planId },
      });

      if (!selectedPlan || !selectedPlan.stripe_id || selectedPlan.stripe_id === 'free_plan') {
        return res.status(400).json({
          success: false,
          message: 'Invalid plan selected',
        });
      }

      // Verifică dacă utilizatorul are deja un abonament activ
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: { subscription_status: true, email: true },
      });

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found',
        });
      }

      if (user.subscription_status === 'active') {
        return res.status(400).json({
          success: false,
          message: 'User already has an active subscription',
        });
      }

      // Creează sesiunea de checkout Stripe
      const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
      const session = await stripe.checkout.sessions.create({
        customer_email: user.email,
        payment_method_types: ['card'],
        line_items: [
          {
            price: selectedPlan.stripe_id,
            quantity: 1,
          },
        ],
        mode: 'subscription',
        success_url: `${process.env.FRONTEND_URL}/subscription/success?session_id={CHECKOUT_SESSION_ID}`,
        cancel_url: `${process.env.FRONTEND_URL}/subscription/cancel`,
        metadata: {
          userId,
          planId,
        },
      });

      return res.json({
        success: true,
        data: {
          sessionId: session.id,
          url: session.url,
        },
      });
    } catch (error) {
      logger.error('Error creating checkout session:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to create checkout session',
      });
    }
  }

  /**
   * Creează un portal pentru gestionarea abonamentului
   */
  async createCustomerPortal(req: Request, res: Response) {
    try {
      const userId = (req as any).user.id;
      const customerId = (req as any).user.stripe_customer_id;

      if (!customerId) {
        return res.status(400).json({
          success: false,
          message: 'No Stripe customer found for this user',
        });
      }

      const portalSession = await stripeService.createCustomerPortal({
        customerId,
        returnUrl: `${process.env.FRONTEND_URL}/subscription`,
      });

      return res.json({
        success: true,
        data: {
          url: portalSession.url,
        },
      });
    } catch (error) {
      logger.error('Error creating customer portal:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to create customer portal',
      });
    }
  }

  /**
   * Anulează abonamentul curent
   */
  async cancelSubscription(req: Request, res: Response) {
    try {
      const userId = (req as any).user.id;

      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          subscription_id: true,
          subscription_status: true,
        },
      });

      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found',
        });
      }

      if (!user.subscription_id || user.subscription_status !== 'active') {
        return res.status(400).json({
          success: false,
          message: 'No active subscription found',
        });
      }

      // Anulează abonamentul în Stripe
      const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
      if (!user || !user.subscription_id) {
        return res.status(400).json({
          success: false,
          message: 'No active subscription found',
        });
      }

      const canceledSubscription = await stripe.subscriptions.update(user.subscription_id, {
        cancel_at_period_end: true,
      });

      // Actualizează abonamentul în baza de date
      await prisma.user.update({
        where: { id: userId },
        data: {
          subscription_status: 'canceled',
        },
      });

      await prisma.subscription.updateMany({
        where: { user_id: userId },
        data: {
          status: 'canceled',
          canceled_at: new Date(),
        },
      });

      return res.json({
        success: true,
        message: 'Subscription will be canceled at the end of the current period',
        data: {
          subscription: canceledSubscription,
        },
      });
    } catch (error) {
      logger.error('Error canceling subscription:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to cancel subscription',
      });
    }
  }

  /**
   * Reactivează un abonament anulat
   */
  async reactivateSubscription(req: Request, res: Response) {
    try {
      const userId = (req as any).user.id;
      // Get user subscription from database
      const subscription = await prisma.subscription.findFirst({
        where: { user_id: userId },
        orderBy: { created_at: 'desc' },
      });

      if (!subscription || subscription.status !== 'canceled') {
        return res.status(400).json({
          success: false,
          message: 'No canceled subscription found',
        });
      }

      // Reactivează abonamentul în Stripe
      const reactivatedSubscription = await stripeService.reactivateSubscription(subscription.stripe_id);

      // Actualizează abonamentul în baza de date
      await prisma.subscription.updateMany({
        where: { stripe_id: subscription.stripe_id },
        data: {
          status: 'active',
          canceled_at: null,
          updated_at: new Date(),
        },
      });

      return res.json({
        success: true,
        message: 'Subscription reactivated successfully',
        data: {
          subscription: reactivatedSubscription,
        },
      });
    } catch (error) {
      logger.error('Error reactivating subscription:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to reactivate subscription',
      });
    }
  }

  /**
   * Verifică statusul unei sesiuni de checkout
   */
  async checkCheckoutSession(req: Request, res: Response) {
    try {
      const { sessionId } = req.params;

      if (!sessionId) {
        return res.status(400).json({
          success: false,
          message: 'Session ID is required',
        });
      }

      const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
      const session = await stripe.checkout.sessions.retrieve(sessionId);

      if (!session) {
        return res.status(404).json({
          success: false,
          message: 'Checkout session not found',
        });
      }

      return res.json({
        success: true,
        data: {
          status: session.payment_status,
          customerEmail: session.customer_email,
          subscriptionId: session.subscription,
        },
      });
    } catch (error) {
      logger.error('Error checking checkout session:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to check checkout session',
      });
    }
  }

  /**
   * Obține statistici de utilizare pentru utilizatorul curent
   */
  async getUsageStats(req: Request, res: Response) {
    try {
      const userId = (req as any).user.id;
      const { startDate, endDate } = req.query;

      const stats = await usageService.getUserUsageStats(userId);

      return res.json({
        success: true,
        data: stats,
      });
    } catch (error) {
      logger.error('Error getting usage stats:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to get usage statistics',
      });
    }
  }

  /**
   * Verifică dacă utilizatorul poate efectua o acțiune
   */
  async checkPermission(req: Request, res: Response) {
    try {
      const { action } = req.params;
      const userId = (req as any).user.id;

      const canPerform = await subscriptionService.canUserPerformAction(userId, action);
      const usageProgress = await usageService.getUsageProgress(userId);

      return res.json({
        success: true,
        data: {
          canPerform,
          usage: usageProgress,
        },
      });
    } catch (error) {
      logger.error('Error checking permission:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to check permission',
      });
    }
  }

  /**
   * Sincronizează planurile din Stripe (admin only)
   */
  async syncPlans(req: Request, res: Response): Promise<void> {
    try {
      await subscriptionService.syncPlansFromStripe();

      res.json({
        success: true,
        message: 'Plans synced successfully',
      });
    } catch (error) {
      logger.error('Error syncing plans:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to sync plans',
      });
    }
  }

  /**
   * Obține lista abonamentelor cu paginare și filtrare (admin only)
   */
  async getSubscriptions(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array(),
        });
      }

      const {
        page = 1,
        limit = 10,
        search = '',
        status = 'all',
        sortBy = 'created_at',
        sortOrder = 'desc',
      } = req.query;

      // Convert query parameters to proper types
      const pageNum = typeof page === 'string' ? parseInt(page) : 1;
      const limitNum = typeof limit === 'string' ? parseInt(limit) : 10;
      const searchStr = typeof search === 'string' ? search : '';
      const statusStr = typeof status === 'string' ? status : 'all';
      const sortByStr = typeof sortBy === 'string' ? sortBy : 'created_at';
      const sortOrderStr = typeof sortOrder === 'string' ? sortOrder : 'desc';

      const offset = (pageNum - 1) * limitNum;

      // Construiește filtrul
      const where: any = {};

      if (statusStr !== 'all') {
        where.status = statusStr;
      }

      if (searchStr) {
        where.OR = [
          {
            user: {
              email: {
                contains: searchStr,
                mode: 'insensitive',
              },
            },
          },
          {
            plan: {
              name: {
                contains: searchStr,
                mode: 'insensitive',
              },
            },
          },
        ];
      }

      // Obține abonamentele cu paginare
      const [subscriptions, total] = await Promise.all([
        prisma.subscription.findMany({
          where,
          include: {
            user: {
              select: {
                id: true,
                email: true,
                name: true,
              },
            },
            plan: {
              select: {
                id: true,
                name: true,
                price: true,
                currency: true,
                interval: true,
              },
            },
          },
          orderBy: {
            [sortByStr]: sortOrderStr,
          },
          skip: offset,
          take: limitNum,
        }),
        prisma.subscription.count({ where }),
      ]);

      const totalPages = Math.ceil(total / limitNum);

      return res.json({
        success: true,
        data: {
          subscriptions,
          pagination: {
            current_page: pageNum,
            total_pages: totalPages,
            total_items: total,
            items_per_page: limitNum,
            has_next: pageNum < totalPages,
            has_prev: pageNum > 1,
          },
        },
      });
    } catch (error) {
      logger.error('Error getting subscriptions:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to get subscriptions',
      });
    }
  }

  /**
   * Obține statistici despre abonamente (admin only)
   */
  async getSubscriptionStats(req: Request, res: Response): Promise<void> {
    try {
      const { startDate, endDate } = req.query;

      const subscriptionStats = await subscriptionService.getSubscriptionStats();
      const usageStats = await usageService.getGlobalUsageStats(startDate as string, endDate as string);

      res.json({
        success: true,
        data: {
          subscriptions: subscriptionStats,
          usage: usageStats,
        },
      });
    } catch (error) {
      logger.error('Error getting subscription stats:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to get subscription statistics',
      });
    }
  }

  /**
   * Sincronizează statisticile planurilor (pentru administratori)
   */
  async syncPlanStats(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      // Obține statistici pentru toate planurile
      const plans = await prisma.plan.findMany({
        include: {
          subscriptions: {
            where: {
              status: 'active',
            },
          },
        },
      });

      const stats = plans.map(plan => ({
        id: plan.id,
        name: plan.name,
        active_subscriptions: plan.subscriptions.length,
        total_revenue: plan.subscriptions.length * (Number(plan.price) || 0),
      }));

      res.json({
        success: true,
        message: 'Plan statistics retrieved successfully',
        data: stats,
      });
    } catch (error) {
      logger.error('Error syncing plan stats:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to sync plan statistics',
      });
    }
  }

  /**
   * Obține statisticile planurilor (pentru administratori)
   */
  async getPlanStats(req: AuthenticatedRequest, res: Response): Promise<Response> {
    try {
      const plans = await prisma.plan.findMany({
        include: {
          subscriptions: {
            where: {
              status: 'active',
            },
          },
          _count: {
            select: {
              subscriptions: true,
            },
          },
        },
      });

      const totalUsers = await prisma.user.count();
      const activeSubscriptions = await prisma.subscription.count({
        where: { status: 'active' },
      });

      const stats = {
        total_users: totalUsers,
        active_subscriptions: activeSubscriptions,
        plans: plans.map(plan => ({
          id: plan.id,
          name: plan.name,
          price: plan.price,
          active_subscriptions: plan.subscriptions.length,
          total_subscriptions: plan._count.subscriptions,
          monthly_revenue: plan.subscriptions.length * (Number(plan.price) || 0),
        })),
      };

      return res.json({
        success: true,
        data: stats,
      });
    } catch (error) {
      logger.error('Error getting plan stats:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to get plan statistics',
      });
    }
  }

  /**
   * Gestionează webhook-urile Stripe
   */
  async handleWebhook(req: Request, res: Response): Promise<void> {
    try {
      const sig = req.headers['stripe-signature'];
      const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;

      let event;
      try {
        const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
        event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
      } catch (err) {
        const error = err as Error;
        logger.error('Webhook signature verification failed:', error.message);
        res.status(400).send(`Webhook Error: ${error.message}`);
        return;
      }

      // Înregistrează evenimentul în baza de date
      await prisma.webhookEvent.create({
        data: {
          stripe_id: event.id,
          type: event.type,
          processed: false,
          data: event.data,
        },
      });

      // Procesează evenimentul
      switch (event.type) {
        case 'checkout.session.completed':
          await this.handleCheckoutCompleted(event.data.object);
          break;
        case 'customer.subscription.updated':
          await this.handleSubscriptionUpdated(event.data.object);
          break;
        case 'customer.subscription.deleted':
          await this.handleSubscriptionDeleted(event.data.object);
          break;
        case 'invoice.payment_succeeded':
          await this.handlePaymentSucceeded(event.data.object);
          break;
        case 'invoice.payment_failed':
          await this.handlePaymentFailed(event.data.object);
          break;
        default:
          logger.info(`Unhandled event type: ${event.type}`);
      }

      // Marchează evenimentul ca procesat
      await prisma.webhookEvent.updateMany({
        where: { stripe_event_id: event.id },
        data: { processed: true },
      });

      return res.json({ received: true });
    } catch (error) {
      logger.error('Error handling webhook:', error);
      return res.status(500).json({
        success: false,
        message: 'Webhook processing failed',
      });
    }
  }

  /**
   * Gestionează finalizarea checkout-ului
   */
  async handleCheckoutCompleted(session: any) {
    try {
      const { userId } = session.metadata;
      const { planId } = session.metadata;

      if (!userId || !planId) {
        logger.error('Missing metadata in checkout session:', session.id);
        return;
      }

      // Obține informații despre abonament din Stripe
      const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
      const subscription = await stripe.subscriptions.retrieve(session.subscription);
      const plan = await prisma.plan.findUnique({ where: { id: planId } });

      if (!plan) {
        logger.error(`Plan not found: ${planId}`);
        return;
      }

      // Creează abonamentul în baza de date
      await prisma.subscription.create({
        data: {
          user_id: userId,
          plan_id: planId,
          stripe_subscription_id: subscription.id,
          stripe_customer_id: subscription.customer,
          status: subscription.status,
          current_period_start: new Date(subscription.current_period_start * 1000),
          current_period_end: new Date(subscription.current_period_end * 1000),
        },
      });

      // Actualizează utilizatorul
      await prisma.user.update({
        where: { id: userId },
        data: {
          plan_type: plan.name.toLowerCase(),
          subscription_status: subscription.status,
          subscription_id: subscription.id,
          subscription_current_period_start: new Date(subscription.current_period_start * 1000),
          subscription_current_period_end: new Date(subscription.current_period_end * 1000),
          monthly_expense_limit: plan.limits.monthly_expenses || 1000,
        },
      });

      // Resetează utilizarea lunară
      await usageService.resetMonthlyUsage(userId);

      logger.info(`Subscription created for user ${userId}, plan ${plan.name}`);
    } catch (error) {
      logger.error('Error handling checkout completed:', error);
      throw error;
    }
  }

  /**
   * Gestionează actualizarea abonamentului
   */
  async handleSubscriptionUpdated(subscription: any) {
    try {
      // Găsește utilizatorul cu acest abonament
      const user = await prisma.user.findFirst({
        where: { subscription_id: subscription.id },
      });

      if (!user) {
        logger.error(`User not found for subscription: ${subscription.id}`);
        return;
      }

      // Actualizează abonamentul în baza de date
      await prisma.subscription.updateMany({
        where: { stripe_subscription_id: subscription.id },
        data: {
          status: subscription.status,
          current_period_start: new Date(subscription.current_period_start * 1000),
          current_period_end: new Date(subscription.current_period_end * 1000),
        },
      });

      // Actualizează utilizatorul
      await prisma.user.update({
        where: { id: user.id },
        data: {
          subscription_status: subscription.status,
          subscription_current_period_start: new Date(subscription.current_period_start * 1000),
          subscription_current_period_end: new Date(subscription.current_period_end * 1000),
        },
      });

      // Dacă abonamentul a fost anulat, retrogradează la planul gratuit
      if (subscription.status === 'canceled') {
        await subscriptionService.downgradeToFree(user.id);
      }

      logger.info(`Subscription updated for user ${user.id}, status: ${subscription.status}`);
    } catch (error) {
      logger.error('Error handling subscription updated:', error);
      throw error;
    }
  }

  /**
   * Gestionează ștergerea abonamentului
   */
  async handleSubscriptionDeleted(subscription: any) {
    try {
      // Găsește utilizatorul cu acest abonament
      const user = await prisma.user.findFirst({
        where: { subscription_id: subscription.id },
      });

      if (!user) {
        logger.error(`User not found for subscription: ${subscription.id}`);
        return;
      }

      // Actualizează abonamentul în baza de date
      await prisma.subscription.updateMany({
        where: { stripe_subscription_id: subscription.id },
        data: {
          status: 'canceled',
          canceled_at: new Date(),
        },
      });

      // Retrogradează utilizatorul la planul gratuit
      await subscriptionService.downgradeToFree(user.id);

      logger.info(`Subscription deleted for user ${user.id}`);
    } catch (error) {
      logger.error('Error handling subscription deleted:', error);
      throw error;
    }
  }

  /**
   * Gestionează plata reușită
   */
  async handlePaymentSucceeded(invoice: any) {
    try {
      const subscriptionId = invoice.subscription;

      if (!subscriptionId) {
        return;
      }

      // Găsește utilizatorul cu acest abonament
      const user = await prisma.user.findFirst({
        where: { subscription_id: subscriptionId },
      });

      if (!user) {
        logger.error(`User not found for subscription: ${subscriptionId}`);
        return;
      }

      // Resetează utilizarea lunară la începutul unei noi perioade
      await usageService.resetMonthlyUsage(user.id);

      logger.info(`Payment succeeded for user ${user.id}, subscription ${subscriptionId}`);
    } catch (error) {
      logger.error('Error handling payment succeeded:', error);
      throw error;
    }
  }

  /**
   * Gestionează plata eșuată
   */
  async handlePaymentFailed(invoice: any) {
    try {
      const subscriptionId = invoice.subscription;

      if (!subscriptionId) {
        return;
      }

      // Găsește utilizatorul cu acest abonament
      const user = await prisma.user.findFirst({
        where: { subscription_id: subscriptionId },
      });

      if (!user) {
        logger.error(`User not found for subscription: ${subscriptionId}`);
        return;
      }

      // Actualizează statusul abonamentului
      await prisma.subscription.updateMany({
        where: { stripe_subscription_id: subscriptionId },
        data: {
          status: 'past_due',
        },
      });

      await prisma.user.update({
        where: { id: user.id },
        data: {
          subscription_status: 'past_due',
        },
      });

      logger.info(`Payment failed for user ${user.id}, subscription ${subscriptionId}`);
    } catch (error) {
      logger.error('Error handling payment failed:', error);
      throw error;
    }
  }
}

export default new SubscriptionController();
