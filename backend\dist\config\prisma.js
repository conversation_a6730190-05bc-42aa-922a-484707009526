"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.closeConnection = exports.initializeDatabase = exports.testConnection = exports.prisma = void 0;
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient({
    log: process.env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
    errorFormat: 'pretty',
});
exports.prisma = prisma;
process.on('beforeExit', async () => {
    await prisma.$disconnect();
});
process.on('SIGINT', async () => {
    await prisma.$disconnect();
    process.exit(0);
});
process.on('SIGTERM', async () => {
    await prisma.$disconnect();
    process.exit(0);
});
const testConnection = async () => {
    try {
        await prisma.$connect();
        console.log('✅ Database connected successfully');
        return true;
    }
    catch (error) {
        console.error('❌ Database connection failed:', error.message);
        return false;
    }
};
exports.testConnection = testConnection;
const initializeDatabase = async () => {
    try {
        await testConnection();
        console.log('📊 Database initialized successfully');
        return true;
    }
    catch (error) {
        console.error('❌ Database initialization failed:', error.message);
        throw error;
    }
};
exports.initializeDatabase = initializeDatabase;
const closeConnection = async () => {
    try {
        await prisma.$disconnect();
        console.log('🔌 Database connection closed');
    }
    catch (error) {
        console.error('❌ Error closing database connection:', error.message);
    }
};
exports.closeConnection = closeConnection;
//# sourceMappingURL=prisma.js.map