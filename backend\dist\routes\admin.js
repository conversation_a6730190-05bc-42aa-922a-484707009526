"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const adminController_1 = require("../controllers/adminController");
const subscriptionController_1 = require("../controllers/subscriptionController");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
router.use(auth_1.authenticateToken);
router.use(auth_1.requireAdmin);
const validatePagination = [
    (0, express_validator_1.query)('page')
        .optional()
        .isInt({ min: 1 })
        .withMessage('Page must be a positive integer'),
    (0, express_validator_1.query)('limit')
        .optional()
        .isInt({ min: 1, max: 100 })
        .withMessage('Limit must be between 1 and 100'),
    (0, express_validator_1.query)('search')
        .optional()
        .isString()
        .trim()
        .isLength({ max: 100 })
        .withMessage('Search term must be a string with max 100 characters'),
    (0, express_validator_1.query)('status')
        .optional()
        .isIn(['all', 'active', 'inactive'])
        .withMessage('Status must be one of: all, active, inactive'),
    (0, express_validator_1.query)('timeRange')
        .optional()
        .isIn(['1h', '24h', '7d', '30d'])
        .withMessage('Time range must be one of: 1h, 24h, 7d, 30d'),
];
const validateUserId = [
    (0, express_validator_1.param)('userId')
        .isInt({ min: 1 })
        .withMessage('User ID must be a positive integer'),
];
const validateAlertId = [
    (0, express_validator_1.param)('alertId')
        .isInt({ min: 1 })
        .withMessage('Alert ID must be a positive integer'),
];
router.get('/dashboard', (req, res) => {
    res.redirect('/api/admin/dashboard/stats');
});
router.get('/dashboard/stats', adminController_1.adminController.getDashboardStats);
router.get('/alerts', adminController_1.adminController.getSystemAlerts);
router.post('/alerts/:alertId/read', validateAlertId, adminController_1.adminController.markAlertAsRead);
router.get('/users', validatePagination, adminController_1.adminController.getUsers);
router.get('/users/:userId', validateUserId, adminController_1.adminController.getUserDetails);
router.get('/subscriptions', validatePagination, subscriptionController_1.subscriptionController.getSubscriptions);
router.get('/subscriptions/stats', subscriptionController_1.subscriptionController.getSubscriptionStats);
router.get('/plans/stats', subscriptionController_1.subscriptionController.getPlanStats);
router.get('/usage/stats', adminController_1.adminController.getUsageStats);
router.get('/revenue/data', adminController_1.adminController.getRevenueData);
router.get('/activity', validatePagination, adminController_1.adminController.getActivity);
exports.default = router;
//# sourceMappingURL=admin.js.map