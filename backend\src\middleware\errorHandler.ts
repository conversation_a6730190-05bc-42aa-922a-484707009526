import { Request, Response, NextFunction } from 'express';
import logger from '../utils/logger';

// Interface for custom error
interface CustomError extends Error {
  statusCode?: number;
  code?: string | number;
  type?: string;
  errors?: any[];
  status?: number;
}

// Interface for authenticated request
interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    [key: string]: any;
  };
}

/**
 * Global error handling middleware
 * This should be the last middleware in the application
 */
const errorHandler = (
  err: CustomError,
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): void => {
  let error: { message: string; statusCode: number } = {
    message: err.message,
    statusCode: err.statusCode || 500
  };

  // Log error cu detalii structurate
  logger.error({
    error: {
      message: err.message,
      stack: err.stack,
      name: err.name,
      code: err.code
    },
    request: {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.id
    }
  });

  // Prisma Database Errors
  if (err.code && typeof err.code === 'string' && err.code.startsWith('P')) {
    switch (err.code) {
      case 'P2002':
        error = { message: 'Datele introduse există deja în sistem', statusCode: 409 };
        break;
      case 'P2014':
        error = { message: 'Datele introduse violează o restricție de relație', statusCode: 400 };
        break;
      case 'P2003':
        error = { message: 'Referința către o înregistrare inexistentă', statusCode: 400 };
        break;
      case 'P2025':
        error = { message: 'Înregistrarea nu a fost găsită', statusCode: 404 };
        break;
      case 'P2016':
        error = { message: 'Eroare de interpretare a query-ului', statusCode: 400 };
        break;
      default:
        error = { message: 'Eroare de bază de date', statusCode: 500 };
    }
  }

  // Mongoose bad ObjectId
  if (err.name === 'CastError') {
    const message = 'Resource not found';
    error = { message, statusCode: 404 };
  }

  // Mongoose duplicate key
  if (err.code === 11000) {
    const message = 'Duplicate field value entered';
    error = { message, statusCode: 400 };
  }

  // Mongoose validation error
  if (err.name === 'ValidationError' && err.errors) {
    const message = Object.values(err.errors).map((val: any) => val.message).join(', ');
    error = { message, statusCode: 400 };
  }

  // Sequelize validation error
  if (err.name === 'SequelizeValidationError' && err.errors) {
    const message = err.errors.map((e: any) => e.message).join(', ');
    error = { message, statusCode: 400 };
  }

  // Sequelize unique constraint error
  if (err.name === 'SequelizeUniqueConstraintError') {
    const message = 'Duplicate field value entered';
    error = { message, statusCode: 400 };
  }

  // Sequelize foreign key constraint error
  if (err.name === 'SequelizeForeignKeyConstraintError') {
    const message = 'Invalid reference to related resource';
    error = { message, statusCode: 400 };
  }

  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    const message = 'Token invalid. Vă rugăm să vă autentificați din nou.';
    error = { message, statusCode: 401 };
  }

  if (err.name === 'TokenExpiredError') {
    const message = 'Token expirat. Vă rugăm să vă autentificați din nou.';
    error = { message, statusCode: 401 };
  }

  // Stripe Errors
  if (err.type && err.type.startsWith('Stripe')) {
    let message = 'Eroare de procesare a plății';
    let statusCode = 400;

    switch (err.type) {
      case 'StripeCardError':
        message = `Eroare card: ${err.message}`;
        break;
      case 'StripeRateLimitError':
        message = 'Prea multe cereri către Stripe. Încercați din nou mai târziu.';
        statusCode = 429;
        break;
      case 'StripeInvalidRequestError':
        message = 'Cerere invalidă către Stripe';
        break;
      case 'StripeAPIError':
        message = 'Eroare internă Stripe';
        statusCode = 500;
        break;
      case 'StripeConnectionError':
        message = 'Eroare de conexiune cu Stripe';
        statusCode = 503;
        break;
      case 'StripeAuthenticationError':
        message = 'Eroare de autentificare Stripe';
        statusCode = 401;
        break;
      default:
        message = 'Eroare de procesare a plății';
    }

    error = { message, statusCode };
  }

  // Multer errors (file upload)
  if (err.code === 'LIMIT_FILE_SIZE') {
    const message = 'File too large';
    error = { message, statusCode: 400 };
  }

  if (err.code === 'LIMIT_UNEXPECTED_FILE') {
    const message = 'Unexpected file field';
    error = { message, statusCode: 400 };
  }

  // Rate limiting errors
  if (err.status === 429) {
    const message = 'Too many requests, please try again later';
    error = { message, statusCode: 429 };
  }

  // Default error response
  const statusCode = error.statusCode || err.statusCode || 500;
  const message = error.message || 'Internal Server Error';

  // Don't leak error details in production
  const response: any = {
    success: false,
    error: message,
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  };

  res.status(statusCode).json(response);
};

export default errorHandler;