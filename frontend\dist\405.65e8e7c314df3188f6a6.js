"use strict";(self.webpackChunkexpense_tracker_frontend=self.webpackChunkexpense_tracker_frontend||[]).push([[405],{3573:(e,t,s)=>{s.d(t,{o:()=>i});var r=s(888),a=s(2389),l=s(8114);const i=()=>{const{t:e,i18n:t}=(0,a.Bd)(),s=(0,l.UK)(),i=(0,l.qX)(),n=t=>e(`languages.${t}`,{defaultValue:t.toUpperCase()});return{t:e,i18n:t,setLanguage:async e=>{try{await(0,l.v2)(e);const t="ro"===e?"Limba a fost schimbată cu succes!":"Language changed successfully!";r.oR.success(t)}catch(e){console.error("Error changing language:",e);const t="ro"===(0,l.UK)()?"Eroare la schimbarea limbii":"Error changing language";r.oR.error(t)}},currentLanguage:s,supportedLanguages:i,isLanguageSupported:e=>i.includes(e),getLanguageDisplayName:n,getLanguagesWithNames:()=>i.map(e=>({code:e,name:n(e)})),isRTL:()=>["ar","he","fa"].includes(s)}}},5080:(e,t,s)=>{s.r(t),s.d(t,{default:()=>k});var r=s(2957),a=s(4266),l=s(3478),i=s(4553),n=s(587),o=s(1507),d=s(8054),c=s(8624),m=s(5410),x=s(9818),g=s(7060),u=s(247),p=s(2933),h=s(6540),b=s(2389),f=s(4976);s.p;var y=s(6103),j=s(8724),v=s(3573),N=s(5009),w=s(4848);const k=function(){const{t:e}=(0,b.Bd)(),{currentLanguage:t,setLanguage:s}=(0,v.o)(),{user:k,isAuthenticated:A,logout:C}=(0,N.nc)(),[L,_]=(0,h.useState)(!1),E=(0,h.useRef)(null);(0,h.useEffect)(()=>{const e=e=>{E.current&&!E.current.contains(e.target)&&_(!1)};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]);const S=[{value:"ro",label:"Română"},{value:"en",label:"English"}],z=[{icon:r.A,title:e("landing.features.items.analytics.title"),description:e("landing.features.items.analytics.description")},{icon:a.A,title:e("landing.features.items.categories.title"),description:e("landing.features.items.categories.description")},{icon:l.A,title:e("landing.features.items.security.title"),description:e("landing.features.items.security.description")},{icon:i.A,title:e("landing.features.items.mobile.title"),description:e("landing.features.items.mobile.description")},{icon:n.A,title:e("landing.features.items.realtime.title"),description:e("landing.features.items.realtime.description")},{icon:o.A,title:e("landing.features.items.sharing.title"),description:e("landing.features.items.sharing.description")}],D=[{name:e("landing.testimonials.items.maria.name"),role:e("landing.testimonials.items.maria.role"),content:e("landing.testimonials.items.maria.content"),rating:5},{name:e("landing.testimonials.items.alexandru.name"),role:e("landing.testimonials.items.alexandru.role"),content:e("landing.testimonials.items.alexandru.content"),rating:5},{name:e("landing.testimonials.items.elena.name"),role:e("landing.testimonials.items.elena.role"),content:e("landing.testimonials.items.elena.content"),rating:5}],F=[{name:e("landing.pricing.plans.free.name"),price:"0",period:e("landing.pricing.plans.free.period"),features:[e("landing.pricing.plans.free.features.transactions"),e("landing.pricing.plans.free.features.categories"),e("landing.pricing.plans.free.features.reports"),e("landing.pricing.plans.free.features.support")],popular:!1},{name:e("landing.pricing.plans.pro.name"),price:"29",period:e("landing.pricing.plans.pro.period"),features:[e("landing.pricing.plans.pro.features.transactions"),e("landing.pricing.plans.pro.features.categories"),e("landing.pricing.plans.pro.features.reports"),e("landing.pricing.plans.pro.features.export"),e("landing.pricing.plans.pro.features.support"),e("landing.pricing.plans.pro.features.backup")],popular:!0},{name:e("landing.pricing.plans.business.name"),price:"99",period:e("landing.pricing.plans.business.period"),features:[e("landing.pricing.plans.business.features.allPro"),e("landing.pricing.plans.business.features.multipleAccounts"),e("landing.pricing.plans.business.features.apiAccess"),e("landing.pricing.plans.business.features.integrations"),e("landing.pricing.plans.business.features.manager"),e("landing.pricing.plans.business.features.sla")],popular:!1}],O=[{number:e("landing.stats.activeUsers.number"),label:e("landing.stats.activeUsers.label")},{number:e("landing.stats.expensesTracked.number"),label:e("landing.stats.expensesTracked.label")},{number:e("landing.stats.averageSavings.number"),label:e("landing.stats.averageSavings.label")},{number:e("landing.stats.uptime.number"),label:e("landing.stats.uptime.label")}];return(0,w.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,w.jsx)("header",{className:"bg-white shadow-sm sticky top-0 z-50",children:(0,w.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,w.jsxs)("div",{className:"flex justify-between items-center py-4",children:[(0,w.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,w.jsx)("div",{className:"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center",children:(0,w.jsx)(a.A,{className:"w-5 h-5 text-white"})}),(0,w.jsx)("span",{className:"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"FinanceFlow"})]}),(0,w.jsxs)("nav",{className:"hidden md:flex space-x-8",children:[(0,w.jsx)("a",{href:"#features",className:"text-gray-600 hover:text-blue-600 transition-colors",children:e("landing.header.features")}),(0,w.jsx)("a",{href:"#testimonials",className:"text-gray-600 hover:text-blue-600 transition-colors",children:e("landing.header.testimonials")}),(0,w.jsx)("a",{href:"#pricing",className:"text-gray-600 hover:text-blue-600 transition-colors",children:e("landing.header.pricing")})]}),(0,w.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,w.jsx)(j.l6,{value:S.find(e=>e.value===t),onChange:e=>s(e.value),options:S,className:"w-32",buttonClassName:"bg-white hover:bg-gray-50 border-gray-300 hover:border-blue-400 rounded-t-lg px-4 py-2.5 text-sm font-medium text-gray-800 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 shadow-sm hover:shadow-md",optionClassName:"hover:bg-blue-50 font-medium",menuClassName:"border-t-0 rounded-t-none rounded-b-lg border-gray-300 shadow-lg",offset:0}),A?(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)("div",{className:"relative",children:(0,w.jsx)("button",{className:"p-2 text-gray-600 hover:text-blue-600 hover:bg-gray-100 rounded-lg transition-colors",children:(0,w.jsx)(d.A,{className:"w-5 h-5"})})}),(0,w.jsxs)("div",{className:"relative",ref:E,children:[(0,w.jsxs)("button",{onClick:()=>_(!L),className:"flex items-center space-x-2 p-2 text-gray-600 hover:text-blue-600 hover:bg-gray-100 rounded-lg transition-colors",children:[(0,w.jsx)("div",{className:"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center",children:(0,w.jsx)(c.A,{className:"w-4 h-4 text-white"})}),(0,w.jsx)("span",{className:"text-sm font-medium",children:k?.firstName}),(0,w.jsx)(m.A,{className:"w-4 h-4"})]}),L&&(0,w.jsxs)("div",{className:"absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50",children:[(0,w.jsxs)("div",{className:"px-4 py-3 border-b border-gray-100",children:[(0,w.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:[k?.firstName," ",k?.lastName]}),(0,w.jsx)("p",{className:"text-sm text-gray-500",children:k?.email})]}),(0,w.jsxs)(f.N_,{to:"/app/dashboard",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>_(!1),children:[(0,w.jsx)(r.A,{className:"w-4 h-4 mr-3"}),"Dashboard"]}),(0,w.jsxs)(f.N_,{to:"/app/profile",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>_(!1),children:[(0,w.jsx)(c.A,{className:"w-4 h-4 mr-3"}),"Profil"]}),(0,w.jsxs)(f.N_,{to:"/app/settings",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors",onClick:()=>_(!1),children:[(0,w.jsx)(x.A,{className:"w-4 h-4 mr-3"}),"Setări"]}),(0,w.jsx)("div",{className:"border-t border-gray-100 mt-2 pt-2",children:(0,w.jsxs)("button",{onClick:()=>{C(),_(!1)},className:"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors",children:[(0,w.jsx)(g.A,{className:"w-4 h-4 mr-3"}),"Deconectare"]})})]})]})]}):(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(f.N_,{to:"/login",className:"text-gray-600 hover:text-blue-600 transition-colors",children:e("landing.header.login")}),(0,w.jsx)(f.N_,{to:"/register",children:(0,w.jsx)(y.Ay,{variant:"primary",size:"sm",children:e("landing.header.register")})})]})]})]})})}),(0,w.jsxs)("section",{className:"relative bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 pt-20 pb-32 overflow-hidden",children:[(0,w.jsxs)("div",{className:"absolute inset-0",children:[(0,w.jsx)("div",{className:"absolute top-20 left-10 w-72 h-72 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse"}),(0,w.jsx)("div",{className:"absolute top-40 right-10 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-2000"}),(0,w.jsx)("div",{className:"absolute bottom-20 left-1/2 w-72 h-72 bg-indigo-300 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-pulse animation-delay-4000"})]}),(0,w.jsx)("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,w.jsxs)("div",{className:"text-center",children:[(0,w.jsx)("div",{className:"animate-fade-in-up",children:(0,w.jsxs)("h1",{className:"text-5xl md:text-6xl font-bold mb-6",children:[(0,w.jsx)("span",{className:"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:e("landing.hero.title.part1")}),(0,w.jsx)("span",{className:"block bg-gradient-to-r from-yellow-400 to-orange-400 bg-clip-text text-transparent",children:e("landing.hero.title.part2")})]})}),(0,w.jsx)("div",{className:"animate-fade-in-up animation-delay-200",children:(0,w.jsx)("p",{className:"text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed",children:e("landing.hero.subtitle")})}),(0,w.jsxs)("div",{className:"animate-fade-in-up animation-delay-400 flex flex-row gap-4 justify-center flex-wrap",children:[(0,w.jsx)(f.N_,{to:"/register",children:(0,w.jsx)(y.Ay,{variant:"primary",size:"lg",className:"w-auto whitespace-nowrap transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl",children:e("landing.hero.cta.primary")})}),(0,w.jsx)(y.Ay,{variant:"outline",size:"lg",className:"w-auto whitespace-nowrap transform hover:scale-105 transition-all duration-200 hover:shadow-lg",children:e("landing.hero.cta.secondary")})]}),(0,w.jsx)("p",{className:"text-sm text-gray-500 mt-4",children:e("landing.hero.features")})]})})]}),(0,w.jsxs)("section",{id:"features",className:"py-24 bg-white relative overflow-hidden",children:[(0,w.jsx)("div",{className:"absolute inset-0 bg-gradient-to-b from-gray-50/50 to-white"}),(0,w.jsxs)("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,w.jsxs)("div",{className:"text-center mb-16",children:[(0,w.jsx)("h2",{className:"text-4xl font-bold text-gray-900 mb-4",children:e("landing.features.title")}),(0,w.jsx)("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:e("landing.features.subtitle")})]}),(0,w.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:z.map((e,t)=>{const s=e.icon;return(0,w.jsxs)("div",{className:"group p-6 rounded-xl border border-gray-200 hover:border-blue-300 hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 bg-white/80 backdrop-blur-sm",children:[(0,w.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300",children:(0,w.jsx)(s,{className:"w-6 h-6 text-white"})}),(0,w.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors duration-300",children:e.title}),(0,w.jsx)("p",{className:"text-gray-600 leading-relaxed",children:e.description})]},t)})})]})]}),(0,w.jsxs)("section",{className:"bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 py-20 relative overflow-hidden",children:[(0,w.jsx)("div",{className:"absolute inset-0 bg-black/10"}),(0,w.jsxs)("div",{className:"absolute inset-0",children:[(0,w.jsx)("div",{className:"absolute top-0 left-1/4 w-96 h-96 bg-white/10 rounded-full filter blur-3xl"}),(0,w.jsx)("div",{className:"absolute bottom-0 right-1/4 w-96 h-96 bg-white/10 rounded-full filter blur-3xl"})]}),(0,w.jsx)("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,w.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8 text-center",children:O.map((e,t)=>(0,w.jsxs)("div",{className:"group",children:[(0,w.jsx)("div",{className:"text-5xl font-bold text-white mb-2 group-hover:scale-110 transition-transform duration-300",children:e.number}),(0,w.jsx)("div",{className:"text-blue-100 text-lg font-medium",children:e.label})]},t))})})]}),(0,w.jsx)("section",{id:"testimonials",className:"py-24 bg-gradient-to-b from-gray-50 to-white",children:(0,w.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,w.jsxs)("div",{className:"text-center mb-16",children:[(0,w.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:e("landing.testimonials.title")}),(0,w.jsx)("p",{className:"text-xl text-gray-600",children:e("landing.testimonials.subtitle")})]}),(0,w.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:D.map((e,t)=>(0,w.jsxs)("div",{className:"group bg-white p-8 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-1 border border-gray-100",children:[(0,w.jsx)("div",{className:"flex items-center mb-6",children:[...Array(e.rating)].map((e,t)=>(0,w.jsx)(u.A,{className:"w-5 h-5 text-yellow-400 fill-current group-hover:scale-110 transition-transform duration-300",style:{animationDelay:100*t+"ms"}},t))}),(0,w.jsxs)("p",{className:"text-gray-700 mb-6 italic text-lg leading-relaxed",children:['"',e.content,'"']}),(0,w.jsxs)("div",{className:"flex items-center",children:[(0,w.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mr-4",children:(0,w.jsx)("span",{className:"text-white font-bold text-lg",children:e.name.charAt(0)})}),(0,w.jsxs)("div",{children:[(0,w.jsx)("div",{className:"font-semibold text-gray-900",children:e.name}),(0,w.jsx)("div",{className:"text-sm text-gray-500",children:e.role})]})]})]},t))})]})}),(0,w.jsxs)("section",{id:"pricing",className:"py-24 bg-gradient-to-b from-gray-50 to-white relative overflow-hidden",children:[(0,w.jsxs)("div",{className:"absolute inset-0",children:[(0,w.jsx)("div",{className:"absolute top-20 right-20 w-64 h-64 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse"}),(0,w.jsx)("div",{className:"absolute bottom-20 left-20 w-64 h-64 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-pulse animation-delay-2000"})]}),(0,w.jsxs)("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,w.jsxs)("div",{className:"text-center mb-16",children:[(0,w.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:e("landing.pricing.title")}),(0,w.jsx)("p",{className:"text-xl text-gray-600",children:e("landing.pricing.subtitle")})]}),(0,w.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:F.map((t,s)=>(0,w.jsxs)("div",{className:"group relative p-8 rounded-2xl border-2 transition-all duration-500 hover:-translate-y-2 "+(t.popular?"border-blue-500 shadow-2xl scale-105 bg-gradient-to-b from-blue-50 to-white":"border-gray-200 bg-white hover:border-blue-300 hover:shadow-xl"),children:[t.popular&&(0,w.jsx)("div",{className:"absolute -top-4 left-1/2 transform -translate-x-1/2",children:(0,w.jsx)("span",{className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-full text-sm font-medium shadow-lg",children:e("landing.pricing.popular")})}),(0,w.jsxs)("div",{className:"text-center mb-6",children:[(0,w.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors duration-300",children:t.name}),(0,w.jsxs)("div",{className:"flex items-center justify-center",children:[(0,w.jsx)("span",{className:"text-5xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:t.price}),(0,w.jsxs)("span",{className:"text-gray-600 ml-2 text-lg",children:[e("landing.pricing.currency")," ",t.period]})]})]}),(0,w.jsx)("ul",{className:"space-y-4 mb-8",children:t.features.map((e,t)=>(0,w.jsxs)("li",{className:"flex items-center group-hover:translate-x-1 transition-transform duration-300",style:{animationDelay:100*t+"ms"},children:[(0,w.jsx)(p.A,{className:"w-5 h-5 text-green-500 mr-3 flex-shrink-0"}),(0,w.jsx)("span",{className:"text-gray-600",children:e})]},t))}),(0,w.jsx)(f.N_,{to:"/register",children:(0,w.jsx)(y.Ay,{variant:t.popular?"primary":"outline",size:"lg",className:"w-full transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl",children:"0"===t.price?e("landing.pricing.buttons.free"):e("landing.pricing.buttons.choose")})})]},s))})]})]}),(0,w.jsxs)("section",{className:"py-24 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 relative overflow-hidden",children:[(0,w.jsxs)("div",{className:"absolute inset-0",children:[(0,w.jsx)("div",{className:"absolute top-10 left-10 w-72 h-72 bg-white/10 rounded-full filter blur-3xl animate-pulse"}),(0,w.jsx)("div",{className:"absolute bottom-10 right-10 w-72 h-72 bg-white/10 rounded-full filter blur-3xl animate-pulse animation-delay-2000"}),(0,w.jsx)("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-white/5 rounded-full filter blur-3xl"})]}),(0,w.jsxs)("div",{className:"relative max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8",children:[(0,w.jsx)("h2",{className:"text-4xl md:text-5xl font-bold text-white mb-6 leading-tight",children:e("landing.cta.title")}),(0,w.jsx)("p",{className:"text-lg md:text-xl text-blue-100 mb-10 leading-relaxed max-w-2xl mx-auto",children:e("landing.cta.subtitle")}),(0,w.jsx)("div",{className:"flex justify-center mb-8",children:(0,w.jsx)(f.N_,{to:"/register",children:(0,w.jsx)(y.Ay,{variant:"white",size:"lg",className:"whitespace-nowrap transform hover:scale-110 transition-all duration-300 shadow-2xl hover:shadow-3xl",children:e("landing.cta.button")})})}),(0,w.jsx)("p",{className:"text-blue-100 text-base md:text-lg font-medium",children:e("landing.cta.features")})]})]}),(0,w.jsx)("footer",{className:"bg-gray-900 text-white py-12",children:(0,w.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,w.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,w.jsxs)("div",{children:[(0,w.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,w.jsx)("div",{className:"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center",children:(0,w.jsx)(a.A,{className:"w-5 h-5 text-white"})}),(0,w.jsx)("span",{className:"text-xl font-bold",children:"FinanceFlow"})]}),(0,w.jsx)("p",{className:"text-gray-400",children:e("landing.footer.description")})]}),(0,w.jsxs)("div",{children:[(0,w.jsx)("h3",{className:"font-semibold mb-4",children:e("landing.footer.product.title")}),(0,w.jsxs)("ul",{className:"space-y-2 text-gray-400",children:[(0,w.jsx)("li",{children:(0,w.jsx)(f.N_,{to:"/features",className:"hover:text-white transition-colors",children:e("landing.footer.product.features")})}),(0,w.jsx)("li",{children:(0,w.jsx)(f.N_,{to:"/pricing",className:"hover:text-white transition-colors",children:e("landing.footer.product.pricing")})}),(0,w.jsx)("li",{children:(0,w.jsx)("a",{href:"#",className:"hover:text-white transition-colors",children:e("landing.footer.product.api")})}),(0,w.jsx)("li",{children:(0,w.jsx)("a",{href:"#",className:"hover:text-white transition-colors",children:e("landing.footer.product.integrations")})})]})]}),(0,w.jsxs)("div",{children:[(0,w.jsx)("h3",{className:"font-semibold mb-4",children:e("landing.footer.support.title")}),(0,w.jsxs)("ul",{className:"space-y-2 text-gray-400",children:[(0,w.jsx)("li",{children:(0,w.jsx)(f.N_,{to:"/documentation",className:"hover:text-white transition-colors",children:e("landing.footer.support.documentation")})}),(0,w.jsx)("li",{children:(0,w.jsx)(f.N_,{to:"/help",className:"hover:text-white transition-colors",children:e("landing.footer.support.guides")})}),(0,w.jsx)("li",{children:(0,w.jsx)(f.N_,{to:"/contact",className:"hover:text-white transition-colors",children:e("landing.footer.support.contact")})}),(0,w.jsx)("li",{children:(0,w.jsx)("a",{href:"#",className:"hover:text-white transition-colors",children:e("landing.footer.support.status")})})]})]}),(0,w.jsxs)("div",{children:[(0,w.jsx)("h3",{className:"font-semibold mb-4",children:e("landing.footer.legal.title")}),(0,w.jsxs)("ul",{className:"space-y-2 text-gray-400",children:[(0,w.jsx)("li",{children:(0,w.jsx)(f.N_,{to:"/terms",className:"hover:text-white transition-colors",children:e("landing.footer.legal.terms")})}),(0,w.jsx)("li",{children:(0,w.jsx)(f.N_,{to:"/privacy",className:"hover:text-white transition-colors",children:e("landing.footer.legal.privacy")})}),(0,w.jsx)("li",{children:(0,w.jsx)(f.N_,{to:"/cookies",className:"hover:text-white transition-colors",children:e("landing.footer.legal.cookies")})}),(0,w.jsx)("li",{children:(0,w.jsx)("a",{href:"#",className:"hover:text-white transition-colors",children:e("landing.footer.legal.gdpr")})})]})]})]}),(0,w.jsx)("div",{className:"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400",children:(0,w.jsx)("p",{children:e("landing.footer.copyright")})})]})})]})}},6103:(e,t,s)=>{s.d(t,{Ay:()=>i});var r=s(4848),a=(s(6540),s(2392)),l=s(9264);const i=({children:e,variant:t="primary",size:s="md",loading:i=!1,disabled:n=!1,fullWidth:o=!1,leftIcon:d=null,rightIcon:c=null,className:m="",onClick:x,type:g="button",...u})=>{const p={primary:["bg-primary-600 text-white border-primary-600","hover:bg-primary-700 hover:border-primary-700","focus:ring-primary-500","disabled:bg-primary-300 disabled:border-primary-300"].join(" "),secondary:["bg-gray-600 text-white border-gray-600","hover:bg-gray-700 hover:border-gray-700","focus:ring-gray-500","disabled:bg-gray-300 disabled:border-gray-300"].join(" "),outline:["bg-transparent text-primary-600 border-primary-600","hover:bg-primary-50 hover:text-primary-700","focus:ring-primary-500","disabled:text-primary-300 disabled:border-primary-300"].join(" "),ghost:["bg-transparent text-gray-700 border-transparent","hover:bg-gray-100 hover:text-gray-900","focus:ring-gray-500","disabled:text-gray-400"].join(" "),danger:["bg-red-600 text-white border-red-600","hover:bg-red-700 hover:border-red-700","focus:ring-red-500","disabled:bg-red-300 disabled:border-red-300"].join(" "),success:["bg-green-600 text-white border-green-600","hover:bg-green-700 hover:border-green-700","focus:ring-green-500","disabled:bg-green-300 disabled:border-green-300"].join(" "),warning:["bg-yellow-600 text-white border-yellow-600","hover:bg-yellow-700 hover:border-yellow-700","focus:ring-yellow-500","disabled:bg-yellow-300 disabled:border-yellow-300"].join(" "),white:["bg-white text-gray-900 border-white","hover:bg-gray-50 hover:text-gray-900","focus:ring-gray-500","disabled:bg-gray-100 disabled:text-gray-400"].join(" ")},h={xs:"h-3 w-3",sm:"h-4 w-4",md:"h-4 w-4",lg:"h-5 w-5",xl:"h-6 w-6"},b=n||i;return(0,r.jsxs)("button",{type:g,onClick:e=>{b?e.preventDefault():x?.(e)},disabled:b,className:(0,a.cn)("inline-flex items-center justify-center","border font-medium rounded-lg","transition-all duration-200 ease-in-out","focus:outline-none focus:ring-2 focus:ring-offset-2","disabled:cursor-not-allowed disabled:opacity-60",p[t],{xs:"px-2 py-1 text-xs",sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base",xl:"px-8 py-4 text-lg"}[s],o&&"w-full",m),...u,children:[d&&!i&&(0,r.jsx)("span",{className:(0,a.cn)(h[s],e&&"mr-2"),children:d}),i&&(0,r.jsx)("span",{className:(0,a.cn)(e&&"mr-2"),children:(0,r.jsx)(l.Ay,{size:"xs"===s||"sm"===s?"sm":"md",color:"currentColor"})}),e&&(0,r.jsx)("span",{className:i?"opacity-70":"",children:e}),c&&!i&&(0,r.jsx)("span",{className:(0,a.cn)(h[s],e&&"ml-2"),children:c})]})}},8724:(e,t,s)=>{s.d(t,{Wy:()=>p,l6:()=>u});var r=s(4848),a=s(5410),l=s(2933),i=s(6540),n=s.n(i),o=s(2392),d=s(9264);const c=({trigger:e,children:t,isOpen:s,onToggle:a,position:l="bottom-left",offset:d=8,className:c="",menuClassName:m="",disabled:x=!1,closeOnSelect:g=!0,...u})=>{const[p,h]=(0,i.useState)(!1),b=(0,i.useRef)(null),f=void 0!==s?s:p,y=()=>{a?a(!1):h(!1)};var j,v;return j=b,v=y,(0,i.useEffect)(()=>{const e=e=>{j.current&&!j.current.contains(e.target)&&v()};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[j,v]),(0,r.jsxs)("div",{ref:b,className:(0,o.cn)("relative inline-block",c),...u,children:[(0,r.jsx)("div",{onClick:()=>{x||(a?a(!f):h(!f))},children:e}),f&&(0,r.jsx)("div",{className:(0,o.cn)("absolute z-50 min-w-full","bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5","transform transition-all duration-200 ease-out","animate-scale-in origin-top",{"top-left":"bottom-full left-0 mb-2","top-right":"bottom-full right-0 mb-2","bottom-left":"top-full left-0 mt-2","bottom-right":"top-full right-0 mt-2",left:"right-full top-0 mr-2",right:"left-full top-0 ml-2"}[l],m),style:{marginTop:l.includes("bottom")?d:void 0},children:(0,r.jsx)("div",{className:"py-1",children:n().Children.map(t,e=>n().isValidElement(e)?n().cloneElement(e,{onSelect:g?()=>{e.props.onSelect?.(),y()}:e.props.onSelect}):e)})})]})},m=({children:e,onClick:t,onSelect:s,disabled:a=!1,active:l=!1,className:i="",icon:n,shortcut:d,...c})=>(0,r.jsxs)("button",{type:"button",className:(0,o.cn)("w-full text-left px-4 py-2 text-sm","flex items-center justify-between","transition-colors duration-150",a?"text-gray-400 cursor-not-allowed":["text-gray-700 hover:bg-gray-100 hover:text-gray-900","focus:bg-gray-100 focus:text-gray-900 focus:outline-none"],l&&"bg-primary-50 text-primary-700",i),onClick:e=>{a||(t?.(e),s?.())},disabled:a,...c,children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 min-w-0 flex-1",children:[n&&(0,r.jsx)("span",{className:"flex-shrink-0",children:n}),(0,r.jsx)("span",{className:"truncate",children:e})]}),d&&(0,r.jsx)("span",{className:"text-xs text-gray-400 ml-2",children:d})]}),x=({className:e=""})=>(0,r.jsx)("div",{className:(0,o.cn)("border-t border-gray-100 my-1",e)}),g=({children:e,className:t="",...s})=>(0,r.jsx)("div",{className:(0,o.cn)("px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider",t),...s,children:e}),u=({value:e,onChange:t,options:s=[],placeholder:n="Selectează o opțiune",disabled:x=!1,loading:g=!1,error:u=!1,searchable:p=!1,clearable:h=!1,multiple:b=!1,className:f="",buttonClassName:y="",optionClassName:j="",menuClassName:v="",renderOption:N,renderValue:w,getOptionLabel:k=e=>e?.label||e,getOptionValue:A=e=>e?.value||e,offset:C=8,...L})=>{const[_,E]=(0,i.useState)(!1),[S,z]=(0,i.useState)(""),D=(0,i.useRef)(null),F=b?Array.isArray(e)?e:[]:e?[e]:[],O=p&&S?s.filter(e=>k(e).toLowerCase().includes(S.toLowerCase())):s,R=((e,t,s,r)=>{const[a,l]=(0,i.useState)(-1);return(0,i.useEffect)(()=>{if(!e)return void l(-1);const i=e=>{switch(e.key){case"ArrowDown":e.preventDefault(),l(e=>e<t.length-1?e+1:0);break;case"ArrowUp":e.preventDefault(),l(e=>e>0?e-1:t.length-1);break;case"Enter":case" ":e.preventDefault(),a>=0&&t[a]&&s(t[a]);break;case"Escape":e.preventDefault(),r()}};return document.addEventListener("keydown",i),()=>document.removeEventListener("keydown",i)},[e,t,a,s,r]),a})(_,O,T,()=>E(!1));function T(e){if(b){const s=A(e);let r;r=F.some(e=>A(e)===s)?F.filter(e=>A(e)!==s):[...F,e],t?.(r)}else t?.(e),E(!1);z("")}return(0,r.jsxs)(c,{isOpen:_,onToggle:E,className:f,menuClassName:v,offset:C,trigger:(0,r.jsxs)("button",{type:"button",className:(0,o.cn)("relative w-full bg-white border rounded-md shadow-sm pl-3 pr-10 py-2 text-left","focus:outline-none focus:ring-1 focus:border-primary-500",x?"bg-gray-50 text-gray-500 cursor-not-allowed border-gray-200":"cursor-pointer border-gray-300 hover:border-gray-400",u&&"border-red-300 focus:border-red-500 focus:ring-red-500",y),disabled:x||g,children:[(0,r.jsx)("span",{className:(0,o.cn)("block truncate",!e&&"text-gray-500"),children:w?w(e):b?0===F.length?n:1===F.length?k(F[0]):`${F.length} opțiuni selectate`:e?k(e):n}),(0,r.jsx)("span",{className:"absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none",children:g?(0,r.jsx)(d.Ay,{size:"sm"}):h&&e&&!x?(0,r.jsx)("button",{type:"button",className:"p-1 hover:bg-gray-100 rounded pointer-events-auto",onClick:e=>{e.stopPropagation(),t?.(b?[]:null)},children:(0,r.jsx)("svg",{className:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}):(0,r.jsx)(a.A,{className:(0,o.cn)("w-5 h-5 text-gray-400 transition-transform duration-200",_&&"transform rotate-180")})})]}),closeOnSelect:!b,children:[p&&(0,r.jsx)("div",{className:"p-2 border-b border-gray-100",children:(0,r.jsx)("input",{ref:D,type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500",placeholder:"Caută...",value:S,onChange:e=>z(e.target.value),onClick:e=>e.stopPropagation()})}),(0,r.jsx)("div",{className:"max-h-60 overflow-auto",children:0===O.length?(0,r.jsx)("div",{className:"px-4 py-2 text-sm text-gray-500",children:S?"Nu s-au găsit rezultate":"Nu există opțiuni"}):O.map((e,t)=>{const s=(e=>{const t=A(e);return F.some(e=>A(e)===t)})(e),a=t===R;return(0,r.jsx)(m,{onClick:()=>T(e),active:a,className:(0,o.cn)(s&&"bg-primary-50 text-primary-700",j),children:(0,r.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,r.jsx)("span",{children:N?(0,r.jsx)(r.Fragment,{children:N(e)}):k(e)}),s&&(0,r.jsx)(l.A,{className:"w-4 h-4 text-primary-600"})]})},A(e))})})]})},p=({trigger:e,actions:t=[],position:s="bottom-right",className:a="",...l})=>(0,r.jsx)(c,{trigger:e,position:s,className:a,...l,children:t.map((e,t)=>"separator"===e.type?(0,r.jsx)(x,{},t):"header"===e.type?(0,r.jsx)(g,{children:e.label},t):(0,r.jsx)(m,{onClick:e.onClick,disabled:e.disabled,icon:e.icon,shortcut:e.shortcut,children:e.label},t))})}}]);