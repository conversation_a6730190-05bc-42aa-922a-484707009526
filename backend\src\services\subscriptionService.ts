import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';
import Stripe from 'stripe';

const prisma = new PrismaClient();
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2023-10-16',
});

interface Plan {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  interval: string;
  features: any;
  limits: any;
  stripe_id: string;
  is_active: boolean;
  sort_order: number;
}

interface SubscriptionStats {
  total_subscriptions: number;
  active_subscriptions: number;
  cancelled_subscriptions: number;
  revenue_this_month: number;
  revenue_last_month: number;
  plan_distribution: Record<string, number>;
}

class SubscriptionService {
  /**
   * Obține planurile disponibile din baza de date
   */
  async getAvailablePlans(): Promise<Plan[]> {
    try {
      const plans = await prisma.plan.findMany({
        where: {
          is_active: true,
        },
        orderBy: {
          sort_order: 'asc',
        },
      });

      return plans.map(plan => ({
        id: plan.id.toString(),
        name: plan.name,
        description: plan.description || '',
        price: parseFloat(plan.price.toString()),
        currency: plan.currency,
        interval: plan.interval,
        features: plan.features as any,
        limits: plan.limits as any,
        stripe_id: plan.stripe_id,
        is_active: plan.is_active,
        sort_order: plan.sort_order,
      }));
    } catch (error) {
      logger.error('Error getting available plans:', error);
      throw error;
    }
  }

  /**
   * Verifică limitele utilizatorului pentru o acțiune specifică
   */
  async checkUserLimits(userId: string, action: string): Promise<boolean> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: parseInt(userId) },
        select: {
          subscription_plan: true,
          subscription_status: true,
        },
      });

      if (!user) {
        return false;
      }

      // Dacă utilizatorul nu are abonament activ, folosește planul gratuit
      const planType = user.subscription_status === 'active' ? user.subscription_plan : 'free';

      // Obține limitele planului
      const plan = await this.getPlanByType(planType || 'free');
      if (!plan) {
        return false;
      }

      const limits = plan.limits as any;

      // Verifică limitele în funcție de acțiune
      switch (action) {
        case 'create_expense':
          if (limits.expenses_per_month === -1) return true;
          return await this.checkExpenseLimit(userId, limits.expenses_per_month);

        case 'create_category':
          if (limits.categories === -1) return true;
          return await this.checkCategoryLimit(userId, limits.categories);

        case 'export_data':
          if (limits.exports_per_month === -1) return true;
          return await this.checkExportLimit(userId, limits.exports_per_month);

        default:
          return true;
      }
    } catch (error) {
      logger.error('Error checking user limits:', error);
      return false;
    }
  }

  /**
   * Actualizează utilizarea utilizatorului
   */
  async updateUsage(userId: string, action: string): Promise<void> {
    try {
      // Înregistrează acțiunea în usage_logs
      await prisma.usageLog.create({
        data: {
          user_id: parseInt(userId),
          action,
          resource: this.getResourceFromAction(action),
          created_at: new Date(),
        },
      });

      logger.info(`Usage updated for user ${userId}, action: ${action}`);
    } catch (error) {
      logger.error('Error updating usage:', error);
      throw error;
    }
  }

  /**
   * Obține statistici despre abonamente
   */
  async getSubscriptionStats(): Promise<SubscriptionStats> {
    try {
      const now = new Date();
      const startOfThisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0);

      const [
        totalSubscriptions,
        activeSubscriptions,
        cancelledSubscriptions,
        thisMonthRevenue,
        lastMonthRevenue,
        planDistribution,
      ] = await Promise.all([
        prisma.subscription.count(),
        prisma.subscription.count({
          where: {
            status: 'active',
          },
        }),
        prisma.subscription.count({
          where: {
            status: 'canceled',
          },
        }),
        this.calculateRevenue(startOfThisMonth, now),
        this.calculateRevenue(startOfLastMonth, endOfLastMonth),
        this.getPlanDistribution(),
      ]);

      return {
        total_subscriptions: totalSubscriptions,
        active_subscriptions: activeSubscriptions,
        cancelled_subscriptions: cancelledSubscriptions,
        revenue_this_month: thisMonthRevenue,
        revenue_last_month: lastMonthRevenue,
        plan_distribution: planDistribution,
      };
    } catch (error) {
      logger.error('Error getting subscription stats:', error);
      throw error;
    }
  }

  /**
   * Sincronizează planurile din Stripe
   */
  async syncPlansFromStripe(): Promise<void> {
    try {
      const stripePlans = await stripe.prices.list({
        active: true,
        expand: ['data.product'],
      });

      for (const stripePrice of stripePlans.data) {
        const product = stripePrice.product as Stripe.Product;

        await prisma.plan.upsert({
          where: { stripe_id: stripePrice.id },
          update: {
            name: product.name,
            description: product.description,
            price: stripePrice.unit_amount ? stripePrice.unit_amount / 100 : 0,
            currency: stripePrice.currency.toUpperCase(),
            interval: stripePrice.recurring?.interval || 'month',
            is_active: stripePrice.active,
          },
          create: {
            stripe_id: stripePrice.id,
            name: product.name,
            description: product.description || '',
            price: stripePrice.unit_amount ? stripePrice.unit_amount / 100 : 0,
            currency: stripePrice.currency.toUpperCase(),
            interval: stripePrice.recurring?.interval || 'month',
            features: {},
            limits: {},
            is_active: stripePrice.active,
            sort_order: 0,
          },
        });
      }

      logger.info('Plans synced from Stripe successfully');
    } catch (error) {
      logger.error('Error syncing plans from Stripe:', error);
      throw error;
    }
  }

  // Helper methods
  private getResourceFromAction(action: string): string {
    switch (action) {
      case 'create_expense':
      case 'update_expense':
      case 'delete_expense':
        return 'expense';
      case 'create_category':
      case 'update_category':
      case 'delete_category':
        return 'category';
      case 'export_data':
        return 'export';
      default:
        return 'unknown';
    }
  }

  private async getPlanByType(planType: string): Promise<Plan | null> {
    const planMapping: Record<string, string> = {
      free: 'free_plan',
      basic: 'price_basic_monthly',
      premium: 'price_premium_monthly',
    };

    const stripeId = planMapping[planType];
    if (!stripeId) return null;

    const plan = await prisma.plan.findFirst({
      where: { stripe_id: stripeId },
    });

    if (!plan) return null;

    return {
      id: plan.id.toString(),
      name: plan.name,
      description: plan.description || '',
      price: parseFloat(plan.price.toString()),
      currency: plan.currency,
      interval: plan.interval,
      features: plan.features as any,
      limits: plan.limits as any,
      stripe_id: plan.stripe_id,
      is_active: plan.is_active,
      sort_order: plan.sort_order,
    };
  }

  private async checkExpenseLimit(userId: string, limit: number): Promise<boolean> {
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);

    const count = await prisma.expense.count({
      where: {
        user_id: parseInt(userId),
        created_at: {
          gte: startOfMonth,
        },
      },
    });

    return count < limit;
  }

  private async checkCategoryLimit(userId: string, limit: number): Promise<boolean> {
    const count = await prisma.category.count({
      where: {
        user_id: parseInt(userId),
        is_default: false,
      },
    });

    return count < limit;
  }

  private async checkExportLimit(userId: string, limit: number): Promise<boolean> {
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);

    const count = await prisma.usageLog.count({
      where: {
        user_id: parseInt(userId),
        action: 'export_data',
        created_at: {
          gte: startOfMonth,
        },
      },
    });

    return count < limit;
  }

  private async calculateRevenue(startDate: Date, endDate: Date): Promise<number> {
    const subscriptions = await prisma.subscription.findMany({
      where: {
        status: 'active',
        created_at: {
          gte: startDate,
          lte: endDate,
        },
      },
      include: {
        plan: true,
      },
    });

    return subscriptions.reduce((total, sub) => {
      return total + parseFloat(sub.plan.price.toString());
    }, 0);
  }

  private async getPlanDistribution(): Promise<Record<string, number>> {
    const distribution = await prisma.subscription.groupBy({
      by: ['plan_id'],
      where: {
        status: 'active',
      },
      _count: {
        plan_id: true,
      },
    });

    const result: Record<string, number> = {};

    for (const item of distribution) {
      const plan = await prisma.plan.findUnique({
        where: { id: item.plan_id },
      });

      if (plan) {
        result[plan.name] = item._count.plan_id;
      }
    }

    return result;
  }
}

const subscriptionService = new SubscriptionService();
export default subscriptionService;
