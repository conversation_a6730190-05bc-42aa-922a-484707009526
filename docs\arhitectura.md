# ARHITECTURA APLICAȚIEI MVP - EXPENSE TRACKER

## 🏗️ OVERVIEW ARHITECTURĂ ACTUALIZATĂ

### Arhitectura de nivel înalt cu monetizare
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   FRONTEND      │    │    BACKEND      │    │    DAT<PERSON>ASE     │
│                 │    │                 │    │                 │
│ React + JSX     │◄──►│ Node.js/Express │◄──►│ SQLite (dev)    │
│ Tailwind CSS    │    │ JWT Auth        │    │ PostgreSQL (prod)│
│ Webpack         │    │ REST API        │    │ Prisma ORM      │
│ i18next         │    │ Stripe SDK      │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│    HOSTING      │    │    HOSTING      │    │   PAYMENTS      │
│                 │    │                 │    │                 │
│ Vercel/Netlify  │    │ Railway/Heroku  │    │ Stripe          │
│                 │    │                 │    │ Webhooks        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Componente noi pentru monetizare:
- **Stripe Integration**: Gestionarea abonamentelor și plăților
- **Subscription Middleware**: Verificarea limitărilor bazate pe plan
- **Admin Dashboard**: Monitorizarea utilizatorilor și veniturilor
- **Usage Tracking**: Monitorizarea utilizării pentru limitări

---

## 🗄️ SCHEMA BAZEI DE DATE

### Pentru EXPENSE TRACKER MVP

```sql
-- Tabela utilizatori (actualizată pentru monetizare)
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    role VARCHAR(20) DEFAULT 'user', -- 'user', 'admin'
    subscription_plan VARCHAR(20) DEFAULT 'free', -- 'free', 'basic', 'premium'
    stripe_customer_id VARCHAR(255) UNIQUE,
    subscription_status VARCHAR(20) DEFAULT 'inactive', -- 'active', 'inactive', 'canceled', 'past_due'
    subscription_expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela categorii (predefinite + personalizate)
CREATE TABLE categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    icon VARCHAR(50), -- emoji sau icon name
    color VARCHAR(7), -- hex color
    is_default BOOLEAN DEFAULT FALSE, -- categorii predefinite
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE, -- NULL pentru categorii default
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela cheltuieli
CREATE TABLE expenses (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    category_id INTEGER NOT NULL REFERENCES categories(id),
    amount DECIMAL(10,2) NOT NULL,
    description TEXT,
    expense_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela bugete (pentru features viitoare)
CREATE TABLE budgets (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    category_id INTEGER REFERENCES categories(id),
    amount DECIMAL(10,2) NOT NULL,
    period VARCHAR(20) NOT NULL, -- 'monthly', 'weekly', 'yearly'
    start_date DATE NOT NULL,
    end_date DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela abonamente Stripe
CREATE TABLE subscriptions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    stripe_subscription_id VARCHAR(255) UNIQUE NOT NULL,
    stripe_customer_id VARCHAR(255) NOT NULL,
    plan_name VARCHAR(50) NOT NULL, -- 'basic', 'premium'
    status VARCHAR(20) NOT NULL, -- 'active', 'canceled', 'past_due', 'unpaid'
    current_period_start TIMESTAMP NOT NULL,
    current_period_end TIMESTAMP NOT NULL,
    cancel_at_period_end BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Tabela pentru tracking utilizare (limitări)
CREATE TABLE usage_tracking (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    month_year VARCHAR(7) NOT NULL, -- format: '2024-01'
    expenses_count INTEGER DEFAULT 0,
    categories_count INTEGER DEFAULT 0,
    exports_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, month_year)
);

-- Tabela pentru planurile de abonament
CREATE TABLE subscription_plans (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL, -- 'free', 'basic', 'premium'
    display_name VARCHAR(100) NOT NULL,
    price_monthly DECIMAL(10,2) NOT NULL,
    stripe_price_id VARCHAR(255),
    max_expenses INTEGER, -- NULL = unlimited
    max_categories INTEGER, -- NULL = unlimited
    max_exports INTEGER, -- NULL = unlimited
    features JSON, -- array de features disponibile
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexuri pentru performanță
CREATE INDEX idx_expenses_user_date ON expenses(user_id, expense_date);
CREATE INDEX idx_expenses_category ON expenses(category_id);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_stripe_customer ON users(stripe_customer_id);
CREATE INDEX idx_subscriptions_user ON subscriptions(user_id);
CREATE INDEX idx_subscriptions_stripe ON subscriptions(stripe_subscription_id);
CREATE INDEX idx_usage_tracking_user_month ON usage_tracking(user_id, month_year);
```

### Date inițiale

#### Categorii predefinite
```sql
INSERT INTO categories (name, icon, color, is_default) VALUES
('Mâncare', '🍽️', '#FF6B6B', TRUE),
('Transport', '🚗', '#4ECDC4', TRUE),
('Utilități', '💡', '#45B7D1', TRUE),
('Divertisment', '🎬', '#96CEB4', TRUE),
('Sănătate', '🏥', '#FFEAA7', TRUE),
('Cumpărături', '🛍️', '#DDA0DD', TRUE),
('Educație', '📚', '#98D8C8', TRUE),
('Altele', '📦', '#F7DC6F', TRUE);
```

#### Planuri de abonament
```sql
INSERT INTO subscription_plans (name, display_name, price_monthly, max_expenses, max_categories, max_exports, features) VALUES
('free', 'Plan Gratuit', 0.00, 50, 8, 1, '["basic_tracking", "default_categories"]'),
('basic', 'Plan Basic', 4.99, 500, 20, 10, '["basic_tracking", "custom_categories", "monthly_reports", "csv_export"]'),
('premium', 'Plan Premium', 9.99, NULL, NULL, NULL, '["unlimited_tracking", "custom_categories", "advanced_reports", "trends_analysis", "pdf_export", "csv_export", "budget_planning"]');
```

#### Utilizator administrator
```sql
-- Parola: admin123 (hash-ul va fi generat în aplicație)
INSERT INTO users (email, password_hash, first_name, last_name, role, subscription_plan, subscription_status) VALUES
('<EMAIL>', '$2b$10$hash_placeholder', 'Admin', 'User', 'admin', 'premium', 'active');
```

---

## 🔌 API DESIGN

### Endpoints pentru MVP

#### Autentificare
```
POST /api/auth/register
POST /api/auth/login
POST /api/auth/logout
GET  /api/auth/me
POST /api/auth/refresh
```

#### Utilizatori
```
GET    /api/users/profile
PUT    /api/users/profile
DELETE /api/users/account
```

#### Categorii
```
GET    /api/categories              # Toate categoriile (default + user)
POST   /api/categories              # Creare categorie personalizată (premium)
PUT    /api/categories/:id          # Editare categorie personalizată
DELETE /api/categories/:id          # Ștergere categorie personalizată
```

#### Cheltuieli
```
GET    /api/expenses                # Lista cheltuieli cu filtrare
POST   /api/expenses                # Adăugare cheltuială
GET    /api/expenses/:id            # Detalii cheltuială
PUT    /api/expenses/:id            # Editare cheltuială
DELETE /api/expenses/:id            # Ștergere cheltuială
```

#### Rapoarte și statistici
```
GET    /api/reports/monthly         # Raport lunar
GET    /api/reports/category        # Cheltuieli pe categorii
GET    /api/reports/trends          # Tendințe (premium)
```

#### Export
```
GET    /api/export/csv              # Export CSV (premium pentru > 50 entries)
GET    /api/export/pdf              # Export PDF (premium)
```

#### Abonamente și plăți
```
GET    /api/subscriptions/plans     # Lista planurilor disponibile
POST   /api/subscriptions/create    # Creare abonament Stripe
POST   /api/subscriptions/cancel    # Anulare abonament
GET    /api/subscriptions/status    # Status abonament curent
POST   /api/subscriptions/portal    # Link către Stripe Customer Portal
POST   /api/webhooks/stripe         # Webhook pentru evenimente Stripe
```

#### Limitări și utilizare
```
GET    /api/usage/current           # Utilizarea curentă (cheltuieli, categorii, etc.)
GET    /api/usage/limits            # Limitările planului curent
```

#### Admin (doar pentru administratori)
```
GET    /api/admin/users             # Lista utilizatori
GET    /api/admin/subscriptions     # Lista abonamente
GET    /api/admin/revenue           # Statistici venituri
PUT    /api/admin/users/:id         # Modificare utilizator
```

### Exemple de request/response

#### POST /api/expenses
```json
// Request
{
  "amount": 25.50,
  "description": "Prânz la restaurant",
  "category_id": 1,
  "expense_date": "2024-01-15"
}

// Response
{
  "success": true,
  "data": {
    "id": 123,
    "amount": 25.50,
    "description": "Prânz la restaurant",
    "category": {
      "id": 1,
      "name": "Mâncare",
      "icon": "🍽️",
      "color": "#FF6B6B"
    },
    "expense_date": "2024-01-15",
    "created_at": "2024-01-15T14:30:00Z"
  }
}
```

#### GET /api/expenses?month=2024-01&category=1
```json
// Response
{
  "success": true,
  "data": {
    "expenses": [
      {
        "id": 123,
        "amount": 25.50,
        "description": "Prânz la restaurant",
        "category": {
          "id": 1,
          "name": "Mâncare",
          "icon": "🍽️",
          "color": "#FF6B6B"
        },
        "expense_date": "2024-01-15",
        "created_at": "2024-01-15T14:30:00Z"
      }
    ],
    "total": 1,
    "sum": 25.50,
    "pagination": {
      "page": 1,
      "limit": 50,
      "total_pages": 1
    }
  }
}
```

#### GET /api/subscriptions/plans
```json
// Response
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "free",
      "display_name": "Plan Gratuit",
      "price_monthly": 0.00,
      "max_expenses": 50,
      "max_categories": 8,
      "max_exports": 1,
      "features": ["basic_tracking", "default_categories"]
    },
    {
      "id": 2,
      "name": "basic",
      "display_name": "Plan Basic",
      "price_monthly": 4.99,
      "max_expenses": 500,
      "max_categories": 20,
      "max_exports": 10,
      "features": ["basic_tracking", "custom_categories", "monthly_reports", "csv_export"]
    },
    {
      "id": 3,
      "name": "premium",
      "display_name": "Plan Premium",
      "price_monthly": 9.99,
      "max_expenses": null,
      "max_categories": null,
      "max_exports": null,
      "features": ["unlimited_tracking", "custom_categories", "advanced_reports", "trends_analysis", "pdf_export", "csv_export", "budget_planning"]
    }
  ]
}
```

#### POST /api/subscriptions/create
```json
// Request
{
  "plan_name": "basic",
  "payment_method_id": "pm_1234567890"
}

// Response
{
  "success": true,
  "data": {
    "subscription_id": "sub_1234567890",
    "client_secret": "pi_1234567890_secret_xyz",
    "status": "requires_payment_method"
  }
}
```

#### GET /api/usage/current
```json
// Response
{
  "success": true,
  "data": {
    "current_month": "2024-01",
    "expenses_count": 25,
    "categories_count": 5,
    "exports_count": 0,
    "plan_limits": {
      "max_expenses": 50,
      "max_categories": 8,
      "max_exports": 1
    },
    "usage_percentage": {
      "expenses": 50,
      "categories": 62.5,
      "exports": 0
    }
  }
}
```

---

## 🎨 FRONTEND ARHITECTURĂ ACTUALIZATĂ

### Structura componentelor (React) - Actualizată
```
src/
├── components/
│   ├── common/
│   │   ├── Header.jsx
│   │   ├── Sidebar.jsx
│   │   ├── Loading.jsx
│   │   ├── ErrorBoundary.jsx
│   │   ├── UpgradePrompt.jsx          # Prompt pentru upgrade la premium
│   │   └── UsageLimitWarning.jsx     # Avertisment limite utilizare
│   ├── auth/
│   │   ├── LoginForm.jsx
│   │   └── RegisterForm.jsx
│   ├── expenses/
│   │   ├── ExpenseForm.jsx
│   │   ├── ExpenseList.jsx
│   │   ├── ExpenseItem.jsx
│   │   └── ExpenseFilters.jsx
│   ├── categories/
│   │   ├── CategorySelector.jsx
│   │   └── CategoryManager.jsx
│   ├── reports/
│   │   ├── MonthlyReport.jsx
│   │   ├── CategoryChart.jsx
│   │   └── TrendsChart.jsx           # Premium feature
│   ├── subscription/
│   │   ├── PricingPlans.jsx          # Afișare planuri de abonament
│   │   ├── SubscriptionStatus.jsx    # Status abonament curent
│   │   ├── PaymentForm.jsx           # Formular plată Stripe
│   │   ├── UsageTracker.jsx          # Monitorizare utilizare
│   │   └── BillingHistory.jsx        # Istoric facturare
│   └── admin/
│       ├── UsersList.jsx             # Lista utilizatori (admin)
│       ├── RevenueStats.jsx          # Statistici venituri (admin)
│       └── SubscriptionsList.jsx     # Lista abonamente (admin)
├── pages/
│   ├── Dashboard.jsx
│   ├── Expenses.jsx
│   ├── Reports.jsx
│   ├── Settings.jsx
│   ├── Premium.jsx                   # Pagina upgrade la premium
│   ├── Billing.jsx                   # Pagina facturare și abonament
│   └── Admin.jsx                     # Dashboard admin
├── hooks/
│   ├── useAuth.js
│   ├── useExpenses.js
│   ├── useCategories.js
│   ├── useSubscription.js            # Hook pentru abonament
│   ├── useUsageTracking.js           # Hook pentru monitorizare utilizare
│   └── useStripe.js                  # Hook pentru integrare Stripe
├── services/
│   ├── api.js
│   ├── auth.js
│   ├── storage.js
│   ├── stripe.js                     # Serviciu Stripe
│   └── subscription.js               # Serviciu abonamente
├── utils/
│   ├── formatters.js
│   ├── validators.js
│   ├── constants.js
│   └── permissions.js                # Verificare permisiuni bazate pe plan
├── middleware/
│   └── subscriptionCheck.js          # Middleware verificare limitări
└── styles/
    ├── globals.css
    └── components.css
```

### State Management - Actualizat
**Pentru MVP: React Context + useState/useReducer**
```javascript
// AuthContext pentru autentificare și roluri
// ExpensesContext pentru cheltuieli
// CategoriesContext pentru categorii
// SubscriptionContext pentru abonamente și limitări
// UsageContext pentru monitorizarea utilizării
// AdminContext pentru funcționalități admin (doar pentru administratori)
```

**Exemple de contexte:**
```javascript
// SubscriptionContext.jsx
const SubscriptionContext = createContext({
  currentPlan: null,
  usage: null,
  limits: null,
  canAddExpense: false,
  canAddCategory: false,
  canExport: false,
  upgradeToBasic: () => {},
  upgradeToPremium: () => {},
  cancelSubscription: () => {}
});

// UsageContext.jsx
const UsageContext = createContext({
  monthlyUsage: {
    expenses: 0,
    categories: 0,
    exports: 0
  },
  refreshUsage: () => {},
  checkLimit: (type) => {}
});
```

**Pentru scaling: Redux Toolkit sau Zustand**

---

## 🔒 SECURITATE ACTUALIZATĂ

### Autentificare și autorizare
- **JWT tokens** cu refresh mechanism
- **Role-based access control** (user, admin)
- **Subscription-based permissions** (free, basic, premium)
- **Password hashing** cu bcrypt (salt rounds: 12)
- **Rate limiting** pe endpoints critice
- **Input validation** pe frontend și backend
- **SQL injection protection** prin prepared statements
- **XSS protection** prin sanitizare input
- **CORS** configurat corect

### Securitate plăți și abonamente
- **Stripe webhook signature verification**
- **PCI compliance** prin Stripe (nu stocăm date de card)
- **Secure customer data handling**
- **Subscription status validation** pe fiecare request
- **Usage limits enforcement** pe server-side
- **Admin endpoints protection** cu verificare rol

### Validări
```javascript
// Exemple validări backend
const expenseSchema = {
  amount: {
    type: 'number',
    min: 0.01,
    max: 999999.99,
    required: true
  },
  description: {
    type: 'string',
    maxLength: 500,
    required: false
  },
  category_id: {
    type: 'integer',
    required: true
  },
  expense_date: {
    type: 'date',
    required: true
  }
};
```

---

## 📊 MONITORING ȘI ANALYTICS ACTUALIZAT

### Metrici tehnice
- **Response time** pentru API endpoints
- **Error rate** și tipuri de erori
- **Database performance** (query time, connections)
- **Memory și CPU usage**
- **Uptime monitoring**
- **Stripe webhook processing** (success rate, latency)
- **Payment processing errors**

### Metrici business - Monetizare
- **Monthly Recurring Revenue (MRR)**
- **Conversion rate** (free → basic → premium)
- **Churn rate** pe planuri
- **Customer Lifetime Value (CLV)**
- **Average Revenue Per User (ARPU)**
- **Subscription renewal rate**
- **Failed payment recovery rate**
- **Usage limits hit rate** (câți utilizatori ating limitele)

### Metrici utilizatori
- **User registration rate**
- **Daily/Monthly active users**
- **Feature usage** (ce funcționalități sunt folosite)
- **Time to first expense** (onboarding success)
- **Retention rate** (7-day, 30-day)

### Tools
- **Sentry** pentru error tracking
- **Google Analytics** pentru user behavior
- **Mixpanel** pentru event tracking
- **Uptime Robot** pentru monitoring

---

## 💳 INTEGRAREA STRIPE

### Configurare Stripe
```javascript
// Configurare Stripe în backend
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

// Webhook endpoint pentru evenimente Stripe
app.post('/api/webhooks/stripe', express.raw({type: 'application/json'}), (req, res) => {
  const sig = req.headers['stripe-signature'];
  let event;
  
  try {
    event = stripe.webhooks.constructEvent(req.body, sig, process.env.STRIPE_WEBHOOK_SECRET);
  } catch (err) {
    return res.status(400).send(`Webhook signature verification failed.`);
  }
  
  // Handle the event
  switch (event.type) {
    case 'customer.subscription.created':
    case 'customer.subscription.updated':
    case 'customer.subscription.deleted':
      // Update subscription in database
      break;
    case 'invoice.payment_succeeded':
    case 'invoice.payment_failed':
      // Handle payment events
      break;
  }
  
  res.json({received: true});
});
```

### Flow-ul de abonament
1. **Utilizatorul alege un plan** → Frontend
2. **Creare Stripe Customer** → Backend API
3. **Creare Stripe Subscription** → Stripe API
4. **Confirmare plată** → Stripe Elements
5. **Webhook notification** → Backend
6. **Update status în DB** → Database
7. **Notificare utilizator** → Frontend

### Gestionarea erorilor de plată
- **Failed payments**: Retry logic cu exponential backoff
- **Expired cards**: Email notifications și grace period
- **Insufficient funds**: Retry în următoarele zile
- **Subscription cancellation**: Immediate sau la sfârșitul perioadei

---

## 🚀 DEPLOYMENT STRATEGY ACTUALIZAT

### Environments
1. **Development** - local machine (SQLite + Stripe Test Mode)
2. **Staging** - pentru testing (PostgreSQL + Stripe Test Mode)
3. **Production** - live application (PostgreSQL + Stripe Live Mode)

### Environment Variables
```bash
# Database
DATABASE_URL=postgresql://...

# JWT
JWT_SECRET=your-jwt-secret
JWT_REFRESH_SECRET=your-refresh-secret

# Stripe
STRIPE_SECRET_KEY=sk_live_... # sau sk_test_...
STRIPE_PUBLISHABLE_KEY=pk_live_... # sau pk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Admin
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=secure-admin-password

# App
NODE_ENV=production
PORT=3000
FRONTEND_URL=https://financeflow.com
```

### CI/CD Pipeline
```yaml
# GitHub Actions example
name: Deploy
on:
  push:
    branches: [main]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Run tests
        run: |
          npm test
          npm run test:integration
      - name: Test Stripe integration
        run: npm run test:stripe
  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - name: Deploy to production
        run: |
          # Database migrations
          npm run migrate:prod
          # Deploy application
          npm run deploy:prod
```

### Database migrations
- **Versioned migrations** cu rollback capability
- **Backup înainte de migration**
- **Zero-downtime deployments**
- **Stripe data synchronization** după migration

---

## 📈 SCALABILITY CONSIDERATIONS ACTUALIZATE

### Pentru creștere viitoare
- **Database indexing** optimizat (inclusiv pentru usage_tracking)
- **Caching layer** (Redis) pentru limitări și planuri
- **CDN** pentru assets statice
- **Load balancing** pentru multiple instances
- **Microservices** pentru features complexe
- **API rate limiting** și throttling bazat pe plan
- **Stripe webhook processing** cu queue system
- **Usage tracking optimization** pentru volume mari

### Performance targets
- **API response time**: < 200ms (95th percentile)
- **Page load time**: < 2 secunde
- **Database queries**: < 100ms average
- **Uptime**: 99.9%
- **Stripe webhook processing**: < 5 secunde
- **Usage limit checks**: < 50ms

### Monetizare la scară
- **Automated billing** cu retry logic
- **Usage aggregation** în batch-uri
- **Revenue reporting** în timp real
- **Customer support** integration
- **Fraud detection** pentru abonamente
- **Multi-currency support** pentru expansiune globală

---

*Arhitectura va fi actualizată pe măsură ce aplicația evoluează.*