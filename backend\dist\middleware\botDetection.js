"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.botDetection = void 0;
const logger_1 = __importDefault(require("../utils/logger"));
const botDetection = (req, res, next) => {
    const userAgent = req.get('User-Agent') || '';
    const botPatterns = [
        /bot/i,
        /crawler/i,
        /spider/i,
        /scraper/i,
        /curl/i,
        /wget/i,
        /python-requests/i,
        /postman/i
    ];
    const isBot = botPatterns.some(pattern => pattern.test(userAgent));
    if (isBot) {
        logger_1.default.info('Bot detected', {
            ip: req.ip,
            url: req.originalUrl,
            method: req.method,
            userAgent,
            userId: req.user?.id
        });
        const allowedBotRoutes = ['/health', '/api/docs', '/robots.txt'];
        if (!allowedBotRoutes.some(route => req.path.startsWith(route))) {
            res.status(403).json({
                success: false,
                error: {
                    message: 'Accesul pentru bot-uri nu este permis pe această rută'
                }
            });
            return;
        }
    }
    next();
};
exports.botDetection = botDetection;
exports.default = exports.botDetection;
//# sourceMappingURL=botDetection.js.map