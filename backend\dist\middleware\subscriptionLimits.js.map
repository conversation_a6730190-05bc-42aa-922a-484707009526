{"version": 3, "file": "subscriptionLimits.js", "sourceRoot": "", "sources": ["../../src/middleware/subscriptionLimits.ts"], "names": [], "mappings": ";;;AACA,6CAA0C;AAOnC,MAAM,kBAAkB,GAAG,KAAK,EACrC,GAAyB,EACzB,GAAa,EACb,IAAkB,EACH,EAAE;IACjB,IAAI,CAAC;QACH,MAAM,EAAE,IAAI,EAAE,GAAG,GAAG,CAAC;QAErB,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,yBAAyB;aACnC,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAGD,MAAM,aAAa,GAAG,MAAM,eAAM,CAAC,QAAQ,CAAC,KAAK,CAAC;YAChD,KAAK,EAAE;gBACL,OAAO,EAAE,IAAI,CAAC,EAAE;gBAChB,UAAU,EAAE,IAAI;aACjB;SACF,CAAC,CAAC;QAGH,MAAM,MAAM,GAAG;YACb,IAAI,EAAE,EAAE;YACR,KAAK,EAAE,EAAE;YACT,OAAO,EAAE,GAAG;YACZ,UAAU,EAAE,IAAI;SACjB,CAAC;QAEF,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,IAAI,MAAM,CAAC;QAC1C,MAAM,KAAK,GAAG,MAAM,CAAC,QAA+B,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC;QAErE,IAAI,aAAa,IAAI,KAAK,EAAE,CAAC;YAC3B,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,gCAAgC,QAAQ,sBAAsB,KAAK,cAAc;gBAC1F,IAAI,EAAE;oBACJ,YAAY,EAAE,aAAa;oBAC3B,KAAK,EAAE,KAAK;oBACZ,QAAQ,EAAE,QAAQ;iBACnB;aACF,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;QACpD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,sDAAsD;SAChE,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC;AAxDW,QAAA,kBAAkB,sBAwD7B;AAGK,MAAM,iBAAiB,GAAG,KAAK,EACpC,GAAyB,EACzB,GAAa,EACb,IAAkB,EACH,EAAE;IACjB,IAAI,CAAC;QACH,MAAM,EAAE,IAAI,EAAE,GAAG,GAAG,CAAC;QAErB,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,yBAAyB;aACnC,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAGD,MAAM,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;QAChC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACxB,YAAY,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;QAElC,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC;QAC1C,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;QAC/C,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;QACtB,UAAU,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;QAErC,MAAM,YAAY,GAAG,MAAM,eAAM,CAAC,OAAO,CAAC,KAAK,CAAC;YAC9C,KAAK,EAAE;gBACL,OAAO,EAAE,IAAI,CAAC,EAAE;gBAChB,IAAI,EAAE;oBACJ,GAAG,EAAE,YAAY;oBACjB,GAAG,EAAE,UAAU;iBAChB;gBACD,UAAU,EAAE,IAAI;aACjB;SACF,CAAC,CAAC;QAGH,MAAM,MAAM,GAAG;YACb,IAAI,EAAE,EAAE;YACR,KAAK,EAAE,GAAG;YACV,OAAO,EAAE,IAAI;YACb,UAAU,EAAE,KAAK;SAClB,CAAC;QAEF,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,IAAI,MAAM,CAAC;QAC1C,MAAM,KAAK,GAAG,MAAM,CAAC,QAA+B,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC;QAErE,IAAI,YAAY,IAAI,KAAK,EAAE,CAAC;YAC1B,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,uCAAuC,QAAQ,sBAAsB,KAAK,sBAAsB;gBACzG,IAAI,EAAE;oBACJ,YAAY,EAAE,YAAY;oBAC1B,KAAK,EAAE,KAAK;oBACZ,QAAQ,EAAE,QAAQ;oBAClB,MAAM,EAAE,SAAS;iBAClB;aACF,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;QACnD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,qDAAqD;SAC/D,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC;AAtEW,QAAA,iBAAiB,qBAsE5B;AAGK,MAAM,gBAAgB,GAAG,KAAK,EACnC,GAAyB,EACzB,GAAa,EACb,IAAkB,EACH,EAAE;IACjB,IAAI,CAAC;QACH,MAAM,EAAE,IAAI,EAAE,GAAG,GAAG,CAAC;QAErB,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,yBAAyB;aACnC,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAGD,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,IAAI,MAAM,CAAC;QAE1C,IAAI,QAAQ,KAAK,MAAM,EAAE,CAAC;YAExB,MAAM,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;YAChC,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACxB,YAAY,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAElC,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC;YAC1C,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;YAC/C,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACtB,UAAU,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;YAKrC,MAAM,WAAW,GAAG,CAAC,CAAC;QAIxB,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAU,EAAE,CAAC;QACpB,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;QAClD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,oDAAoD;SAC9D,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC;AA/CW,QAAA,gBAAgB,oBA+C3B;AAGK,MAAM,qBAAqB,GAAG,CAAC,MAAc,EAAE,EAAE;IACtD,OAAO,KAAK,EACV,GAAyB,EACzB,GAAa,EACb,IAAkB,EACH,EAAE;QACjB,IAAI,CAAC;YACH,MAAM,EAAE,IAAI,EAAE,GAAG,GAAG,CAAC;YAErB,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,yBAAyB;iBACnC,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,IAAI,MAAM,CAAC;YAG1C,MAAM,WAAW,GAAG;gBAClB,IAAI,EAAE,EAAE;gBACR,KAAK,EAAE,CAAC,KAAK,CAAC;gBACd,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC;gBAChC,UAAU,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC;aACpC,CAAC;YAEF,MAAM,cAAc,GAAG,WAAW,CAAC,QAAoC,CAAC,IAAI,EAAE,CAAC;YAE/E,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBACrC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,GAAG,MAAM,CAAC,WAAW,EAAE,qCAAqC,QAAQ,+CAA+C;oBAC5H,IAAI,EAAE;wBACJ,WAAW,EAAE,QAAQ;wBACrB,YAAY,EAAE,MAAM,KAAK,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS;wBACpD,gBAAgB,EAAE,cAAc;qBACjC;iBACF,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,IAAI,EAAE,CAAC;QACT,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;YACvD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,yDAAyD;aACnE,CAAC,CAAC;QACL,CAAC;IACH,CAAC,CAAC;AACJ,CAAC,CAAC;AAnDW,QAAA,qBAAqB,yBAmDhC"}