{"version": 3, "file": "attackDetection.js", "sourceRoot": "", "sources": ["../../src/middleware/attackDetection.ts"], "names": [], "mappings": ";;;;;;AACA,6DAAqC;AAa9B,MAAM,eAAe,GAAG,CAAC,GAAyB,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IACpG,MAAM,kBAAkB,GAAG;QAEzB,iCAAiC;QAEjC,yFAAyF;QAEzF,uBAAuB;QAEvB,wCAAwC;KACzC,CAAC;IAEF,MAAM,eAAe,GAAG,CAAC,IAAS,EAAE,MAAc,EAAW,EAAE;QAC7D,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC7B,KAAK,MAAM,OAAO,IAAI,kBAAkB,EAAE,CAAC;gBACzC,IAAI,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;oBACvB,gBAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE;wBACxC,EAAE,EAAE,GAAG,CAAC,EAAE;wBACV,GAAG,EAAE,GAAG,CAAC,WAAW;wBACpB,MAAM,EAAE,GAAG,CAAC,MAAM;wBAClB,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;wBAChC,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE;wBACpB,MAAM;wBACN,OAAO,EAAE,OAAO,CAAC,QAAQ,EAAE;wBAC3B,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;qBAC7B,CAAC,CAAC;oBACH,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC;aAAM,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC;YACrD,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;gBACvB,IAAI,eAAe,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,MAAM,IAAI,GAAG,EAAE,CAAC,EAAE,CAAC;oBACnD,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC,CAAC;IAGF,eAAe,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAGpC,eAAe,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IAGlC,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;IAEtC,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAjDW,QAAA,eAAe,mBAiD1B;AAEF,kBAAe,uBAAe,CAAC"}