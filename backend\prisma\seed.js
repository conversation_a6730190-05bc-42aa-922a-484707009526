const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create demo user
  const hashedPassword = await bcrypt.hash('password123', 12);
  
  const user = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: hashedPassword,
      name: 'Demo User',
      role: 'user',
      currency: 'RON',
      timezone: 'Europe/Bucharest',
      email_verified: true,
      preferences: {
        theme: 'light',
        notifications: {
          email: true,
          push: false,
          weekly_summary: true,
          monthly_report: true
        },
        dashboard: {
          default_period: 'month',
          show_categories: true,
          show_trends: true
        }
      }
    }
  });

  console.log(`👤 Created user: ${user.email}`);

  // Create admin user
  const adminPassword = await bcrypt.hash('admin123', 12);
  
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: adminPassword,
      name: 'Administrator',
      role: 'admin',
      currency: 'RON',
      timezone: 'Europe/Bucharest',
      email_verified: true,
      preferences: {
        theme: 'light',
        notifications: {
          email: true,
          push: false,
          weekly_summary: true,
          monthly_report: true
        },
        dashboard: {
          default_period: 'month',
          show_categories: true,
          show_trends: true
        }
      }
    }
  });

  console.log(`👤 Created admin user: ${adminUser.email}`);

  // Create default categories
  const categories = [
    {
      name: 'Alimentație',
      description: 'Cheltuieli pentru mâncare și băuturi',
      color: '#FF6B6B',
      icon: 'utensils',
      budget_limit: 1500.00,
      budget_period: 'monthly',
      is_default: true,
      sort_order: 1
    },
    {
      name: 'Transport',
      description: 'Cheltuieli pentru transport public, combustibil, etc.',
      color: '#4ECDC4',
      icon: 'car',
      budget_limit: 800.00,
      budget_period: 'monthly',
      sort_order: 2
    },
    {
      name: 'Utilități',
      description: 'Facturi pentru electricitate, gaz, apă, internet',
      color: '#45B7D1',
      icon: 'home',
      budget_limit: 600.00,
      budget_period: 'monthly',
      sort_order: 3
    },
    {
      name: 'Divertisment',
      description: 'Cinema, restaurante, hobby-uri',
      color: '#F7DC6F',
      icon: 'gamepad-2',
      budget_limit: 400.00,
      budget_period: 'monthly',
      sort_order: 4
    },
    {
      name: 'Sănătate',
      description: 'Medicamente, consultații medicale',
      color: '#BB8FCE',
      icon: 'heart',
      budget_limit: 300.00,
      budget_period: 'monthly',
      sort_order: 5
    },
    {
      name: 'Educație',
      description: 'Cărți, cursuri, training-uri',
      color: '#85C1E9',
      icon: 'book',
      budget_limit: 200.00,
      budget_period: 'monthly',
      sort_order: 6
    }
  ];

  for (const categoryData of categories) {
    const category = await prisma.category.upsert({
      where: {
        user_id_name: {
          user_id: user.id,
          name: categoryData.name
        }
      },
      update: {},
      create: {
        ...categoryData,
        user_id: user.id
      }
    });
    console.log(`📂 Created category: ${category.name}`);
  }

  // Create sample expenses
  const currentDate = new Date();
  const lastMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1);
  
  const alimentatieCategory = await prisma.category.findFirst({
    where: { user_id: user.id, name: 'Alimentație' }
  });
  
  const transportCategory = await prisma.category.findFirst({
    where: { user_id: user.id, name: 'Transport' }
  });

  const sampleExpenses = [
    {
      amount: 45.50,
      description: 'Cumpărături Kaufland',
      date: new Date(lastMonth.getFullYear(), lastMonth.getMonth(), 5),
      payment_method: 'card',
      location: 'Kaufland Brașov',
      tags: ['supermarket', 'alimentație'],
      category_id: alimentatieCategory.id,
      user_id: user.id
    },
    {
      amount: 12.00,
      description: 'Bilet autobuz',
      date: new Date(lastMonth.getFullYear(), lastMonth.getMonth(), 10),
      payment_method: 'cash',
      tags: ['transport', 'public'],
      category_id: transportCategory.id,
      user_id: user.id
    },
    {
      amount: 89.99,
      description: 'Cumpărături săptămânale',
      date: new Date(lastMonth.getFullYear(), lastMonth.getMonth(), 15),
      payment_method: 'card',
      location: 'Carrefour',
      tags: ['supermarket', 'săptămânal'],
      category_id: alimentatieCategory.id,
      user_id: user.id
    }
  ];

  for (const expenseData of sampleExpenses) {
    const expense = await prisma.expense.create({
      data: expenseData
    });
    console.log(`💰 Created expense: ${expense.description} - ${expense.amount} RON`);
  }

  console.log('✅ Database seeding completed successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });