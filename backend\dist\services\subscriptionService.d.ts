interface Plan {
    id: string;
    name: string;
    description: string;
    price: number;
    currency: string;
    interval: string;
    features: any;
    limits: any;
    stripe_id: string;
}
declare class SubscriptionService {
    getAvailablePlans(): Promise<Plan[]>;
    checkUserLimits(userId: string, action: string): Promise<boolean>;
    updateUsage(userId: string, action: string): Promise<void>;
    getSubscriptionStats(): Promise<any>;
    syncPlansFromStripe(): Promise<void>;
}
declare const _default: SubscriptionService;
export default _default;
//# sourceMappingURL=subscriptionService.d.ts.map