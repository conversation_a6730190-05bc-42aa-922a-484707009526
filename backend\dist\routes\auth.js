"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const authController_1 = __importDefault(require("../controllers/authController"));
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const router = express_1.default.Router();
router.post('/register', (0, validation_1.validate)(validation_1.userSchemas.register), authController_1.default.register);
router.post('/login', (0, validation_1.validate)(validation_1.userSchemas.login), authController_1.default.login);
router.post('/refresh', authController_1.default.refreshToken);
router.post('/logout', auth_1.authenticateToken, authController_1.default.logout);
router.get('/profile', auth_1.authenticateToken, authController_1.default.getProfile);
router.put('/profile', auth_1.authenticateToken, (0, validation_1.validate)(validation_1.userSchemas.updateProfile), authController_1.default.updateProfile);
router.post('/change-password', auth_1.authenticateToken, (0, validation_1.validate)(validation_1.userSchemas.changePassword), authController_1.default.changePassword);
router.post('/forgot-password', (0, validation_1.validate)(validation_1.userSchemas.forgotPassword), authController_1.default.forgotPassword);
router.post('/reset-password', (0, validation_1.validate)(validation_1.userSchemas.resetPassword), authController_1.default.resetPassword);
router.post('/verify-email', authController_1.default.verifyEmail);
router.post('/resend-verification', auth_1.authenticateToken, authController_1.default.resendVerification);
exports.default = router;
//# sourceMappingURL=auth.js.map