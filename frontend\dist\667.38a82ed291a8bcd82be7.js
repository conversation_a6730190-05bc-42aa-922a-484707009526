"use strict";(self.webpackChunkexpense_tracker_frontend=self.webpackChunkexpense_tracker_frontend||[]).push([[667],{125:(e,r,a)=>{a.d(r,{Ay:()=>i});var s=a(4848),t=(a(6540),a(2392));a(9264);const i=({children:e,className:r="",shadow:a=!0,border:i=!0,rounded:l=!0,padding:o=!0,hover:n=!1,clickable:c=!1,onClick:d,...m})=>{const x=c||Bo<PERSON>an(d);return(0,s.jsx)("div",{className:(0,t.cn)("bg-white",a&&"shadow-sm",i&&"border border-gray-200",l&&"rounded-lg",o&&"p-6",n&&"hover:shadow-md transition-shadow duration-200",x&&["cursor-pointer","hover:shadow-md hover:border-gray-300","transition-all duration-200","focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"],r),onClick:d,role:x?"button":void 0,tabIndex:x?0:void 0,onKeyDown:x?e=>{"Enter"!==e.key&&" "!==e.key||(e.preventDefault(),d?.(e))}:void 0,...m,children:e})}},6103:(e,r,a)=>{a.d(r,{Ay:()=>l});var s=a(4848),t=(a(6540),a(2392)),i=a(9264);const l=({children:e,variant:r="primary",size:a="md",loading:l=!1,disabled:o=!1,fullWidth:n=!1,leftIcon:c=null,rightIcon:d=null,className:m="",onClick:x,type:u="button",...p})=>{const y={primary:["bg-primary-600 text-white border-primary-600","hover:bg-primary-700 hover:border-primary-700","focus:ring-primary-500","disabled:bg-primary-300 disabled:border-primary-300"].join(" "),secondary:["bg-gray-600 text-white border-gray-600","hover:bg-gray-700 hover:border-gray-700","focus:ring-gray-500","disabled:bg-gray-300 disabled:border-gray-300"].join(" "),outline:["bg-transparent text-primary-600 border-primary-600","hover:bg-primary-50 hover:text-primary-700","focus:ring-primary-500","disabled:text-primary-300 disabled:border-primary-300"].join(" "),ghost:["bg-transparent text-gray-700 border-transparent","hover:bg-gray-100 hover:text-gray-900","focus:ring-gray-500","disabled:text-gray-400"].join(" "),danger:["bg-red-600 text-white border-red-600","hover:bg-red-700 hover:border-red-700","focus:ring-red-500","disabled:bg-red-300 disabled:border-red-300"].join(" "),success:["bg-green-600 text-white border-green-600","hover:bg-green-700 hover:border-green-700","focus:ring-green-500","disabled:bg-green-300 disabled:border-green-300"].join(" "),warning:["bg-yellow-600 text-white border-yellow-600","hover:bg-yellow-700 hover:border-yellow-700","focus:ring-yellow-500","disabled:bg-yellow-300 disabled:border-yellow-300"].join(" "),white:["bg-white text-gray-900 border-white","hover:bg-gray-50 hover:text-gray-900","focus:ring-gray-500","disabled:bg-gray-100 disabled:text-gray-400"].join(" ")},h={xs:"h-3 w-3",sm:"h-4 w-4",md:"h-4 w-4",lg:"h-5 w-5",xl:"h-6 w-6"},g=o||l;return(0,s.jsxs)("button",{type:u,onClick:e=>{g?e.preventDefault():x?.(e)},disabled:g,className:(0,t.cn)("inline-flex items-center justify-center","border font-medium rounded-lg","transition-all duration-200 ease-in-out","focus:outline-none focus:ring-2 focus:ring-offset-2","disabled:cursor-not-allowed disabled:opacity-60",y[r],{xs:"px-2 py-1 text-xs",sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base",xl:"px-8 py-4 text-lg"}[a],n&&"w-full",m),...p,children:[c&&!l&&(0,s.jsx)("span",{className:(0,t.cn)(h[a],e&&"mr-2"),children:c}),l&&(0,s.jsx)("span",{className:(0,t.cn)(e&&"mr-2"),children:(0,s.jsx)(i.Ay,{size:"xs"===a||"sm"===a?"sm":"md",color:"currentColor"})}),e&&(0,s.jsx)("span",{className:l?"opacity-70":"",children:e}),d&&!l&&(0,s.jsx)("span",{className:(0,t.cn)(h[a],e&&"ml-2"),children:d})]})}},6215:(e,r,a)=>{a.d(r,{Ay:()=>x});var s=a(4848),t=a(2509),i=a(72),l=a(3956),o=a(7117),n=a(4015),c=a(6540),d=a(2392);const m=(0,c.forwardRef)(({label:e,type:r="text",placeholder:a,value:m,onChange:x,onBlur:u,onFocus:p,error:y,success:h,hint:g,required:b=!1,disabled:f=!1,readOnly:j=!1,size:N="md",leftIcon:w,rightIcon:v,className:A="",inputClassName:k="",labelClassName:P="",id:S,name:C,...I},R)=>{const[z,B]=(0,c.useState)(!1),[E,T]=(0,c.useState)(!1),Y=S||`input-${Math.random().toString(36).substr(2,9)}`,D="password"===r&&z?"text":r,F={sm:"h-4 w-4",md:"h-5 w-5",lg:"h-6 w-6"},_=Boolean(y),q=Boolean(h)&&!_;return Boolean(w||v||"password"===r),(0,s.jsxs)("div",{className:(0,d.cn)("w-full",A),children:[e&&(0,s.jsxs)("label",{htmlFor:Y,className:(0,d.cn)("block text-sm font-medium mb-2",_?"text-red-700":"text-gray-700",f&&"text-gray-400",P),children:[e,b&&(0,s.jsx)("span",{className:"text-red-500 ml-1","aria-label":"obligatoriu",children:"*"})]}),(0,s.jsxs)("div",{className:"relative",children:[w&&(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)("span",{className:(0,d.cn)(F[N],_?"text-red-400":"text-gray-400"),children:w})}),(0,s.jsx)("input",{ref:R,id:Y,name:C,type:D,value:m,onChange:x,onFocus:e=>{T(!0),p?.(e)},onBlur:e=>{T(!1),u?.(e)},placeholder:a,required:b,disabled:f,readOnly:j,className:(0,d.cn)("block w-full border rounded-lg transition-all duration-200","focus:outline-none focus:ring-2 focus:ring-offset-0","placeholder:text-gray-400",{sm:"px-3 py-2 text-sm",md:"px-4 py-2.5 text-sm",lg:"px-4 py-3 text-base"}[N],w&&"pl-10",(v||"password"===r)&&"pr-10",!f&&!j&&[_?["border-red-300 text-red-900","focus:border-red-500 focus:ring-red-500"]:q?["border-green-300 text-green-900","focus:border-green-500 focus:ring-green-500"]:["border-gray-300 text-gray-900","focus:border-primary-500 focus:ring-primary-500","hover:border-gray-400"]],f&&["bg-gray-50 border-gray-200 text-gray-500","cursor-not-allowed"],j&&["bg-gray-50 border-gray-200","cursor-default"],k),...I}),(0,s.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:"password"===r?(0,s.jsx)("button",{type:"button",onClick:()=>{B(!z)},className:(0,d.cn)("text-gray-400 hover:text-gray-600 focus:outline-none",F[N]),"aria-label":z?"Ascunde parola":"Arată parola",children:z?(0,s.jsx)(t.A,{className:F[N]}):(0,s.jsx)(i.A,{className:F[N]})}):_?(0,s.jsx)(l.A,{className:(0,d.cn)(F[N],"text-red-400")}):q?(0,s.jsx)(o.A,{className:(0,d.cn)(F[N],"text-green-400")}):v?(0,s.jsx)("span",{className:(0,d.cn)(F[N],"text-gray-400"),children:v}):null})]}),(y||h||g)&&(0,s.jsxs)("div",{className:"mt-2 flex items-start space-x-1",children:[(y||h)&&(0,s.jsx)("span",{className:"flex-shrink-0 mt-0.5",children:y?(0,s.jsx)(l.A,{className:"h-4 w-4 text-red-400"}):(0,s.jsx)(o.A,{className:"h-4 w-4 text-green-400"})}),g&&!y&&!h&&(0,s.jsx)(n.A,{className:"h-4 w-4 text-gray-400 flex-shrink-0 mt-0.5"}),(0,s.jsx)("p",{className:(0,d.cn)("text-sm",y?"text-red-600":h?"text-green-600":"text-gray-600"),children:y||h||g})]})]})});m.displayName="Input",(0,c.forwardRef)((e,r)=>(0,s.jsx)(m,{ref:r,type:"email",placeholder:"<EMAIL>",...e})).displayName="EmailInput",(0,c.forwardRef)((e,r)=>(0,s.jsx)(m,{ref:r,type:"password",placeholder:"••••••••",...e})).displayName="PasswordInput",(0,c.forwardRef)(({min:e,max:r,step:a=1,...t},i)=>(0,s.jsx)(m,{ref:i,type:"number",min:e,max:r,step:a,...t})).displayName="NumberInput",(0,c.forwardRef)(({onSearch:e,...r},a)=>(0,s.jsx)(m,{ref:a,type:"search",placeholder:"Caută...",onKeyDown:r=>{"Enter"===r.key&&e&&e(r.currentTarget.value)},...r})).displayName="SearchInput",(0,c.forwardRef)((e,r)=>(0,s.jsx)(m,{ref:r,type:"tel",placeholder:"+40 123 456 789",...e})).displayName="PhoneInput",(0,c.forwardRef)((e,r)=>(0,s.jsx)(m,{ref:r,type:"url",placeholder:"https://exemplu.com",...e})).displayName="UrlInput",(0,c.forwardRef)((e,r)=>(0,s.jsx)(m,{ref:r,type:"date",...e})).displayName="DateInput",(0,c.forwardRef)((e,r)=>(0,s.jsx)(m,{ref:r,type:"time",...e})).displayName="TimeInput",(0,c.forwardRef)((e,r)=>(0,s.jsx)(m,{ref:r,type:"datetime-local",...e})).displayName="DateTimeInput";const x=m},9667:(e,r,a)=>{a.r(r),a.d(r,{default:()=>N});var s=a(8624),t=a(1266),i=a(8054),l=a(6820),o=a(5763),n=a(7037),c=a(893),d=a(6540),m=a(9785),x=a(888),u=a(8952),p=a(6103),y=a(125),h=a(6215),g=a(5009),b=a(4848);const f=u.Ik({firstName:u.Yj().min(2,"Prenumele trebuie să aibă cel puțin 2 caractere"),lastName:u.Yj().min(2,"Numele trebuie să aibă cel puțin 2 caractere"),email:u.Yj().email("Email invalid"),phone:u.Yj().optional(),bio:u.Yj().max(500,"Biografia nu poate depăși 500 de caractere").optional()}),j=u.Ik({currentPassword:u.Yj().min(1,"Parola curentă este obligatorie"),newPassword:u.Yj().min(8,"Parola nouă trebuie să aibă cel puțin 8 caractere"),confirmPassword:u.Yj().min(1,"Confirmarea parolei este obligatorie")}).refine(e=>e.newPassword===e.confirmPassword,{message:"Parolele nu se potrivesc",path:["confirmPassword"]}),N=()=>{const{user:e}=(0,g.nc)(),[r,a]=(0,d.useState)("profile"),[u,N]=(0,d.useState)(!1),{register:w,handleSubmit:v,formState:{errors:A,isSubmitting:k}}=(0,m.mN)({resolver:(0,c.u)(f),defaultValues:{firstName:e?.firstName||"",lastName:e?.lastName||"",email:e?.email||"",phone:e?.phone||"",bio:e?.bio||""}}),{register:P,handleSubmit:S,formState:{errors:C,isSubmitting:I},reset:R}=(0,m.mN)({resolver:(0,c.u)(j)}),z=[{id:"profile",label:"Informații personale",icon:s.A},{id:"security",label:"Securitate",icon:t.A},{id:"notifications",label:"Notificări",icon:i.A}];return(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Profil"}),(0,b.jsx)("p",{className:"text-gray-600",children:"Gestionează informațiile contului tău"})]}),(0,b.jsx)("div",{className:"border-b border-gray-200",children:(0,b.jsx)("nav",{className:"-mb-px flex space-x-8",children:z.map(e=>{const s=e.icon;return(0,b.jsxs)("button",{onClick:()=>a(e.id),className:"flex items-center py-2 px-1 border-b-2 font-medium text-sm "+(r===e.id?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[(0,b.jsx)(s,{className:"h-5 w-5 mr-2"}),e.label]},e.id)})})}),"profile"===r&&(0,b.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,b.jsx)(y.Ay,{className:"p-6",children:(0,b.jsxs)("div",{className:"text-center",children:[(0,b.jsxs)("div",{className:"relative inline-block",children:[(0,b.jsx)("div",{className:"w-32 h-32 bg-gray-300 rounded-full flex items-center justify-center mx-auto",children:e?.avatar?(0,b.jsx)("img",{src:e.avatar,alt:"Avatar",className:"w-32 h-32 rounded-full object-cover"}):(0,b.jsx)(s.A,{className:"h-16 w-16 text-gray-400"})}),(0,b.jsxs)("label",{className:"absolute bottom-0 right-0 bg-primary-600 text-white p-2 rounded-full cursor-pointer hover:bg-primary-700",children:[(0,b.jsx)(l.A,{className:"h-4 w-4"}),(0,b.jsx)("input",{type:"file",accept:"image/*",onChange:async e=>{const r=e.target.files?.[0];if(r)if(r.size>5242880)x.oR.error("Fișierul este prea mare. Dimensiunea maximă este 5MB.");else if(r.type.startsWith("image/")){N(!0);try{await new Promise(e=>setTimeout(e,2e3)),x.oR.success("Avatarul a fost actualizat cu succes!")}catch(e){x.oR.error("Eroare la încărcarea avatarului")}finally{N(!1)}}else x.oR.error("Te rog să selectezi un fișier imagine valid.")},className:"hidden",disabled:u})]})]}),(0,b.jsxs)("h3",{className:"mt-4 text-lg font-medium text-gray-900",children:[e?.firstName," ",e?.lastName]}),(0,b.jsx)("p",{className:"text-gray-500",children:e?.email}),u&&(0,b.jsx)("p",{className:"text-sm text-primary-600 mt-2",children:"Se încarcă..."})]})}),(0,b.jsx)("div",{className:"lg:col-span-2",children:(0,b.jsxs)(y.Ay,{className:"p-6",children:[(0,b.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-6",children:"Informații personale"}),(0,b.jsxs)("form",{onSubmit:v(async e=>{try{await new Promise(e=>setTimeout(e,1e3)),x.oR.success("Profilul a fost actualizat cu succes!")}catch(e){x.oR.error("Eroare la actualizarea profilului")}}),className:"space-y-6",children:[(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,b.jsx)(h.Ay,{label:"Prenume",...w("firstName"),error:A.firstName?.message,icon:s.A}),(0,b.jsx)(h.Ay,{label:"Nume",...w("lastName"),error:A.lastName?.message,icon:s.A})]}),(0,b.jsx)(h.Ay,{label:"Email",type:"email",...w("email"),error:A.email?.message,icon:o.A}),(0,b.jsx)(h.Ay,{label:"Telefon",type:"tel",...w("phone"),error:A.phone?.message,icon:n.A,placeholder:"+40 123 456 789"}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Biografie"}),(0,b.jsx)("textarea",{...w("bio"),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500",placeholder:"Scrie câteva cuvinte despre tine..."}),A.bio&&(0,b.jsx)("p",{className:"mt-1 text-sm text-red-600",children:A.bio.message})]}),(0,b.jsx)("div",{className:"flex justify-end",children:(0,b.jsx)(p.Ay,{type:"submit",variant:"primary",disabled:k,children:k?"Se salvează...":"Salvează modificările"})})]})]})})]}),"security"===r&&(0,b.jsxs)(y.Ay,{className:"p-6 max-w-2xl",children:[(0,b.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-6",children:"Schimbă parola"}),(0,b.jsxs)("form",{onSubmit:S(async e=>{try{await new Promise(e=>setTimeout(e,1e3)),x.oR.success("Parola a fost schimbată cu succes!"),R()}catch(e){x.oR.error("Eroare la schimbarea parolei")}}),className:"space-y-6",children:[(0,b.jsx)(h.Ay,{label:"Parola curentă",type:"password",...P("currentPassword"),error:C.currentPassword?.message,icon:t.A}),(0,b.jsx)(h.Ay,{label:"Parola nouă",type:"password",...P("newPassword"),error:C.newPassword?.message,icon:t.A}),(0,b.jsx)(h.Ay,{label:"Confirmă parola nouă",type:"password",...P("confirmPassword"),error:C.confirmPassword?.message,icon:t.A}),(0,b.jsx)("div",{className:"flex justify-end",children:(0,b.jsx)(p.Ay,{type:"submit",variant:"primary",disabled:I,children:I?"Se schimbă...":"Schimbă parola"})})]})]}),"notifications"===r&&(0,b.jsxs)(y.Ay,{className:"p-6 max-w-2xl",children:[(0,b.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-6",children:"Preferințe notificări"}),(0,b.jsxs)("div",{className:"space-y-6",children:[(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:"Notificări email"}),(0,b.jsx)("p",{className:"text-sm text-gray-500",children:"Primește notificări despre cheltuieli și rapoarte"})]}),(0,b.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"})]}),(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:"Notificări push"}),(0,b.jsx)("p",{className:"text-sm text-gray-500",children:"Primește notificări în browser"})]}),(0,b.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"})]}),(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:"Rapoarte săptămânale"}),(0,b.jsx)("p",{className:"text-sm text-gray-500",children:"Primește un rezumat săptămânal al cheltuielilor"})]}),(0,b.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"})]}),(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h4",{className:"text-sm font-medium text-gray-900",children:"Alerte buget"}),(0,b.jsx)("p",{className:"text-sm text-gray-500",children:"Primește alerte când depășești bugetul"})]}),(0,b.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"})]}),(0,b.jsx)("div",{className:"pt-4",children:(0,b.jsx)(p.Ay,{variant:"primary",children:"Salvează preferințele"})})]})]})]})}}}]);