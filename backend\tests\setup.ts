import { PrismaClient } from '@prisma/client';
import { execSync } from 'child_process';
import { join } from 'path';

// Mock environment variables pentru teste
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-only';
process.env.JWT_REFRESH_SECRET = 'test-refresh-secret-key-for-testing-only';
process.env.DATABASE_URL = 'file:./test.db';
process.env.REDIS_URL = 'redis://localhost:6379/1';

// Creează instanța Prisma pentru teste
export const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
});

// Setup înainte de toate testele
beforeAll(async () => {
  // Rulează migrările pentru baza de date de test
  try {
    execSync('npx prisma migrate deploy', {
      cwd: join(__dirname, '..'),
      stdio: 'inherit',
    });
  } catch (error) {
    console.error('Failed to run database migrations:', error);
  }

  // Conectează la baza de date
  await prisma.$connect();
});

// Cleanup după toate testele
afterAll(async () => {
  // Șterge toate datele din baza de date
  await cleanupDatabase();
  
  // Închide conexiunea
  await prisma.$disconnect();
});

// Cleanup după fiecare test
afterEach(async () => {
  // Șterge datele create în timpul testului
  await cleanupDatabase();
});

// Funcție pentru curățarea bazei de date
async function cleanupDatabase() {
  const tablenames = await prisma.$queryRaw<
    Array<{ tablename: string }>
  >`SELECT tablename FROM pg_tables WHERE schemaname='public'`;

  const tables = tablenames
    .map(({ tablename }) => tablename)
    .filter((name) => name !== '_prisma_migrations')
    .map((name) => `"public"."${name}"`)
    .join(', ');

  try {
    await prisma.$executeRawUnsafe(`TRUNCATE TABLE ${tables} CASCADE;`);
  } catch (error) {
    console.log({ error });
  }
}

// Mock pentru logger
jest.mock('../src/utils/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

// Mock pentru Redis
jest.mock('ioredis', () => {
  return jest.fn().mockImplementation(() => ({
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
    exists: jest.fn(),
    expire: jest.fn(),
    ping: jest.fn().mockResolvedValue('PONG'),
    disconnect: jest.fn(),
  }));
});

// Mock pentru Stripe
jest.mock('stripe', () => {
  return jest.fn().mockImplementation(() => ({
    customers: {
      create: jest.fn(),
      retrieve: jest.fn(),
      update: jest.fn(),
    },
    checkout: {
      sessions: {
        create: jest.fn(),
        retrieve: jest.fn(),
      },
    },
    subscriptions: {
      create: jest.fn(),
      retrieve: jest.fn(),
      update: jest.fn(),
      cancel: jest.fn(),
    },
    webhooks: {
      constructEvent: jest.fn(),
    },
    prices: {
      list: jest.fn(),
    },
    accounts: {
      retrieve: jest.fn(),
    },
  }));
});

// Helper functions pentru teste
export const createTestUser = async (overrides = {}) => {
  return await prisma.user.create({
    data: {
      email: '<EMAIL>',
      name: 'Test User',
      password: 'hashedpassword',
      role: 'user',
      subscription_plan: 'free',
      subscription_status: 'active',
      email_verified: true,
      ...overrides,
    },
  });
};

export const createTestCategory = async (userId: number, overrides = {}) => {
  return await prisma.category.create({
    data: {
      name: 'Test Category',
      icon: '🏠',
      color: '#FF6B6B',
      is_default: false,
      user_id: userId,
      ...overrides,
    },
  });
};

export const createTestExpense = async (userId: number, categoryId: number, overrides = {}) => {
  return await prisma.expense.create({
    data: {
      amount: 25.50,
      description: 'Test expense',
      expense_date: new Date(),
      payment_method: 'cash',
      user_id: userId,
      category_id: categoryId,
      tags: [],
      is_recurring: false,
      ...overrides,
    },
  });
};

// Mock pentru JWT
export const mockJWT = {
  sign: jest.fn().mockReturnValue('mock-jwt-token'),
  verify: jest.fn().mockReturnValue({ userId: 1, email: '<EMAIL>' }),
};

jest.mock('jsonwebtoken', () => mockJWT);

// Global test utilities
global.testUtils = {
  createTestUser,
  createTestCategory,
  createTestExpense,
  prisma,
};
