import express, { Request, Response } from 'express';
import { query, param } from 'express-validator';
import adminController from '../controllers/adminController';
import subscriptionController from '../controllers/subscriptionController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

// Middleware pentru autentificare și verificare rol admin
router.use(authenticateToken);
router.use(requireAdmin);

// Valid<PERSON>ri pentru request-uri
const validatePagination = [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('search')
    .optional()
    .isString()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Search term must be a string with max 100 characters'),
  query('status')
    .optional()
    .isIn(['all', 'active', 'inactive'])
    .withMessage('Status must be one of: all, active, inactive'),
  query('timeRange')
    .optional()
    .isIn(['1h', '24h', '7d', '30d'])
    .withMessage('Time range must be one of: 1h, 24h, 7d, 30d'),
];

const validateUserId = [param('userId').isInt({ min: 1 }).withMessage('User ID must be a positive integer')];

const validateAlertId = [param('alertId').isInt({ min: 1 }).withMessage('Alert ID must be a positive integer')];

/**
 * @route   GET /api/admin/dashboard
 * @desc    Get admin dashboard (redirects to stats)
 * @access  Private (Admin only)
 */
router.get('/dashboard', (req: Request, res: Response) => {
  res.redirect('/api/admin/dashboard/stats');
});

/**
 * @route   GET /api/admin/dashboard/stats
 * @desc    Get admin dashboard statistics
 * @access  Private (Admin only)
 */
router.get('/dashboard/stats', adminController.getDashboardStats);

/**
 * @route   GET /api/admin/alerts
 * @desc    Get system alerts
 * @access  Private (Admin only)
 */
router.get('/alerts', adminController.getSystemAlerts);

/**
 * @route   POST /api/admin/alerts/:alertId/read
 * @desc    Mark alert as read
 * @access  Private (Admin only)
 */
router.post('/alerts/:alertId/read', validateAlertId, adminController.markAlertAsRead);

/**
 * @route   GET /api/admin/users
 * @desc    Get users list with pagination and filtering
 * @access  Private (Admin only)
 */
router.get('/users', validatePagination, adminController.getUsers);

/**
 * @route   GET /api/admin/users/:userId
 * @desc    Get user details
 * @access  Private (Admin only)
 */
router.get('/users/:userId', validateUserId, adminController.getUserDetails);

/**
 * @route   GET /api/admin/subscriptions
 * @desc    Get subscriptions list with pagination and filtering
 * @access  Private (Admin only)
 */
router.get('/subscriptions', validatePagination, subscriptionController.getSubscriptions);

/**
 * @route   GET /api/admin/subscriptions/stats
 * @desc    Get subscription statistics
 * @access  Private (Admin only)
 */
router.get('/subscriptions/stats', subscriptionController.getSubscriptionStats);

/**
 * @route   GET /api/admin/plans/stats
 * @desc    Get plan statistics
 * @access  Private (Admin only)
 */
router.get('/plans/stats', subscriptionController.getPlanStats);

/**
 * @route   GET /api/admin/usage/stats
 * @desc    Get usage statistics
 * @access  Private (Admin only)
 */
router.get('/usage/stats', adminController.getUsageStats);

/**
 * @route   GET /api/admin/revenue/data
 * @desc    Get revenue data
 * @access  Private (Admin only)
 */
router.get('/revenue/data', adminController.getRevenueData);

/**
 * @route   GET /api/admin/activity
 * @desc    Get user activity logs
 * @access  Private (Admin only)
 */
router.get('/activity', validatePagination, adminController.getActivity);

export default router;
