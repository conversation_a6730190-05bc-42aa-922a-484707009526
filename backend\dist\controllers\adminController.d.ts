import { Request, Response } from 'express';
interface AuthenticatedRequest extends Request {
    user?: {
        id: string;
        [key: string]: any;
    };
}
declare class AdminController {
    getDashboardStats(req: AuthenticatedRequest, res: Response): Promise<void>;
    getSystemAlerts(req: AuthenticatedRequest, res: Response): Promise<void>;
    markAlertAsRead(req: AuthenticatedRequest, res: Response): Promise<void>;
    getUsers(req: AuthenticatedRequest, res: Response): Promise<void>;
    getUserDetails(req: AuthenticatedRequest, res: Response): Promise<void>;
    getUsageStats(req: AuthenticatedRequest, res: Response): Promise<void>;
    getRevenueData(req: AuthenticatedRequest, res: Response): Promise<void>;
    getActivity(req: AuthenticatedRequest, res: Response): Promise<void>;
}
declare const _default: AdminController;
export default _default;
//# sourceMappingURL=adminController.d.ts.map