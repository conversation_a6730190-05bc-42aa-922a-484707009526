import express from 'express';
import { authenticateToken } from '../middleware/auth';
import {
  getCurrentUsage,
  getUsageStats,
  checkActionPermission,
  getUpgradeRecommendations
} from '../controllers/usageController';

const router = express.Router();

/**
 * Rute pentru gestionarea utilizării și limitelor
 */

// Toate rutele necesită autentificare
router.use(authenticateToken);

/**
 * @route   GET /api/usage/current
 * @desc    Obține informații despre utilizarea curentă
 * @access  Private
 */
router.get('/current', getCurrentUsage);

/**
 * @route   GET /api/usage/stats
 * @desc    Obține statistici detaliate despre utilizare
 * @access  Private
 * @query   period - week|month|year (default: month)
 */
router.get('/stats', getUsageStats);

/**
 * @route   POST /api/usage/check-action
 * @desc    Verifică dacă utilizatorul poate efectua o acțiune
 * @access  Private
 * @body    { action: string, data?: object }
 */
router.post('/check-action', checkActionPermission);

/**
 * @route   GET /api/usage/upgrade-recommendations
 * @desc    Obține recomandări pentru upgrade
 * @access  Private
 */
router.get('/upgrade-recommendations', getUpgradeRecommendations);

export default router;