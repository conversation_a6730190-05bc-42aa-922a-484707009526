(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
  typeof define === 'function' && define.amd ? define(factory) :
  (global.jsonExt = factory());
}(typeof globalThis != 'undefined' ? globalThis : typeof window != 'undefined' ? window : typeof global != 'undefined' ? global : typeof self != 'undefined' ? self : this, (function () {
var exports=(()=>{var P=Object.defineProperty;var K=Object.getOwnPropertyDescriptor;var M=Object.getOwnPropertyNames;var V=Object.prototype.hasOwnProperty;var W=(t,e)=>{for(var n in e)P(t,n,{get:e[n],enumerable:!0})},_=(t,e,n,s)=>{if(e&&typeof e=="object"||typeof e=="function")for(let i of M(e))!V.call(t,i)&&i!==n&&P(t,i,{get:()=>e[i],enumerable:!(s=K(e,i))||s.enumerable});return t};var $=t=>_(P({},"__esModule",{value:!0}),t);var st={};W(st,{createStringifyWebStream:()=>z,parseChunked:()=>N,parseFromWebStream:()=>J,stringifyChunked:()=>C,stringifyInfo:()=>q});function j(t){return typeof t=="object"&&t!==null&&(typeof t[Symbol.iterator]=="function"||typeof t[Symbol.asyncIterator]=="function")}function F(t,e,n,s){switch(n&&typeof n.toJSON=="function"&&(n=n.toJSON()),s!==null&&(n=s.call(t,String(e),n)),typeof n){case"function":case"symbol":n=void 0;break;case"object":if(n!==null){let i=n.constructor;(i===String||i===Number||i===Boolean)&&(n=n.valueOf())}break}return n}function U(t){return typeof t=="function"?t:Array.isArray(t)?[...new Set(t.map(n=>{let s=n&&n.constructor;return s===String||s===Number?String(n):null}).filter(n=>typeof n=="string"))]:null}function R(t){return typeof t=="number"?!Number.isFinite(t)||t<1?!1:" ".repeat(Math.min(t,10)):typeof t=="string"&&t.slice(0,10)||!1}function B(t,e){(t===null||Array.isArray(t)||typeof t!="object")&&(t={replacer:t,space:e});let n=U(t.replacer),s=Object.keys;if(Array.isArray(n)){let i=n;s=()=>i,n=null}return{...t,replacer:n,getKeys:s,space:R(t.space)}}var O=1,Y=2,G=new TextDecoder;function H(t,e){return t.name==="SyntaxError"&&e.jsonParseOffset&&(t.message=t.message.replace(/at position (\d+)/,(n,s)=>"at position "+(Number(s)+e.jsonParseOffset))),t}function Q(t,e){let n=t.length;t.length+=e.length;for(let s=0;s<e.length;s++)t[n+s]=e[s]}async function N(t){let e=typeof t=="function"?t():t;if(j(e)){let n=new E;try{for await(let s of e){if(typeof s!="string"&&!ArrayBuffer.isView(s))throw new TypeError("Invalid chunk: Expected string, TypedArray or Buffer");n.push(s)}return n.finish()}catch(s){throw H(s,n)}}throw new TypeError("Invalid chunk emitter: Expected an Iterable, AsyncIterable, generator, async generator, or a function returning an Iterable or AsyncIterable")}var E=class{constructor(){this.value=void 0,this.valueStack=null,this.stack=new Array(100),this.lastFlushDepth=0,this.flushDepth=0,this.stateString=!1,this.stateStringEscape=!1,this.pendingByteSeq=null,this.pendingChunk=null,this.chunkOffset=0,this.jsonParseOffset=0}parseAndAppend(e,n){this.stack[this.lastFlushDepth-1]===O?(n&&(this.jsonParseOffset--,e="{"+e+"}"),Object.assign(this.valueStack.value,JSON.parse(e))):(n&&(this.jsonParseOffset--,e="["+e+"]"),Q(this.valueStack.value,JSON.parse(e)))}prepareAddition(e){let{value:n}=this.valueStack;if(Array.isArray(n)?n.length!==0:Object.keys(n).length!==0){if(e[0]===",")return this.jsonParseOffset++,e.slice(1);if(e[0]!=="}"&&e[0]!=="]")return this.jsonParseOffset-=3,"[[]"+e}return e}flush(e,n,s){let i=e.slice(n,s);if(this.jsonParseOffset=this.chunkOffset+n,this.pendingChunk!==null&&(i=this.pendingChunk+i,this.jsonParseOffset-=this.pendingChunk.length,this.pendingChunk=null),this.flushDepth===this.lastFlushDepth)this.flushDepth>0?this.parseAndAppend(this.prepareAddition(i),!0):(this.value=JSON.parse(i),this.valueStack={value:this.value,prev:null});else if(this.flushDepth>this.lastFlushDepth){for(let r=this.flushDepth-1;r>=this.lastFlushDepth;r--)i+=this.stack[r]===O?"}":"]";this.lastFlushDepth===0?(this.value=JSON.parse(i),this.valueStack={value:this.value,prev:null}):this.parseAndAppend(this.prepareAddition(i),!0);for(let r=this.lastFlushDepth||1;r<this.flushDepth;r++){let l=this.valueStack.value;if(this.stack[r-1]===O){let x;for(x in l);l=l[x]}else l=l[l.length-1];this.valueStack={value:l,prev:this.valueStack}}}else{i=this.prepareAddition(i);for(let r=this.lastFlushDepth-1;r>=this.flushDepth;r--)this.jsonParseOffset--,i=(this.stack[r]===O?"{":"[")+i;this.parseAndAppend(i,!1);for(let r=this.lastFlushDepth-1;r>=this.flushDepth;r--)this.valueStack=this.valueStack.prev}this.lastFlushDepth=this.flushDepth}push(e){if(typeof e!="string"){if(this.pendingByteSeq!==null){let r=e;e=new Uint8Array(this.pendingByteSeq.length+r.length),e.set(this.pendingByteSeq),e.set(r,this.pendingByteSeq.length),this.pendingByteSeq=null}if(e[e.length-1]>127)for(let r=0;r<e.length;r++){let l=e[e.length-1-r];if(l>>6===3){r++,(r!==4&&l>>3===30||r!==3&&l>>4===14||r!==2&&l>>5===6)&&(this.pendingByteSeq=e.slice(e.length-r),e=e.slice(0,-r));break}}e=G.decode(e)}let n=e.length,s=0,i=0;t:for(let r=0;r<n;r++){if(this.stateString){for(;r<n;r++)if(this.stateStringEscape)this.stateStringEscape=!1;else switch(e.charCodeAt(r)){case 34:this.stateString=!1;continue t;case 92:this.stateStringEscape=!0}break}switch(e.charCodeAt(r)){case 34:this.stateString=!0,this.stateStringEscape=!1;break;case 44:i=r;break;case 123:i=r+1,this.stack[this.flushDepth++]=O;break;case 91:i=r+1,this.stack[this.flushDepth++]=Y;break;case 93:case 125:i=r+1,this.flushDepth--,this.flushDepth<this.lastFlushDepth&&(this.flush(e,s,i),s=i);break;case 9:case 10:case 13:case 32:s===r&&s++,i===r&&i++;break}}i>s&&this.flush(e,s,i),i<n&&(this.pendingChunk!==null?this.pendingChunk+=e:this.pendingChunk=e.slice(i,n)),this.chunkOffset+=n}finish(){return this.pendingChunk!==null&&(this.flush("",0,0),this.pendingChunk=null),this.value}};function L(t){return/[^\x20\x21\x23-\x5B\x5D-\uD799]/.test(t)?JSON.stringify(t):'"'+t+'"'}function*C(t,...e){let{replacer:n,getKeys:s,space:i,...r}=B(...e),l=Number(r.highWaterMark)||16384,x=new Map,g=[],w={"":t},u=null,k=()=>D("",t),y=w,a=!0,S=[""],p=0,h="";for(;k(),!((h.length>=l||u===null)&&(yield h,h="",u===null)););function A(){if(p===0&&(S=s(y),h+="{"),p===S.length){h+=i&&!a?`
${i.repeat(g.length-1)}}`:"}",b();return}let f=S[p++];D(f,y[f])}function m(){if(p===0&&(h+="["),p===y.length){h+=i&&!a?`
${i.repeat(g.length-1)}]`:"]",b();return}D(p,y[p++])}function o(f){if(a?a=!1:h+=",",i&&u!==null&&(h+=`
${i.repeat(g.length)}`),k===A){let c=x.get(f);c===void 0&&x.set(f,c=L(f)+(i?": ":":")),h+=c}}function D(f,c){if(c=F(y,f,c,n),c===null||typeof c!="object")(k!==A||c!==void 0)&&(o(f),I(c));else{if(g.includes(c))throw new TypeError("Converting circular structure to JSON");o(f),g.push(c),d(),k=Array.isArray(c)?m:A,y=c,a=!0,p=0}}function I(f){switch(typeof f){case"string":h+=L(f);break;case"number":h+=Number.isFinite(f)?String(f):"null";break;case"boolean":h+=f?"true":"false";break;case"undefined":case"object":h+="null";break;default:throw new TypeError(`Do not know how to serialize a ${f.constructor?.name||typeof f}`)}}function d(){u={keys:S,index:p,prev:u}}function b(){g.pop();let f=g.length>0?g[g.length-1]:w;k=Array.isArray(f)?m:A,y=f,a=!1,S=u.keys,p=u.index,u=u.prev}}var X=typeof Object.hasOwn=="function"?Object.hasOwn:(t,e)=>Object.hasOwnProperty.call(t,e),Z={8:"\\b",9:"\\t",10:"\\n",12:"\\f",13:"\\r",34:'\\"',92:"\\\\"},v=Uint8Array.from({length:2048},(t,e)=>X(Z,e)?2:e<32?6:e<128?1:2);function tt(t){return t>=55296&&t<=56319}function et(t){return t>=56320&&t<=57343}function T(t){if(!/[^\x20\x21\x23-\x5B\x5D-\x7F]/.test(t))return t.length+2;let e=0,n=!1;for(let s=0;s<t.length;s++){let i=t.charCodeAt(s);if(i<2048)e+=v[i];else if(tt(i)){e+=6,n=!0;continue}else et(i)?e=n?e-2:e+6:e+=3;n=!1}return e+2}function nt(t){let e=0;return t<0&&(e=1,t=-t),t>=1e9&&(e+=9,t=(t-t%1e9)/1e9),t>=1e4?t>=1e6?e+(t>=1e8?9:t>=1e7?8:7):e+(t>=1e5?6:5):e+(t>=100?t>=1e3?4:3:t>=10?2:1)}function it(t){switch(typeof t){case"string":return T(t);case"number":return Number.isFinite(t)?Number.isInteger(t)?nt(t):String(t).length:4;case"boolean":return t?4:5;case"undefined":case"object":return 4;default:return 0}}function q(t,...e){let{replacer:n,getKeys:s,...i}=B(...e),r=!!i.continueOnCircular,l=i.space?.length||0,x=new Map,g=new Map,w=new Set,u=[],k={"":t},y=!1,a=0,S=0,p=0;return h(k,"",t),a===0&&(a+=9),{bytes:isNaN(a)?1/0:a+S,spaceBytes:l>0&&isNaN(a)?1/0:S,circular:[...w]};function h(A,m,o){if(!y)if(o=F(A,m,o,n),o===null||typeof o!="object")(o!==void 0||Array.isArray(A))&&(a+=it(o));else{if(u.includes(o)){w.add(o),a+=4,r||(y=!0);return}if(g.has(o)){a+=g.get(o);return}p++;let D=p,I=a,d=0;if(u.push(o),Array.isArray(o)){d=o.length;for(let b=0;b<d;b++)h(o,b,o[b])}else{let b=a;for(let f of s(o))if(h(o,f,o[f]),b!==a){let c=x.get(f);c===void 0&&x.set(f,c=T(f)+1),a+=c,d++,b=a}}a+=d===0?2:1+d,l>0&&d>0&&(S+=(Array.isArray(o)?0:d)+(1+u.length*l)*(d+1)-l),u.pop(),D!==p&&g.set(o,a-I)}}}function J(t){return N(j(t)?t:async function*(){let e=t.getReader();try{for(;;){let{value:n,done:s}=await e.read();if(s)break;yield n}}finally{e.releaseLock()}})}function z(t,e,n){return typeof ReadableStream.from=="function"?ReadableStream.from(C(t,e,n)):new ReadableStream({start(){this.generator=C(t,e,n)},pull(s){let{value:i,done:r}=this.generator.next();r?s.close():s.enqueue(i)},cancel(){this.generator=null}})}return $(st);})();

  return exports;
})));
//# sourceMappingURL=json-ext.min.js.map
