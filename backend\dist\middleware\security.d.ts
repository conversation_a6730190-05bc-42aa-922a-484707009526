import cors from 'cors';
import { Request, Response, NextFunction, Application } from 'express';
interface AuthenticatedRequest extends Request {
    user?: {
        id: string;
        [key: string]: any;
    };
    id?: string;
}
declare const corsOptions: cors.CorsOptions;
declare const helmetConfig: {
    contentSecurityPolicy: {
        directives: {
            defaultSrc: string[];
            styleSrc: string[];
            fontSrc: string[];
            imgSrc: string[];
            scriptSrc: string[];
            connectSrc: string[];
            frameSrc: string[];
            objectSrc: string[];
            upgradeInsecureRequests: never[];
        };
    };
    crossOriginEmbedderPolicy: boolean;
    hsts: {
        maxAge: number;
        includeSubDomains: boolean;
        preload: boolean;
    };
    noSniff: boolean;
    frameguard: {
        action: string;
    };
    xssFilter: boolean;
    referrerPolicy: {
        policy: string;
    };
};
declare const createRateLimiter: (windowMs: number, max: number, message: string, skipSuccessfulRequests?: boolean) => import("express-rate-limit").RateLimitRequestHandler;
declare const createSlowDown: (windowMs: number, delayAfter: number, delayMs: number) => import("express-rate-limit").RateLimitRequestHandler;
declare const attackDetection: (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
declare const headerSecurity: (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
declare const antiClickjacking: (req: Request, res: Response, next: NextFunction) => void;
declare const botDetection: (req: AuthenticatedRequest, res: Response, next: NextFunction) => void;
declare const setupSecurity: (app: Application) => void;
export { setupSecurity, corsOptions, helmetConfig, createRateLimiter, createSlowDown, attackDetection, headerSecurity, antiClickjacking, botDetection };
//# sourceMappingURL=security.d.ts.map