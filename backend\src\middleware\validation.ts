import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';

// Validation middleware
export const validate = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const { error } = schema.validate(req.body, { abortEarly: false });
    
    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message
      }));
      
      res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors
      });
      return;
    }
    
    next();
  };
};

// User validation schemas
export const userSchemas = {
  register: Joi.object({
    email: Joi.string().email().required().messages({
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    }),
    password: Joi.string().min(6).required().messages({
      'string.min': 'Password must be at least 6 characters long',
      'any.required': 'Password is required'
    }),
    name: Joi.string().min(2).max(50).required().messages({
      'string.min': 'Name must be at least 2 characters long',
      'string.max': 'Name cannot exceed 50 characters',
      'any.required': 'Name is required'
    }),
    currency: Joi.string().length(3).optional().default('USD').messages({
      'string.length': 'Currency must be a 3-letter code (e.g., USD, EUR)'
    })
  }),

  login: Joi.object({
    email: Joi.string().email().required().messages({
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    }),
    password: Joi.string().required().messages({
      'any.required': 'Password is required'
    })
  }),

  updateProfile: Joi.object({
    name: Joi.string().min(2).max(50).optional().messages({
      'string.min': 'Name must be at least 2 characters long',
      'string.max': 'Name cannot exceed 50 characters'
    }),
    currency: Joi.string().length(3).optional().messages({
      'string.length': 'Currency must be a 3-letter code (e.g., USD, EUR)'
    }),
    timezone: Joi.string().optional()
  }),

  changePassword: Joi.object({
    currentPassword: Joi.string().required().messages({
      'any.required': 'Current password is required'
    }),
    newPassword: Joi.string().min(6).required().messages({
      'string.min': 'New password must be at least 6 characters long',
      'any.required': 'New password is required'
    })
  }),

  forgotPassword: Joi.object({
    email: Joi.string().email().required().messages({
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email is required'
    })
  }),

  resetPassword: Joi.object({
    token: Joi.string().required().messages({
      'any.required': 'Reset token is required'
    }),
    password: Joi.string().min(6).required().messages({
      'string.min': 'Password must be at least 6 characters long',
      'any.required': 'Password is required'
    })
  })
};

// Category validation schemas
export const categorySchemas = {
  create: Joi.object({
    name: Joi.string().min(1).max(50).required().messages({
      'string.min': 'Category name is required',
      'string.max': 'Category name cannot exceed 50 characters',
      'any.required': 'Category name is required'
    }),
    description: Joi.string().max(200).optional().messages({
      'string.max': 'Description cannot exceed 200 characters'
    }),
    color: Joi.string().pattern(/^#[0-9A-F]{6}$/i).optional().messages({
      'string.pattern.base': 'Color must be a valid hex color code (e.g., #FF0000)'
    }),
    icon: Joi.string().max(50).optional().messages({
      'string.max': 'Icon name cannot exceed 50 characters'
    }),
    sort_order: Joi.number().integer().min(0).optional().messages({
      'number.base': 'Sort order must be a number',
      'number.integer': 'Sort order must be an integer',
      'number.min': 'Sort order must be 0 or greater'
    })
  }),

  update: Joi.object({
    name: Joi.string().min(1).max(50).optional().messages({
      'string.min': 'Category name cannot be empty',
      'string.max': 'Category name cannot exceed 50 characters'
    }),
    description: Joi.string().max(200).optional().allow('').messages({
      'string.max': 'Description cannot exceed 200 characters'
    }),
    color: Joi.string().pattern(/^#[0-9A-F]{6}$/i).optional().messages({
      'string.pattern.base': 'Color must be a valid hex color code (e.g., #FF0000)'
    }),
    icon: Joi.string().max(50).optional().messages({
      'string.max': 'Icon name cannot exceed 50 characters'
    }),
    sort_order: Joi.number().integer().min(0).optional().messages({
      'number.base': 'Sort order must be a number',
      'number.integer': 'Sort order must be an integer',
      'number.min': 'Sort order must be 0 or greater'
    })
  })
};

// Parameter validation schemas
export const paramSchemas = {
  id: Joi.object({
    id: Joi.string().uuid().required().messages({
      'string.guid': 'Invalid ID format',
      'any.required': 'ID is required'
    })
  })
};

// Expense validation schemas
export const expenseSchemas = {
  create: Joi.object({
    amount: Joi.number().positive().precision(2).required().messages({
      'number.base': 'Amount must be a number',
      'number.positive': 'Amount must be positive',
      'number.precision': 'Amount can have at most 2 decimal places',
      'any.required': 'Amount is required'
    }),
    description: Joi.string().min(1).max(255).required().messages({
      'string.min': 'Description is required',
      'string.max': 'Description cannot exceed 255 characters',
      'any.required': 'Description is required'
    }),
    category_id: Joi.string().uuid().required().messages({
      'string.guid': 'Invalid category ID format',
      'any.required': 'Category is required'
    }),
    date: Joi.date().iso().required().messages({
      'date.base': 'Invalid date format',
      'date.format': 'Date must be in ISO format',
      'any.required': 'Date is required'
    }),
    tags: Joi.array().items(Joi.string().max(50)).max(10).optional().messages({
      'array.base': 'Tags must be an array',
      'string.max': 'Each tag cannot exceed 50 characters',
      'array.max': 'Maximum 10 tags allowed'
    }),
    notes: Joi.string().max(1000).optional().allow('').messages({
      'string.max': 'Notes cannot exceed 1000 characters'
    }),
    receipt_url: Joi.string().uri().optional().allow('').messages({
      'string.uri': 'Receipt URL must be a valid URL'
    })
  }),

  update: Joi.object({
    amount: Joi.number().positive().precision(2).optional().messages({
      'number.base': 'Amount must be a number',
      'number.positive': 'Amount must be positive',
      'number.precision': 'Amount can have at most 2 decimal places'
    }),
    description: Joi.string().min(1).max(255).optional().messages({
      'string.min': 'Description cannot be empty',
      'string.max': 'Description cannot exceed 255 characters'
    }),
    category_id: Joi.string().uuid().optional().messages({
      'string.guid': 'Invalid category ID format'
    }),
    date: Joi.date().iso().optional().messages({
      'date.base': 'Invalid date format',
      'date.format': 'Date must be in ISO format'
    }),
    tags: Joi.array().items(Joi.string().max(50)).max(10).optional().messages({
      'array.base': 'Tags must be an array',
      'string.max': 'Each tag cannot exceed 50 characters',
      'array.max': 'Maximum 10 tags allowed'
    }),
    notes: Joi.string().max(1000).optional().allow('').messages({
      'string.max': 'Notes cannot exceed 1000 characters'
    }),
    receipt_url: Joi.string().uri().optional().allow('').messages({
      'string.uri': 'Receipt URL must be a valid URL'
    })
  }),

  query: Joi.object({
    page: Joi.number().integer().min(1).optional().default(1).messages({
      'number.base': 'Page must be a number',
      'number.integer': 'Page must be an integer',
      'number.min': 'Page must be at least 1'
    }),
    limit: Joi.number().integer().min(1).max(100).optional().default(20).messages({
      'number.base': 'Limit must be a number',
      'number.integer': 'Limit must be an integer',
      'number.min': 'Limit must be at least 1',
      'number.max': 'Limit cannot exceed 100'
    }),
    category_id: Joi.string().uuid().optional().messages({
      'string.guid': 'Invalid category ID format'
    }),
    start_date: Joi.date().iso().optional().messages({
      'date.base': 'Invalid start date format',
      'date.format': 'Start date must be in ISO format'
    }),
    end_date: Joi.date().iso().optional().messages({
      'date.base': 'Invalid end date format',
      'date.format': 'End date must be in ISO format'
    }),
    min_amount: Joi.number().positive().optional().messages({
      'number.base': 'Minimum amount must be a number',
      'number.positive': 'Minimum amount must be positive'
    }),
    max_amount: Joi.number().positive().optional().messages({
      'number.base': 'Maximum amount must be a number',
      'number.positive': 'Maximum amount must be positive'
    }),
    tags: Joi.alternatives().try(
      Joi.string(),
      Joi.array().items(Joi.string())
    ).optional().messages({
      'alternatives.match': 'Tags must be a string or array of strings'
    }),
    search: Joi.string().max(255).optional().messages({
      'string.max': 'Search term cannot exceed 255 characters'
    }),
    sort_by: Joi.string().valid('date', 'amount', 'description', 'created_at').optional().default('date').messages({
      'any.only': 'Sort by must be one of: date, amount, description, created_at'
    }),
    sort_order: Joi.string().valid('asc', 'desc').optional().default('desc').messages({
      'any.only': 'Sort order must be either asc or desc'
    })
  })
};