"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const winston = __importStar(require("winston"));
const logsDir = path_1.default.join(__dirname, '../../logs');
if (!fs_1.default.existsSync(logsDir)) {
    fs_1.default.mkdirSync(logsDir, { recursive: true });
}
const logFormat = winston.format.combine(winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss',
}), winston.format.errors({ stack: true }), winston.format.json());
const logger = winston.createLogger({
    level: process.env.LOG_LEVEL || 'info',
    format: logFormat,
    defaultMeta: {
        service: 'finance-app-backend',
        environment: process.env.NODE_ENV || 'development'
    },
    transports: [
        new winston.transports.File({
            filename: path_1.default.join(logsDir, 'error.log'),
            level: 'error',
            maxsize: 5242880,
            maxFiles: 5,
        }),
        new winston.transports.File({
            filename: path_1.default.join(logsDir, 'combined.log'),
            maxsize: 5242880,
            maxFiles: 5,
        }),
        new winston.transports.File({
            filename: path_1.default.join(logsDir, 'audit.log'),
            level: 'info',
            maxsize: 5242880,
            maxFiles: 10,
            format: winston.format.combine(winston.format.timestamp(), winston.format.json(), winston.format.printf((info) => {
                if (info.type === 'audit') {
                    return JSON.stringify(info);
                }
                return '';
            }))
        })
    ],
    exceptionHandlers: [
        new winston.transports.File({
            filename: path_1.default.join(logsDir, 'exceptions.log')
        })
    ],
    rejectionHandlers: [
        new winston.transports.File({
            filename: path_1.default.join(logsDir, 'rejections.log')
        })
    ]
});
if (process.env.NODE_ENV !== 'production') {
    logger.add(new winston.transports.Console({
        format: winston.format.combine(winston.format.colorize(), winston.format.simple()),
    }));
}
const customLogger = {
    stream: {
        write(message) {
            logger.info(message.trim());
        },
    },
    info: (message, ...meta) => logger.info(message, ...meta),
    error: (message, ...meta) => logger.error(message, ...meta),
    warn: (message, ...meta) => logger.warn(message, ...meta),
    debug: (message, ...meta) => logger.debug(message, ...meta),
    add: (transport) => logger.add(transport),
    logRequest: (req, res, responseTime) => {
        logger.info({
            type: 'request',
            method: req.method,
            url: req.originalUrl,
            statusCode: res.statusCode,
            responseTime: `${responseTime}ms`,
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            userId: req.user?.id,
            contentLength: res.get('Content-Length')
        });
    },
    logPerformance: (operation, duration, metadata = {}) => {
        logger.info({
            type: 'performance',
            operation,
            duration: typeof duration === 'number' ? `${duration}ms` : duration,
            ...metadata
        });
    },
    logAudit: (action, userId, details = {}) => {
        logger.info({
            type: 'audit',
            action,
            userId,
            timestamp: new Date().toISOString(),
            ...details
        });
    },
    logSecurity: (event, details = {}) => {
        logger.warn({
            type: 'security',
            event,
            timestamp: new Date().toISOString(),
            ...details
        });
    },
    logDatabase: (query, duration, metadata = {}) => {
        logger.debug({
            type: 'database',
            query: query.substring(0, 200),
            duration: `${duration}ms`,
            ...metadata
        });
    }
};
exports.default = customLogger;
//# sourceMappingURL=logger.js.map