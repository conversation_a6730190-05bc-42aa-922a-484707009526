import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, act, waitFor } from '@testing-library/react';
import { useAuth } from './useAuth';
import { createMockUser, createMockApiResponse } from '../tests/setup';

// Mock pentru authService
const mockAuthService = {
  login: vi.fn(),
  register: vi.fn(),
  logout: vi.fn(),
  refreshToken: vi.fn(),
  getCurrentUser: vi.fn(),
  updateProfile: vi.fn(),
  changePassword: vi.fn(),
};

vi.mock('../services/authService', () => ({
  authService: mockAuthService,
}));

// Mock pentru localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

describe('useAuth Hook', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);
  });

  it('should initialize with default state', () => {
    // Arrange & Act
    const { result } = renderHook(() => useAuth());

    // Assert
    expect(result.current.user).toBeNull();
    expect(result.current.isAuthenticated).toBe(false);
    expect(result.current.isLoading).toBe(false);
    expect(result.current.error).toBeNull();
  });

  it('should initialize with stored user data', () => {
    // Arrange
    const mockUser = createMockUser();
    mockLocalStorage.getItem.mockImplementation((key) => {
      if (key === 'access_token') return 'mock-token';
      if (key === 'user') return JSON.stringify(mockUser);
      return null;
    });

    // Act
    const { result } = renderHook(() => useAuth());

    // Assert
    expect(result.current.user).toEqual(mockUser);
    expect(result.current.isAuthenticated).toBe(true);
  });

  describe('login', () => {
    it('should login successfully', async () => {
      // Arrange
      const mockUser = createMockUser();
      const mockResponse = createMockApiResponse({
        user: mockUser,
        access_token: 'new-token',
        refresh_token: 'new-refresh-token',
      });
      
      mockAuthService.login.mockResolvedValue(mockResponse);
      
      const { result } = renderHook(() => useAuth());

      // Act
      await act(async () => {
        await result.current.login('<EMAIL>', 'password123');
      });

      // Assert
      expect(mockAuthService.login).toHaveBeenCalledWith('<EMAIL>', 'password123');
      expect(result.current.user).toEqual(mockUser);
      expect(result.current.isAuthenticated).toBe(true);
      expect(result.current.error).toBeNull();
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('access_token', 'new-token');
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('refresh_token', 'new-refresh-token');
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('user', JSON.stringify(mockUser));
    });

    it('should handle login failure', async () => {
      // Arrange
      const errorResponse = {
        success: false,
        message: 'Invalid credentials',
      };
      
      mockAuthService.login.mockResolvedValue(errorResponse);
      
      const { result } = renderHook(() => useAuth());

      // Act
      await act(async () => {
        await result.current.login('<EMAIL>', 'wrongpassword');
      });

      // Assert
      expect(result.current.user).toBeNull();
      expect(result.current.isAuthenticated).toBe(false);
      expect(result.current.error).toBe('Invalid credentials');
    });

    it('should handle network errors during login', async () => {
      // Arrange
      mockAuthService.login.mockRejectedValue(new Error('Network error'));
      
      const { result } = renderHook(() => useAuth());

      // Act
      await act(async () => {
        await result.current.login('<EMAIL>', 'password123');
      });

      // Assert
      expect(result.current.user).toBeNull();
      expect(result.current.isAuthenticated).toBe(false);
      expect(result.current.error).toBe('Network error');
    });

    it('should set loading state during login', async () => {
      // Arrange
      let resolveLogin: (value: any) => void;
      const loginPromise = new Promise((resolve) => {
        resolveLogin = resolve;
      });
      
      mockAuthService.login.mockReturnValue(loginPromise);
      
      const { result } = renderHook(() => useAuth());

      // Act
      act(() => {
        result.current.login('<EMAIL>', 'password123');
      });

      // Assert - loading state
      expect(result.current.isLoading).toBe(true);

      // Complete the login
      await act(async () => {
        resolveLogin!(createMockApiResponse({
          user: createMockUser(),
          access_token: 'token',
          refresh_token: 'refresh-token',
        }));
        await loginPromise;
      });

      // Assert - loading complete
      expect(result.current.isLoading).toBe(false);
    });
  });

  describe('register', () => {
    it('should register successfully', async () => {
      // Arrange
      const mockUser = createMockUser();
      const mockResponse = createMockApiResponse({
        user: mockUser,
        access_token: 'new-token',
        refresh_token: 'new-refresh-token',
      });
      
      mockAuthService.register.mockResolvedValue(mockResponse);
      
      const { result } = renderHook(() => useAuth());

      const registerData = {
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password123',
        password_confirmation: 'password123',
        terms_accepted: true,
      };

      // Act
      await act(async () => {
        await result.current.register(registerData);
      });

      // Assert
      expect(mockAuthService.register).toHaveBeenCalledWith(registerData);
      expect(result.current.user).toEqual(mockUser);
      expect(result.current.isAuthenticated).toBe(true);
    });

    it('should handle registration validation errors', async () => {
      // Arrange
      const errorResponse = {
        success: false,
        message: 'Validation failed',
        errors: {
          email: ['Email is already taken'],
          password: ['Password is too weak'],
        },
      };
      
      mockAuthService.register.mockResolvedValue(errorResponse);
      
      const { result } = renderHook(() => useAuth());

      // Act
      await act(async () => {
        await result.current.register({
          name: 'Test User',
          email: '<EMAIL>',
          password: 'weak',
          password_confirmation: 'weak',
          terms_accepted: true,
        });
      });

      // Assert
      expect(result.current.user).toBeNull();
      expect(result.current.isAuthenticated).toBe(false);
      expect(result.current.error).toBe('Validation failed');
    });
  });

  describe('logout', () => {
    it('should logout successfully', async () => {
      // Arrange
      const mockUser = createMockUser();
      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === 'access_token') return 'mock-token';
        if (key === 'user') return JSON.stringify(mockUser);
        return null;
      });

      mockAuthService.logout.mockResolvedValue({ success: true });
      
      const { result } = renderHook(() => useAuth());

      // Act
      await act(async () => {
        await result.current.logout();
      });

      // Assert
      expect(mockAuthService.logout).toHaveBeenCalled();
      expect(result.current.user).toBeNull();
      expect(result.current.isAuthenticated).toBe(false);
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('access_token');
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('refresh_token');
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('user');
    });

    it('should clear local state even if logout API fails', async () => {
      // Arrange
      const mockUser = createMockUser();
      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === 'access_token') return 'mock-token';
        if (key === 'user') return JSON.stringify(mockUser);
        return null;
      });

      mockAuthService.logout.mockRejectedValue(new Error('Network error'));
      
      const { result } = renderHook(() => useAuth());

      // Act
      await act(async () => {
        await result.current.logout();
      });

      // Assert
      expect(result.current.user).toBeNull();
      expect(result.current.isAuthenticated).toBe(false);
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('access_token');
    });
  });

  describe('updateProfile', () => {
    it('should update profile successfully', async () => {
      // Arrange
      const mockUser = createMockUser();
      const updatedUser = { ...mockUser, name: 'Updated Name' };
      
      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === 'access_token') return 'mock-token';
        if (key === 'user') return JSON.stringify(mockUser);
        return null;
      });

      mockAuthService.updateProfile.mockResolvedValue(
        createMockApiResponse({ user: updatedUser })
      );
      
      const { result } = renderHook(() => useAuth());

      // Act
      await act(async () => {
        await result.current.updateProfile({ name: 'Updated Name' });
      });

      // Assert
      expect(mockAuthService.updateProfile).toHaveBeenCalledWith({ name: 'Updated Name' });
      expect(result.current.user?.name).toBe('Updated Name');
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('user', JSON.stringify(updatedUser));
    });
  });

  describe('refreshToken', () => {
    it('should refresh token successfully', async () => {
      // Arrange
      const mockUser = createMockUser();
      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === 'refresh_token') return 'refresh-token';
        return null;
      });

      mockAuthService.refreshToken.mockResolvedValue(
        createMockApiResponse({
          user: mockUser,
          access_token: 'new-access-token',
          refresh_token: 'new-refresh-token',
        })
      );
      
      const { result } = renderHook(() => useAuth());

      // Act
      await act(async () => {
        await result.current.refreshToken();
      });

      // Assert
      expect(mockAuthService.refreshToken).toHaveBeenCalledWith('refresh-token');
      expect(result.current.user).toEqual(mockUser);
      expect(result.current.isAuthenticated).toBe(true);
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('access_token', 'new-access-token');
    });

    it('should logout if refresh token is invalid', async () => {
      // Arrange
      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === 'refresh_token') return 'invalid-token';
        return null;
      });

      mockAuthService.refreshToken.mockResolvedValue({
        success: false,
        message: 'Invalid refresh token',
      });
      
      const { result } = renderHook(() => useAuth());

      // Act
      await act(async () => {
        await result.current.refreshToken();
      });

      // Assert
      expect(result.current.user).toBeNull();
      expect(result.current.isAuthenticated).toBe(false);
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('access_token');
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('refresh_token');
    });
  });
});
