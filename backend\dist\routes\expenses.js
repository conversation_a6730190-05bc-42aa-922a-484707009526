"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const expenseController_1 = require("../controllers/expenseController");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const subscriptionLimits_1 = require("../middleware/subscriptionLimits");
const router = express_1.default.Router();
router.get('/', auth_1.authenticateToken, (0, validation_1.validate)(validation_1.expenseSchemas.query, 'query'), expenseController_1.expenseController.getExpenses);
router.get('/stats', auth_1.authenticateToken, expenseController_1.expenseController.getExpenseStats);
router.get('/trends', auth_1.authenticateToken, expenseController_1.expenseController.getMonthlyTrends);
router.get('/tags', auth_1.authenticateToken, expenseController_1.expenseController.getPopularTags);
router.get('/:id', auth_1.authenticateToken, (0, validation_1.validate)(validation_1.paramSchemas.id, 'params'), expenseController_1.expenseController.getExpense);
router.post('/', auth_1.authenticateToken, subscriptionLimits_1.checkExpenseLimit, (0, validation_1.validate)(validation_1.expenseSchemas.create), expenseController_1.expenseController.createExpense);
router.put('/:id', auth_1.authenticateToken, (0, validation_1.validate)(validation_1.paramSchemas.id, 'params'), (0, validation_1.validate)(validation_1.expenseSchemas.update), expenseController_1.expenseController.updateExpense);
router.delete('/:id', auth_1.authenticateToken, (0, validation_1.validate)(validation_1.paramSchemas.id, 'params'), expenseController_1.expenseController.deleteExpense);
router.post('/:id/tags', auth_1.authenticateToken, (0, validation_1.validate)(validation_1.paramSchemas.id, 'params'), expenseController_1.expenseController.addTag);
router.delete('/:id/tags', auth_1.authenticateToken, (0, validation_1.validate)(validation_1.paramSchemas.id, 'params'), expenseController_1.expenseController.removeTag);
router.delete('/bulk', auth_1.authenticateToken, expenseController_1.expenseController.bulkDeleteExpenses);
exports.default = router;
//# sourceMappingURL=expenses.js.map