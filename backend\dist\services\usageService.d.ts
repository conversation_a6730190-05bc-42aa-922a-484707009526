interface UsageStats {
    current_period: {
        expenses: number;
        categories: number;
        exports: number;
    };
    limits: {
        expenses_per_month: number;
        categories: number;
        exports_per_month: number;
    };
}
declare class UsageService {
    getUserUsageStats(userId: string): Promise<UsageStats>;
    incrementUsage(userId: string, action: string): Promise<void>;
    canPerformAction(userId: string, action: string): Promise<boolean>;
    resetUsageForPeriod(userId: string, period: string): Promise<void>;
    getGlobalUsageStats(startDate?: string, endDate?: string): Promise<any>;
    getUsageProgress(userId: string): Promise<any>;
}
declare const _default: UsageService;
export default _default;
//# sourceMappingURL=usageService.d.ts.map