"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.expenseSchemas = exports.paramSchemas = exports.categorySchemas = exports.userSchemas = exports.validate = void 0;
const joi_1 = __importDefault(require("joi"));
const validate = (schema) => {
    return (req, res, next) => {
        const { error } = schema.validate(req.body, { abortEarly: false });
        if (error) {
            const errors = error.details.map(detail => ({
                field: detail.path.join('.'),
                message: detail.message
            }));
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors
            });
        }
        next();
    };
};
exports.validate = validate;
exports.userSchemas = {
    register: joi_1.default.object({
        email: joi_1.default.string().email().required().messages({
            'string.email': 'Please provide a valid email address',
            'any.required': 'Email is required'
        }),
        password: joi_1.default.string().min(6).required().messages({
            'string.min': 'Password must be at least 6 characters long',
            'any.required': 'Password is required'
        }),
        name: joi_1.default.string().min(2).max(50).required().messages({
            'string.min': 'Name must be at least 2 characters long',
            'string.max': 'Name cannot exceed 50 characters',
            'any.required': 'Name is required'
        }),
        currency: joi_1.default.string().length(3).optional().default('USD').messages({
            'string.length': 'Currency must be a 3-letter code (e.g., USD, EUR)'
        })
    }),
    login: joi_1.default.object({
        email: joi_1.default.string().email().required().messages({
            'string.email': 'Please provide a valid email address',
            'any.required': 'Email is required'
        }),
        password: joi_1.default.string().required().messages({
            'any.required': 'Password is required'
        })
    }),
    updateProfile: joi_1.default.object({
        name: joi_1.default.string().min(2).max(50).optional().messages({
            'string.min': 'Name must be at least 2 characters long',
            'string.max': 'Name cannot exceed 50 characters'
        }),
        currency: joi_1.default.string().length(3).optional().messages({
            'string.length': 'Currency must be a 3-letter code (e.g., USD, EUR)'
        }),
        timezone: joi_1.default.string().optional()
    }),
    changePassword: joi_1.default.object({
        currentPassword: joi_1.default.string().required().messages({
            'any.required': 'Current password is required'
        }),
        newPassword: joi_1.default.string().min(6).required().messages({
            'string.min': 'New password must be at least 6 characters long',
            'any.required': 'New password is required'
        })
    }),
    forgotPassword: joi_1.default.object({
        email: joi_1.default.string().email().required().messages({
            'string.email': 'Please provide a valid email address',
            'any.required': 'Email is required'
        })
    }),
    resetPassword: joi_1.default.object({
        token: joi_1.default.string().required().messages({
            'any.required': 'Reset token is required'
        }),
        password: joi_1.default.string().min(6).required().messages({
            'string.min': 'Password must be at least 6 characters long',
            'any.required': 'Password is required'
        })
    })
};
exports.categorySchemas = {
    create: joi_1.default.object({
        name: joi_1.default.string().min(1).max(50).required().messages({
            'string.min': 'Category name is required',
            'string.max': 'Category name cannot exceed 50 characters',
            'any.required': 'Category name is required'
        }),
        description: joi_1.default.string().max(200).optional().messages({
            'string.max': 'Description cannot exceed 200 characters'
        }),
        color: joi_1.default.string().pattern(/^#[0-9A-F]{6}$/i).optional().messages({
            'string.pattern.base': 'Color must be a valid hex color code (e.g., #FF0000)'
        }),
        icon: joi_1.default.string().max(50).optional().messages({
            'string.max': 'Icon name cannot exceed 50 characters'
        }),
        sort_order: joi_1.default.number().integer().min(0).optional().messages({
            'number.base': 'Sort order must be a number',
            'number.integer': 'Sort order must be an integer',
            'number.min': 'Sort order must be 0 or greater'
        })
    }),
    update: joi_1.default.object({
        name: joi_1.default.string().min(1).max(50).optional().messages({
            'string.min': 'Category name cannot be empty',
            'string.max': 'Category name cannot exceed 50 characters'
        }),
        description: joi_1.default.string().max(200).optional().allow('').messages({
            'string.max': 'Description cannot exceed 200 characters'
        }),
        color: joi_1.default.string().pattern(/^#[0-9A-F]{6}$/i).optional().messages({
            'string.pattern.base': 'Color must be a valid hex color code (e.g., #FF0000)'
        }),
        icon: joi_1.default.string().max(50).optional().messages({
            'string.max': 'Icon name cannot exceed 50 characters'
        }),
        sort_order: joi_1.default.number().integer().min(0).optional().messages({
            'number.base': 'Sort order must be a number',
            'number.integer': 'Sort order must be an integer',
            'number.min': 'Sort order must be 0 or greater'
        })
    })
};
exports.paramSchemas = {
    id: joi_1.default.object({
        id: joi_1.default.string().uuid().required().messages({
            'string.guid': 'Invalid ID format',
            'any.required': 'ID is required'
        })
    })
};
exports.expenseSchemas = {
    create: joi_1.default.object({
        amount: joi_1.default.number().positive().precision(2).required().messages({
            'number.base': 'Amount must be a number',
            'number.positive': 'Amount must be positive',
            'number.precision': 'Amount can have at most 2 decimal places',
            'any.required': 'Amount is required'
        }),
        description: joi_1.default.string().min(1).max(255).required().messages({
            'string.min': 'Description is required',
            'string.max': 'Description cannot exceed 255 characters',
            'any.required': 'Description is required'
        }),
        category_id: joi_1.default.string().uuid().required().messages({
            'string.guid': 'Invalid category ID format',
            'any.required': 'Category is required'
        }),
        date: joi_1.default.date().iso().required().messages({
            'date.base': 'Invalid date format',
            'date.format': 'Date must be in ISO format',
            'any.required': 'Date is required'
        }),
        tags: joi_1.default.array().items(joi_1.default.string().max(50)).max(10).optional().messages({
            'array.base': 'Tags must be an array',
            'string.max': 'Each tag cannot exceed 50 characters',
            'array.max': 'Maximum 10 tags allowed'
        }),
        notes: joi_1.default.string().max(1000).optional().allow('').messages({
            'string.max': 'Notes cannot exceed 1000 characters'
        }),
        receipt_url: joi_1.default.string().uri().optional().allow('').messages({
            'string.uri': 'Receipt URL must be a valid URL'
        })
    }),
    update: joi_1.default.object({
        amount: joi_1.default.number().positive().precision(2).optional().messages({
            'number.base': 'Amount must be a number',
            'number.positive': 'Amount must be positive',
            'number.precision': 'Amount can have at most 2 decimal places'
        }),
        description: joi_1.default.string().min(1).max(255).optional().messages({
            'string.min': 'Description cannot be empty',
            'string.max': 'Description cannot exceed 255 characters'
        }),
        category_id: joi_1.default.string().uuid().optional().messages({
            'string.guid': 'Invalid category ID format'
        }),
        date: joi_1.default.date().iso().optional().messages({
            'date.base': 'Invalid date format',
            'date.format': 'Date must be in ISO format'
        }),
        tags: joi_1.default.array().items(joi_1.default.string().max(50)).max(10).optional().messages({
            'array.base': 'Tags must be an array',
            'string.max': 'Each tag cannot exceed 50 characters',
            'array.max': 'Maximum 10 tags allowed'
        }),
        notes: joi_1.default.string().max(1000).optional().allow('').messages({
            'string.max': 'Notes cannot exceed 1000 characters'
        }),
        receipt_url: joi_1.default.string().uri().optional().allow('').messages({
            'string.uri': 'Receipt URL must be a valid URL'
        })
    }),
    query: joi_1.default.object({
        page: joi_1.default.number().integer().min(1).optional().default(1).messages({
            'number.base': 'Page must be a number',
            'number.integer': 'Page must be an integer',
            'number.min': 'Page must be at least 1'
        }),
        limit: joi_1.default.number().integer().min(1).max(100).optional().default(20).messages({
            'number.base': 'Limit must be a number',
            'number.integer': 'Limit must be an integer',
            'number.min': 'Limit must be at least 1',
            'number.max': 'Limit cannot exceed 100'
        }),
        category_id: joi_1.default.string().uuid().optional().messages({
            'string.guid': 'Invalid category ID format'
        }),
        start_date: joi_1.default.date().iso().optional().messages({
            'date.base': 'Invalid start date format',
            'date.format': 'Start date must be in ISO format'
        }),
        end_date: joi_1.default.date().iso().optional().messages({
            'date.base': 'Invalid end date format',
            'date.format': 'End date must be in ISO format'
        }),
        min_amount: joi_1.default.number().positive().optional().messages({
            'number.base': 'Minimum amount must be a number',
            'number.positive': 'Minimum amount must be positive'
        }),
        max_amount: joi_1.default.number().positive().optional().messages({
            'number.base': 'Maximum amount must be a number',
            'number.positive': 'Maximum amount must be positive'
        }),
        tags: joi_1.default.alternatives().try(joi_1.default.string(), joi_1.default.array().items(joi_1.default.string())).optional().messages({
            'alternatives.match': 'Tags must be a string or array of strings'
        }),
        search: joi_1.default.string().max(255).optional().messages({
            'string.max': 'Search term cannot exceed 255 characters'
        }),
        sort_by: joi_1.default.string().valid('date', 'amount', 'description', 'created_at').optional().default('date').messages({
            'any.only': 'Sort by must be one of: date, amount, description, created_at'
        }),
        sort_order: joi_1.default.string().valid('asc', 'desc').optional().default('desc').messages({
            'any.only': 'Sort order must be either asc or desc'
        })
    })
};
//# sourceMappingURL=validation.js.map