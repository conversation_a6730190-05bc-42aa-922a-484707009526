const { PrismaClient } = require('@prisma/client');

async function checkAdminUsers() {
  const prisma = new PrismaClient();
  
  try {
    // Găsește toți utilizatorii admin
    const adminUsers = await prisma.users.findMany({
      where: { role: 'admin' },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        is_active: true
      }
    });
    
    console.log('Admin users found:', adminUsers.length);
    console.log('Admin users:', JSON.stringify(adminUsers, null, 2));
    
    // Găsește utilizatorul cu ID 1
    const user1 = await prisma.users.findUnique({
      where: { id: 1 },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        is_active: true
      }
    });
    
    console.log('\nUser with ID 1:', JSON.stringify(user1, null, 2));
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

checkAdminUsers();