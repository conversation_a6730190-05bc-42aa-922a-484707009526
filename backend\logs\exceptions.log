{"date":"Fri Jul 11 2025 10:34:05 GMT+0300 (<PERSON><PERSON> de var<PERSON> a <PERSON>i de <PERSON>st)","environment":"development","error":{},"exception":true,"level":"error","message":"uncaughtException: Please install sqlite3 package manually\nError: Please install sqlite3 package manually\n    at ConnectionManager._loadDialectModule (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\connection-manager.js:55:15)\n    at new ConnectionManager (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\connection-manager.js:18:21)\n    at new SqliteDialect (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\index.js:13:30)\n    at new Sequelize (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\lib\\sequelize.js:194:20)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\dist\\config\\database.js:70:41)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)\n    at Object..js (node:internal/modules/cjs/loader:1899:10)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)\n    at Module._load (node:internal/modules/cjs/loader:1286:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":762237.281},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","F:\\proiecte\\fn\\backend\\dist\\app.js","dist/app.js"],"cwd":"F:\\proiecte\\fn\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":19328,"external":2368033,"heapTotal":56504320,"heapUsed":26860664,"rss":86536192},"pid":26792,"uid":null,"version":"v24.0.1"},"service":"finance-app-backend","stack":"Error: Please install sqlite3 package manually\n    at ConnectionManager._loadDialectModule (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\connection-manager.js:55:15)\n    at new ConnectionManager (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\connection-manager.js:18:21)\n    at new SqliteDialect (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\index.js:13:30)\n    at new Sequelize (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\lib\\sequelize.js:194:20)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\dist\\config\\database.js:70:41)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)\n    at Object..js (node:internal/modules/cjs/loader:1899:10)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)\n    at Module._load (node:internal/modules/cjs/loader:1286:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-07-11 10:34:05","trace":[{"column":15,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\connection-manager.js","function":"ConnectionManager._loadDialectModule","line":55,"method":"_loadDialectModule","native":false},{"column":21,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\connection-manager.js","function":"new ConnectionManager","line":18,"method":null,"native":false},{"column":30,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\index.js","function":"new SqliteDialect","line":13,"method":null,"native":false},{"column":20,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\lib\\sequelize.js","function":"new Sequelize","line":194,"method":null,"native":false},{"column":41,"file":"F:\\proiecte\\fn\\backend\\dist\\config\\database.js","function":null,"line":70,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1734,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1899,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1469,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1286,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Fri Jul 11 2025 10:34:35 GMT+0300 (Ora de vară a Europei de Est)","environment":"development","error":{},"exception":true,"level":"error","message":"uncaughtException: Please install sqlite3 package manually\nError: Please install sqlite3 package manually\n    at ConnectionManager._loadDialectModule (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\abstract\\connection-manager.js:81:15)\n    at new ConnectionManager (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\connection-manager.js:24:21)\n    at new SqliteDialect (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\index.js:15:30)\n    at new Sequelize (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\sequelize.js:368:20)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\src\\config\\database.ts:103:17)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)\n    at Module.m._compile (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)","os":{"loadavg":[0,0,0],"uptime":762267.64},"process":{"argv":["F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\dist\\bin.js","F:\\proiecte\\fn\\backend\\src\\app.ts"],"cwd":"F:\\proiecte\\fn\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":2586905,"external":5702711,"heapTotal":119373824,"heapUsed":71170368,"rss":141836288},"pid":22800,"uid":null,"version":"v24.0.1"},"service":"finance-app-backend","stack":"Error: Please install sqlite3 package manually\n    at ConnectionManager._loadDialectModule (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\abstract\\connection-manager.js:81:15)\n    at new ConnectionManager (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\connection-manager.js:24:21)\n    at new SqliteDialect (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\index.js:15:30)\n    at new Sequelize (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\sequelize.js:368:20)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\src\\config\\database.ts:103:17)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)\n    at Module.m._compile (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)","timestamp":"2025-07-11 10:34:35","trace":[{"column":15,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\abstract\\connection-manager.js","function":"ConnectionManager._loadDialectModule","line":81,"method":"_loadDialectModule","native":false},{"column":21,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\connection-manager.js","function":"new ConnectionManager","line":24,"method":null,"native":false},{"column":30,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\index.js","function":"new SqliteDialect","line":15,"method":null,"native":false},{"column":20,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\sequelize.js","function":"new Sequelize","line":368,"method":null,"native":false},{"column":17,"file":"F:\\proiecte\\fn\\backend\\src\\config\\database.ts","function":null,"line":103,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1734,"method":"_compile","native":false},{"column":23,"file":"F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"loadTS","line":1826,"method":null,"native":false},{"column":12,"file":"F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1469,"method":"load","native":false}]}
{"date":"Fri Jul 11 2025 10:35:24 GMT+0300 (Ora de vară a Europei de Est)","environment":"development","error":{},"exception":true,"level":"error","message":"uncaughtException: Please install sqlite3 package manually\nError: Please install sqlite3 package manually\n    at ConnectionManager._loadDialectModule (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\abstract\\connection-manager.js:81:15)\n    at new ConnectionManager (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\connection-manager.js:24:21)\n    at new SqliteDialect (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\index.js:15:30)\n    at new Sequelize (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\sequelize.js:368:20)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\src\\config\\database.ts:103:17)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)\n    at Module.m._compile (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)","os":{"loadavg":[0,0,0],"uptime":762316.218},"process":{"argv":["F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\dist\\bin.js","F:\\proiecte\\fn\\backend\\src\\app.ts"],"cwd":"F:\\proiecte\\fn\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":5138208,"external":8254014,"heapTotal":119111680,"heapUsed":71540256,"rss":147267584},"pid":14312,"uid":null,"version":"v24.0.1"},"service":"finance-app-backend","stack":"Error: Please install sqlite3 package manually\n    at ConnectionManager._loadDialectModule (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\abstract\\connection-manager.js:81:15)\n    at new ConnectionManager (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\connection-manager.js:24:21)\n    at new SqliteDialect (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\index.js:15:30)\n    at new Sequelize (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\sequelize.js:368:20)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\src\\config\\database.ts:103:17)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)\n    at Module.m._compile (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)","timestamp":"2025-07-11 10:35:24","trace":[{"column":15,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\abstract\\connection-manager.js","function":"ConnectionManager._loadDialectModule","line":81,"method":"_loadDialectModule","native":false},{"column":21,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\connection-manager.js","function":"new ConnectionManager","line":24,"method":null,"native":false},{"column":30,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\index.js","function":"new SqliteDialect","line":15,"method":null,"native":false},{"column":20,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\sequelize.js","function":"new Sequelize","line":368,"method":null,"native":false},{"column":17,"file":"F:\\proiecte\\fn\\backend\\src\\config\\database.ts","function":null,"line":103,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1734,"method":"_compile","native":false},{"column":23,"file":"F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"loadTS","line":1826,"method":null,"native":false},{"column":12,"file":"F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1469,"method":"load","native":false}]}
{"date":"Fri Jul 11 2025 10:35:42 GMT+0300 (Ora de vară a Europei de Est)","environment":"development","error":{},"exception":true,"level":"error","message":"uncaughtException: Please install sqlite3 package manually\nError: Please install sqlite3 package manually\n    at ConnectionManager._loadDialectModule (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\abstract\\connection-manager.js:81:15)\n    at new ConnectionManager (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\connection-manager.js:24:21)\n    at new SqliteDialect (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\index.js:15:30)\n    at new Sequelize (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\sequelize.js:368:20)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\src\\config\\database.ts:103:17)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)\n    at Module.m._compile (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)","os":{"loadavg":[0,0,0],"uptime":762334.171},"process":{"argv":["F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\dist\\bin.js","F:\\proiecte\\fn\\backend\\src\\app.ts"],"cwd":"F:\\proiecte\\fn\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":5128874,"external":8244680,"heapTotal":119373824,"heapUsed":71468216,"rss":145797120},"pid":11720,"uid":null,"version":"v24.0.1"},"service":"finance-app-backend","stack":"Error: Please install sqlite3 package manually\n    at ConnectionManager._loadDialectModule (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\abstract\\connection-manager.js:81:15)\n    at new ConnectionManager (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\connection-manager.js:24:21)\n    at new SqliteDialect (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\index.js:15:30)\n    at new Sequelize (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\sequelize.js:368:20)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\src\\config\\database.ts:103:17)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)\n    at Module.m._compile (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)","timestamp":"2025-07-11 10:35:42","trace":[{"column":15,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\abstract\\connection-manager.js","function":"ConnectionManager._loadDialectModule","line":81,"method":"_loadDialectModule","native":false},{"column":21,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\connection-manager.js","function":"new ConnectionManager","line":24,"method":null,"native":false},{"column":30,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\index.js","function":"new SqliteDialect","line":15,"method":null,"native":false},{"column":20,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\sequelize.js","function":"new Sequelize","line":368,"method":null,"native":false},{"column":17,"file":"F:\\proiecte\\fn\\backend\\src\\config\\database.ts","function":null,"line":103,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1734,"method":"_compile","native":false},{"column":23,"file":"F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"loadTS","line":1826,"method":null,"native":false},{"column":12,"file":"F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1469,"method":"load","native":false}]}
{"date":"Fri Jul 11 2025 10:36:42 GMT+0300 (Ora de vară a Europei de Est)","environment":"development","error":{},"exception":true,"level":"error","message":"uncaughtException: Please install sqlite3 package manually\nError: Please install sqlite3 package manually\n    at ConnectionManager._loadDialectModule (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\abstract\\connection-manager.js:81:15)\n    at new ConnectionManager (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\connection-manager.js:24:21)\n    at new SqliteDialect (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\index.js:15:30)\n    at new Sequelize (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\sequelize.js:368:20)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\src\\config\\database.ts:103:17)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)\n    at Module.m._compile (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)","os":{"loadavg":[0,0,0],"uptime":762394.453},"process":{"argv":["F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\dist\\bin.js","F:\\proiecte\\fn\\backend\\src\\app.ts"],"cwd":"F:\\proiecte\\fn\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":5128994,"external":8244800,"heapTotal":119635968,"heapUsed":71410472,"rss":144687104},"pid":23568,"uid":null,"version":"v24.0.1"},"service":"finance-app-backend","stack":"Error: Please install sqlite3 package manually\n    at ConnectionManager._loadDialectModule (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\abstract\\connection-manager.js:81:15)\n    at new ConnectionManager (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\connection-manager.js:24:21)\n    at new SqliteDialect (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\index.js:15:30)\n    at new Sequelize (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\sequelize.js:368:20)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\src\\config\\database.ts:103:17)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)\n    at Module.m._compile (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)","timestamp":"2025-07-11 10:36:42","trace":[{"column":15,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\abstract\\connection-manager.js","function":"ConnectionManager._loadDialectModule","line":81,"method":"_loadDialectModule","native":false},{"column":21,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\connection-manager.js","function":"new ConnectionManager","line":24,"method":null,"native":false},{"column":30,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\index.js","function":"new SqliteDialect","line":15,"method":null,"native":false},{"column":20,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\sequelize.js","function":"new Sequelize","line":368,"method":null,"native":false},{"column":17,"file":"F:\\proiecte\\fn\\backend\\src\\config\\database.ts","function":null,"line":103,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1734,"method":"_compile","native":false},{"column":23,"file":"F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"loadTS","line":1826,"method":null,"native":false},{"column":12,"file":"F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1469,"method":"load","native":false}]}
{"date":"Fri Jul 11 2025 10:37:19 GMT+0300 (Ora de vară a Europei de Est)","environment":"development","error":{},"exception":true,"level":"error","message":"uncaughtException: Please install sqlite3 package manually\nError: Please install sqlite3 package manually\n    at ConnectionManager._loadDialectModule (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\connection-manager.js:55:15)\n    at new ConnectionManager (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\connection-manager.js:18:21)\n    at new SqliteDialect (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\index.js:13:30)\n    at new Sequelize (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\lib\\sequelize.js:194:20)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\dist\\config\\database.js:70:41)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)\n    at Object..js (node:internal/modules/cjs/loader:1899:10)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)\n    at Module._load (node:internal/modules/cjs/loader:1286:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":762432.062},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","F:\\proiecte\\fn\\backend\\dist\\app.js"],"cwd":"F:\\proiecte\\fn\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":19151,"external":2341701,"heapTotal":56766464,"heapUsed":26555376,"rss":86077440},"pid":19360,"uid":null,"version":"v24.0.1"},"service":"finance-app-backend","stack":"Error: Please install sqlite3 package manually\n    at ConnectionManager._loadDialectModule (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\connection-manager.js:55:15)\n    at new ConnectionManager (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\connection-manager.js:18:21)\n    at new SqliteDialect (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\index.js:13:30)\n    at new Sequelize (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\lib\\sequelize.js:194:20)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\dist\\config\\database.js:70:41)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)\n    at Object..js (node:internal/modules/cjs/loader:1899:10)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)\n    at Module._load (node:internal/modules/cjs/loader:1286:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-07-11 10:37:19","trace":[{"column":15,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\connection-manager.js","function":"ConnectionManager._loadDialectModule","line":55,"method":"_loadDialectModule","native":false},{"column":21,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\connection-manager.js","function":"new ConnectionManager","line":18,"method":null,"native":false},{"column":30,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\index.js","function":"new SqliteDialect","line":13,"method":null,"native":false},{"column":20,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\lib\\sequelize.js","function":"new Sequelize","line":194,"method":null,"native":false},{"column":41,"file":"F:\\proiecte\\fn\\backend\\dist\\config\\database.js","function":null,"line":70,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1734,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1899,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1469,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1286,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Fri Jul 11 2025 10:37:36 GMT+0300 (Ora de vară a Europei de Est)","environment":"development","error":{},"exception":true,"level":"error","message":"uncaughtException: Please install sqlite3 package manually\nError: Please install sqlite3 package manually\n    at ConnectionManager._loadDialectModule (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\connection-manager.js:55:15)\n    at new ConnectionManager (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\connection-manager.js:18:21)\n    at new SqliteDialect (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\index.js:13:30)\n    at new Sequelize (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\lib\\sequelize.js:194:20)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\dist\\config\\database.js:70:41)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)\n    at Object..js (node:internal/modules/cjs/loader:1899:10)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)\n    at Module._load (node:internal/modules/cjs/loader:1286:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","os":{"loadavg":[0,0,0],"uptime":762448.921},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","F:\\proiecte\\fn\\backend\\dist\\app.js"],"cwd":"F:\\proiecte\\fn\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":19151,"external":2341701,"heapTotal":56504320,"heapUsed":26475432,"rss":86331392},"pid":9312,"uid":null,"version":"v24.0.1"},"service":"finance-app-backend","stack":"Error: Please install sqlite3 package manually\n    at ConnectionManager._loadDialectModule (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\connection-manager.js:55:15)\n    at new ConnectionManager (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\connection-manager.js:18:21)\n    at new SqliteDialect (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\index.js:13:30)\n    at new Sequelize (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\lib\\sequelize.js:194:20)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\dist\\config\\database.js:70:41)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)\n    at Object..js (node:internal/modules/cjs/loader:1899:10)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)\n    at Module._load (node:internal/modules/cjs/loader:1286:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)","timestamp":"2025-07-11 10:37:36","trace":[{"column":15,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\connection-manager.js","function":"ConnectionManager._loadDialectModule","line":55,"method":"_loadDialectModule","native":false},{"column":21,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\connection-manager.js","function":"new ConnectionManager","line":18,"method":null,"native":false},{"column":30,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\lib\\dialects\\sqlite\\index.js","function":"new SqliteDialect","line":13,"method":null,"native":false},{"column":20,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\lib\\sequelize.js","function":"new Sequelize","line":194,"method":null,"native":false},{"column":41,"file":"F:\\proiecte\\fn\\backend\\dist\\config\\database.js","function":null,"line":70,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1734,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"Object..js","line":1899,"method":".js","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1469,"method":"load","native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1286,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false}]}
{"date":"Fri Jul 11 2025 10:39:00 GMT+0300 (Ora de vară a Europei de Est)","environment":"development","error":{},"exception":true,"level":"error","message":"uncaughtException: Please install sqlite3 package manually\nError: Please install sqlite3 package manually\n    at ConnectionManager._loadDialectModule (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\abstract\\connection-manager.js:81:15)\n    at new ConnectionManager (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\connection-manager.js:24:21)\n    at new SqliteDialect (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\index.js:15:30)\n    at new Sequelize (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\sequelize.js:368:20)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\src\\config\\database.ts:103:17)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)\n    at Module.m._compile (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)","os":{"loadavg":[0,0,0],"uptime":762532.234},"process":{"argv":["F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\dist\\bin.js","F:\\proiecte\\fn\\backend\\src\\app.ts"],"cwd":"F:\\proiecte\\fn\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":5128938,"external":8244744,"heapTotal":119373824,"heapUsed":71451248,"rss":145473536},"pid":3688,"uid":null,"version":"v24.0.1"},"service":"finance-app-backend","stack":"Error: Please install sqlite3 package manually\n    at ConnectionManager._loadDialectModule (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\abstract\\connection-manager.js:81:15)\n    at new ConnectionManager (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\connection-manager.js:24:21)\n    at new SqliteDialect (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\index.js:15:30)\n    at new Sequelize (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\sequelize.js:368:20)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\src\\config\\database.ts:103:17)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)\n    at Module.m._compile (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)","timestamp":"2025-07-11 10:39:00","trace":[{"column":15,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\abstract\\connection-manager.js","function":"ConnectionManager._loadDialectModule","line":81,"method":"_loadDialectModule","native":false},{"column":21,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\connection-manager.js","function":"new ConnectionManager","line":24,"method":null,"native":false},{"column":30,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\index.js","function":"new SqliteDialect","line":15,"method":null,"native":false},{"column":20,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\sequelize.js","function":"new Sequelize","line":368,"method":null,"native":false},{"column":17,"file":"F:\\proiecte\\fn\\backend\\src\\config\\database.ts","function":null,"line":103,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1734,"method":"_compile","native":false},{"column":23,"file":"F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"loadTS","line":1826,"method":null,"native":false},{"column":12,"file":"F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1469,"method":"load","native":false}]}
{"date":"Fri Jul 11 2025 10:39:37 GMT+0300 (Ora de vară a Europei de Est)","environment":"development","error":{},"exception":true,"level":"error","message":"uncaughtException: Please install sqlite3 package manually\nError: Please install sqlite3 package manually\n    at ConnectionManager._loadDialectModule (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\abstract\\connection-manager.js:81:15)\n    at new ConnectionManager (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\connection-manager.js:24:21)\n    at new SqliteDialect (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\index.js:15:30)\n    at new Sequelize (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\sequelize.js:368:20)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\src\\config\\database.ts:103:17)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)\n    at Module.m._compile (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)","os":{"loadavg":[0,0,0],"uptime":762569.593},"process":{"argv":["F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\dist\\bin.js","F:\\proiecte\\fn\\backend\\src\\app.ts"],"cwd":"F:\\proiecte\\fn\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":5138304,"external":8254110,"heapTotal":119373824,"heapUsed":71895136,"rss":145842176},"pid":6848,"uid":null,"version":"v24.0.1"},"service":"finance-app-backend","stack":"Error: Please install sqlite3 package manually\n    at ConnectionManager._loadDialectModule (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\abstract\\connection-manager.js:81:15)\n    at new ConnectionManager (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\connection-manager.js:24:21)\n    at new SqliteDialect (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\index.js:15:30)\n    at new Sequelize (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\sequelize.js:368:20)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\src\\config\\database.ts:103:17)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)\n    at Module.m._compile (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)","timestamp":"2025-07-11 10:39:37","trace":[{"column":15,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\abstract\\connection-manager.js","function":"ConnectionManager._loadDialectModule","line":81,"method":"_loadDialectModule","native":false},{"column":21,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\connection-manager.js","function":"new ConnectionManager","line":24,"method":null,"native":false},{"column":30,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\index.js","function":"new SqliteDialect","line":15,"method":null,"native":false},{"column":20,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\sequelize.js","function":"new Sequelize","line":368,"method":null,"native":false},{"column":17,"file":"F:\\proiecte\\fn\\backend\\src\\config\\database.ts","function":null,"line":103,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1734,"method":"_compile","native":false},{"column":23,"file":"F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"loadTS","line":1826,"method":null,"native":false},{"column":12,"file":"F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1469,"method":"load","native":false}]}
{"date":"Fri Jul 11 2025 10:40:04 GMT+0300 (Ora de vară a Europei de Est)","environment":"development","error":{},"exception":true,"level":"error","message":"uncaughtException: Please install sqlite3 package manually\nError: Please install sqlite3 package manually\n    at ConnectionManager._loadDialectModule (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\abstract\\connection-manager.js:81:15)\n    at new ConnectionManager (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\connection-manager.js:24:21)\n    at new SqliteDialect (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\index.js:15:30)\n    at new Sequelize (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\sequelize.js:368:20)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\src\\config\\database.ts:103:17)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)\n    at Module.m._compile (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)","os":{"loadavg":[0,0,0],"uptime":762596.203},"process":{"argv":["F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\dist\\bin.js","F:\\proiecte\\fn\\backend\\src\\app.ts"],"cwd":"F:\\proiecte\\fn\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":5128778,"external":8244584,"heapTotal":119111680,"heapUsed":71602888,"rss":146997248},"pid":12372,"uid":null,"version":"v24.0.1"},"service":"finance-app-backend","stack":"Error: Please install sqlite3 package manually\n    at ConnectionManager._loadDialectModule (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\abstract\\connection-manager.js:81:15)\n    at new ConnectionManager (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\connection-manager.js:24:21)\n    at new SqliteDialect (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\index.js:15:30)\n    at new Sequelize (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\sequelize.js:368:20)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\src\\config\\database.ts:103:17)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)\n    at Module.m._compile (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)","timestamp":"2025-07-11 10:40:04","trace":[{"column":15,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\abstract\\connection-manager.js","function":"ConnectionManager._loadDialectModule","line":81,"method":"_loadDialectModule","native":false},{"column":21,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\connection-manager.js","function":"new ConnectionManager","line":24,"method":null,"native":false},{"column":30,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\index.js","function":"new SqliteDialect","line":15,"method":null,"native":false},{"column":20,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\sequelize.js","function":"new Sequelize","line":368,"method":null,"native":false},{"column":17,"file":"F:\\proiecte\\fn\\backend\\src\\config\\database.ts","function":null,"line":103,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1734,"method":"_compile","native":false},{"column":23,"file":"F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"loadTS","line":1826,"method":null,"native":false},{"column":12,"file":"F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1469,"method":"load","native":false}]}
{"date":"Fri Jul 11 2025 10:48:42 GMT+0300 (Ora de vară a Europei de Est)","environment":"development","error":{},"exception":true,"level":"error","message":"uncaughtException: Please install sqlite3 package manually\nError: Please install sqlite3 package manually\n    at ConnectionManager._loadDialectModule (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\abstract\\connection-manager.js:81:15)\n    at new ConnectionManager (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\connection-manager.js:24:21)\n    at new SqliteDialect (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\index.js:15:30)\n    at new Sequelize (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\sequelize.js:368:20)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\src\\config\\database.ts:103:17)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)\n    at Module.m._compile (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)","os":{"loadavg":[0,0,0],"uptime":763114.296},"process":{"argv":["F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\dist\\bin.js","F:\\proiecte\\fn\\backend\\src\\app.ts"],"cwd":"F:\\proiecte\\fn\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":5138304,"external":8254110,"heapTotal":119111680,"heapUsed":71615912,"rss":146374656},"pid":24936,"uid":null,"version":"v24.0.1"},"service":"finance-app-backend","stack":"Error: Please install sqlite3 package manually\n    at ConnectionManager._loadDialectModule (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\abstract\\connection-manager.js:81:15)\n    at new ConnectionManager (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\connection-manager.js:24:21)\n    at new SqliteDialect (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\index.js:15:30)\n    at new Sequelize (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\sequelize.js:368:20)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\src\\config\\database.ts:103:17)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)\n    at Module.m._compile (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)","timestamp":"2025-07-11 10:48:42","trace":[{"column":15,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\abstract\\connection-manager.js","function":"ConnectionManager._loadDialectModule","line":81,"method":"_loadDialectModule","native":false},{"column":21,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\connection-manager.js","function":"new ConnectionManager","line":24,"method":null,"native":false},{"column":30,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\index.js","function":"new SqliteDialect","line":15,"method":null,"native":false},{"column":20,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\sequelize.js","function":"new Sequelize","line":368,"method":null,"native":false},{"column":17,"file":"F:\\proiecte\\fn\\backend\\src\\config\\database.ts","function":null,"line":103,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1734,"method":"_compile","native":false},{"column":23,"file":"F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"loadTS","line":1826,"method":null,"native":false},{"column":12,"file":"F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1469,"method":"load","native":false}]}
{"date":"Fri Jul 11 2025 10:49:36 GMT+0300 (Ora de vară a Europei de Est)","environment":"development","error":{},"exception":true,"level":"error","message":"uncaughtException: Please install sqlite3 package manually\nError: Please install sqlite3 package manually\n    at ConnectionManager._loadDialectModule (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\abstract\\connection-manager.js:81:15)\n    at new ConnectionManager (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\connection-manager.js:24:21)\n    at new SqliteDialect (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\index.js:15:30)\n    at new Sequelize (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\sequelize.js:368:20)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\src\\config\\database.ts:103:17)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)\n    at Module.m._compile (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)","os":{"loadavg":[0,0,0],"uptime":763169.046},"process":{"argv":["F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\dist\\bin.js","F:\\proiecte\\fn\\backend\\src\\app.ts"],"cwd":"F:\\proiecte\\fn\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":5138472,"external":8254278,"heapTotal":119373824,"heapUsed":71585192,"rss":144814080},"pid":21564,"uid":null,"version":"v24.0.1"},"service":"finance-app-backend","stack":"Error: Please install sqlite3 package manually\n    at ConnectionManager._loadDialectModule (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\abstract\\connection-manager.js:81:15)\n    at new ConnectionManager (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\connection-manager.js:24:21)\n    at new SqliteDialect (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\index.js:15:30)\n    at new Sequelize (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\sequelize.js:368:20)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\src\\config\\database.ts:103:17)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)\n    at Module.m._compile (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)","timestamp":"2025-07-11 10:49:36","trace":[{"column":15,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\abstract\\connection-manager.js","function":"ConnectionManager._loadDialectModule","line":81,"method":"_loadDialectModule","native":false},{"column":21,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\connection-manager.js","function":"new ConnectionManager","line":24,"method":null,"native":false},{"column":30,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\index.js","function":"new SqliteDialect","line":15,"method":null,"native":false},{"column":20,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\sequelize.js","function":"new Sequelize","line":368,"method":null,"native":false},{"column":17,"file":"F:\\proiecte\\fn\\backend\\src\\config\\database.ts","function":null,"line":103,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1734,"method":"_compile","native":false},{"column":23,"file":"F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"loadTS","line":1826,"method":null,"native":false},{"column":12,"file":"F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1469,"method":"load","native":false}]}
{"date":"Fri Jul 11 2025 10:50:15 GMT+0300 (Ora de vară a Europei de Est)","environment":"development","error":{},"exception":true,"level":"error","message":"uncaughtException: Please install sqlite3 package manually\nError: Please install sqlite3 package manually\n    at ConnectionManager._loadDialectModule (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\abstract\\connection-manager.js:81:15)\n    at new ConnectionManager (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\connection-manager.js:24:21)\n    at new SqliteDialect (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\index.js:15:30)\n    at new Sequelize (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\sequelize.js:368:20)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\src\\config\\database.ts:103:17)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)\n    at Module.m._compile (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)","os":{"loadavg":[0,0,0],"uptime":763208.015},"process":{"argv":["F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\dist\\bin.js","F:\\proiecte\\fn\\backend\\src\\app.ts"],"cwd":"F:\\proiecte\\fn\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":5128985,"external":8244791,"heapTotal":119111680,"heapUsed":71098264,"rss":144297984},"pid":20944,"uid":null,"version":"v24.0.1"},"service":"finance-app-backend","stack":"Error: Please install sqlite3 package manually\n    at ConnectionManager._loadDialectModule (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\abstract\\connection-manager.js:81:15)\n    at new ConnectionManager (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\connection-manager.js:24:21)\n    at new SqliteDialect (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\index.js:15:30)\n    at new Sequelize (F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\sequelize.js:368:20)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\src\\config\\database.ts:103:17)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)\n    at Module.m._compile (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1618:23)\n    at loadTS (node:internal/modules/cjs/loader:1826:10)\n    at Object.require.extensions.<computed> [as .ts] (F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts:1621:12)\n    at Module.load (node:internal/modules/cjs/loader:1469:32)","timestamp":"2025-07-11 10:50:15","trace":[{"column":15,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\abstract\\connection-manager.js","function":"ConnectionManager._loadDialectModule","line":81,"method":"_loadDialectModule","native":false},{"column":21,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\connection-manager.js","function":"new ConnectionManager","line":24,"method":null,"native":false},{"column":30,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\dialects\\sqlite\\index.js","function":"new SqliteDialect","line":15,"method":null,"native":false},{"column":20,"file":"F:\\proiecte\\fn\\backend\\node_modules\\sequelize\\src\\sequelize.js","function":"new Sequelize","line":368,"method":null,"native":false},{"column":17,"file":"F:\\proiecte\\fn\\backend\\src\\config\\database.ts","function":null,"line":103,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1734,"method":"_compile","native":false},{"column":23,"file":"F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Module.m._compile","line":1618,"method":"_compile","native":false},{"column":10,"file":"node:internal/modules/cjs/loader","function":"loadTS","line":1826,"method":null,"native":false},{"column":12,"file":"F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\src\\index.ts","function":"Object.require.extensions.<computed> [as .ts]","line":1621,"method":"ts]","native":false},{"column":32,"file":"node:internal/modules/cjs/loader","function":"Module.load","line":1469,"method":"load","native":false}]}
{"date":"Fri Jul 11 2025 10:51:35 GMT+0300 (Ora de vară a Europei de Est)","environment":"development","error":{"code":"MODULE_NOT_FOUND","requireStack":["F:\\proiecte\\fn\\backend\\src\\routes\\auth.ts","F:\\proiecte\\fn\\backend\\src\\app.ts"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../middleware/validation'\nRequire stack:\n- F:\\proiecte\\fn\\backend\\src\\routes\\auth.ts\n- F:\\proiecte\\fn\\backend\\src\\app.ts\nError: Cannot find module '../middleware/validation'\nRequire stack:\n- F:\\proiecte\\fn\\backend\\src\\routes\\auth.ts\n- F:\\proiecte\\fn\\backend\\src\\app.ts\n    at Module.<anonymous> (node:internal/modules/cjs/loader:1405:15)\n    at Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (F:\\proiecte\\fn\\backend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Module._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\src\\routes\\auth.ts:4:1)","os":{"loadavg":[0,0,0],"uptime":763287.5},"process":{"argv":["F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\dist\\bin.js","F:\\proiecte\\fn\\backend\\src\\app.ts"],"cwd":"F:\\proiecte\\fn\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":5160706,"external":8243142,"heapTotal":78942208,"heapUsed":60250680,"rss":134230016},"pid":23140,"uid":null,"version":"v24.0.1"},"service":"finance-app-backend","stack":"Error: Cannot find module '../middleware/validation'\nRequire stack:\n- F:\\proiecte\\fn\\backend\\src\\routes\\auth.ts\n- F:\\proiecte\\fn\\backend\\src\\app.ts\n    at Module.<anonymous> (node:internal/modules/cjs/loader:1405:15)\n    at Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (F:\\proiecte\\fn\\backend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Module._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\src\\routes\\auth.ts:4:1)","timestamp":"2025-07-11 10:51:35","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":null,"line":1405,"method":null,"native":false},{"column":30,"file":"F:\\proiecte\\fn\\backend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js","function":"Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename]","line":811,"method":"installedValue [as _resolveFilename]","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"defaultResolveImpl","line":1061,"method":null,"native":false},{"column":22,"file":"node:internal/modules/cjs/loader","function":"resolveForCJSWithHooks","line":1066,"method":null,"native":false},{"column":37,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1215,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":235,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1491,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":135,"method":null,"native":false},{"column":1,"file":"F:\\proiecte\\fn\\backend\\src\\routes\\auth.ts","function":null,"line":4,"method":null,"native":false}]}
{"date":"Fri Jul 11 2025 10:53:24 GMT+0300 (Ora de vară a Europei de Est)","environment":"development","error":{"code":"MODULE_NOT_FOUND","requireStack":["F:\\proiecte\\fn\\backend\\src\\routes\\categories.ts","F:\\proiecte\\fn\\backend\\src\\app.ts"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../middleware/subscriptionLimits'\nRequire stack:\n- F:\\proiecte\\fn\\backend\\src\\routes\\categories.ts\n- F:\\proiecte\\fn\\backend\\src\\app.ts\nError: Cannot find module '../middleware/subscriptionLimits'\nRequire stack:\n- F:\\proiecte\\fn\\backend\\src\\routes\\categories.ts\n- F:\\proiecte\\fn\\backend\\src\\app.ts\n    at Module.<anonymous> (node:internal/modules/cjs/loader:1405:15)\n    at Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (F:\\proiecte\\fn\\backend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Module._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\src\\routes\\categories.ts:19:1)","os":{"loadavg":[0,0,0],"uptime":763397.046},"process":{"argv":["F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\dist\\bin.js","F:\\proiecte\\fn\\backend\\src\\app.ts"],"cwd":"F:\\proiecte\\fn\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":5180484,"external":8262920,"heapTotal":118284288,"heapUsed":76376096,"rss":149692416},"pid":12064,"uid":null,"version":"v24.0.1"},"service":"finance-app-backend","stack":"Error: Cannot find module '../middleware/subscriptionLimits'\nRequire stack:\n- F:\\proiecte\\fn\\backend\\src\\routes\\categories.ts\n- F:\\proiecte\\fn\\backend\\src\\app.ts\n    at Module.<anonymous> (node:internal/modules/cjs/loader:1405:15)\n    at Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (F:\\proiecte\\fn\\backend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Module._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\src\\routes\\categories.ts:19:1)","timestamp":"2025-07-11 10:53:24","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":null,"line":1405,"method":null,"native":false},{"column":30,"file":"F:\\proiecte\\fn\\backend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js","function":"Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename]","line":811,"method":"installedValue [as _resolveFilename]","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"defaultResolveImpl","line":1061,"method":null,"native":false},{"column":22,"file":"node:internal/modules/cjs/loader","function":"resolveForCJSWithHooks","line":1066,"method":null,"native":false},{"column":37,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1215,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":235,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1491,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":135,"method":null,"native":false},{"column":1,"file":"F:\\proiecte\\fn\\backend\\src\\routes\\categories.ts","function":null,"line":19,"method":null,"native":false}]}
{"date":"Fri Jul 11 2025 11:22:11 GMT+0300 (Ora de vară a Europei de Est)","environment":"development","error":{"code":"MODULE_NOT_FOUND","requireStack":["F:\\proiecte\\fn\\backend\\src\\controllers\\expenseController.ts","F:\\proiecte\\fn\\backend\\src\\routes\\expenses.ts","F:\\proiecte\\fn\\backend\\src\\app.ts"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../services/subscriptionService'\nRequire stack:\n- F:\\proiecte\\fn\\backend\\src\\controllers\\expenseController.ts\n- F:\\proiecte\\fn\\backend\\src\\routes\\expenses.ts\n- F:\\proiecte\\fn\\backend\\src\\app.ts\nError: Cannot find module '../services/subscriptionService'\nRequire stack:\n- F:\\proiecte\\fn\\backend\\src\\controllers\\expenseController.ts\n- F:\\proiecte\\fn\\backend\\src\\routes\\expenses.ts\n- F:\\proiecte\\fn\\backend\\src\\app.ts\n    at Module.<anonymous> (node:internal/modules/cjs/loader:1405:15)\n    at Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (F:\\proiecte\\fn\\backend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Module._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\src\\controllers\\expenseController.ts:2:29)","os":{"loadavg":[0,0,0],"uptime":765124.031},"process":{"argv":["F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\dist\\bin.js","F:\\proiecte\\fn\\backend\\src\\app.ts"],"cwd":"F:\\proiecte\\fn\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":5200653,"external":8283089,"heapTotal":123002880,"heapUsed":65031864,"rss":162091008},"pid":8584,"uid":null,"version":"v24.0.1"},"service":"finance-app-backend","stack":"Error: Cannot find module '../services/subscriptionService'\nRequire stack:\n- F:\\proiecte\\fn\\backend\\src\\controllers\\expenseController.ts\n- F:\\proiecte\\fn\\backend\\src\\routes\\expenses.ts\n- F:\\proiecte\\fn\\backend\\src\\app.ts\n    at Module.<anonymous> (node:internal/modules/cjs/loader:1405:15)\n    at Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (F:\\proiecte\\fn\\backend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Module._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\src\\controllers\\expenseController.ts:2:29)","timestamp":"2025-07-11 11:22:11","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":null,"line":1405,"method":null,"native":false},{"column":30,"file":"F:\\proiecte\\fn\\backend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js","function":"Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename]","line":811,"method":"installedValue [as _resolveFilename]","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"defaultResolveImpl","line":1061,"method":null,"native":false},{"column":22,"file":"node:internal/modules/cjs/loader","function":"resolveForCJSWithHooks","line":1066,"method":null,"native":false},{"column":37,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1215,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":235,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1491,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":135,"method":null,"native":false},{"column":29,"file":"F:\\proiecte\\fn\\backend\\src\\controllers\\expenseController.ts","function":null,"line":2,"method":null,"native":false}]}
{"date":"Fri Jul 11 2025 11:23:07 GMT+0300 (Ora de vară a Europei de Est)","environment":"development","error":{"code":"MODULE_NOT_FOUND","requireStack":["F:\\proiecte\\fn\\backend\\src\\controllers\\expenseController.ts","F:\\proiecte\\fn\\backend\\src\\routes\\expenses.ts","F:\\proiecte\\fn\\backend\\src\\app.ts"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../services/subscriptionService'\nRequire stack:\n- F:\\proiecte\\fn\\backend\\src\\controllers\\expenseController.ts\n- F:\\proiecte\\fn\\backend\\src\\routes\\expenses.ts\n- F:\\proiecte\\fn\\backend\\src\\app.ts\nError: Cannot find module '../services/subscriptionService'\nRequire stack:\n- F:\\proiecte\\fn\\backend\\src\\controllers\\expenseController.ts\n- F:\\proiecte\\fn\\backend\\src\\routes\\expenses.ts\n- F:\\proiecte\\fn\\backend\\src\\app.ts\n    at Module.<anonymous> (node:internal/modules/cjs/loader:1405:15)\n    at Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (F:\\proiecte\\fn\\backend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Module._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\src\\controllers\\expenseController.ts:2:29)","os":{"loadavg":[0,0,0],"uptime":765179.828},"process":{"argv":["F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\dist\\bin.js","F:\\proiecte\\fn\\backend\\src\\app.ts"],"cwd":"F:\\proiecte\\fn\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":5200741,"external":8283177,"heapTotal":123527168,"heapUsed":67323904,"rss":162729984},"pid":18140,"uid":null,"version":"v24.0.1"},"service":"finance-app-backend","stack":"Error: Cannot find module '../services/subscriptionService'\nRequire stack:\n- F:\\proiecte\\fn\\backend\\src\\controllers\\expenseController.ts\n- F:\\proiecte\\fn\\backend\\src\\routes\\expenses.ts\n- F:\\proiecte\\fn\\backend\\src\\app.ts\n    at Module.<anonymous> (node:internal/modules/cjs/loader:1405:15)\n    at Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (F:\\proiecte\\fn\\backend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Module._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\src\\controllers\\expenseController.ts:2:29)","timestamp":"2025-07-11 11:23:07","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":null,"line":1405,"method":null,"native":false},{"column":30,"file":"F:\\proiecte\\fn\\backend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js","function":"Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename]","line":811,"method":"installedValue [as _resolveFilename]","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"defaultResolveImpl","line":1061,"method":null,"native":false},{"column":22,"file":"node:internal/modules/cjs/loader","function":"resolveForCJSWithHooks","line":1066,"method":null,"native":false},{"column":37,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1215,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":235,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1491,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":135,"method":null,"native":false},{"column":29,"file":"F:\\proiecte\\fn\\backend\\src\\controllers\\expenseController.ts","function":null,"line":2,"method":null,"native":false}]}
{"date":"Fri Jul 11 2025 11:24:58 GMT+0300 (Ora de vară a Europei de Est)","environment":"development","error":{"code":"MODULE_NOT_FOUND","requireStack":["F:\\proiecte\\fn\\backend\\src\\controllers\\exportController.ts","F:\\proiecte\\fn\\backend\\src\\routes\\export.ts","F:\\proiecte\\fn\\backend\\src\\app.ts"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../utils/AppError'\nRequire stack:\n- F:\\proiecte\\fn\\backend\\src\\controllers\\exportController.ts\n- F:\\proiecte\\fn\\backend\\src\\routes\\export.ts\n- F:\\proiecte\\fn\\backend\\src\\app.ts\nError: Cannot find module '../utils/AppError'\nRequire stack:\n- F:\\proiecte\\fn\\backend\\src\\controllers\\exportController.ts\n- F:\\proiecte\\fn\\backend\\src\\routes\\export.ts\n- F:\\proiecte\\fn\\backend\\src\\app.ts\n    at Module.<anonymous> (node:internal/modules/cjs/loader:1405:15)\n    at Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (F:\\proiecte\\fn\\backend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Module._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\src\\controllers\\exportController.ts:3:18)","os":{"loadavg":[0,0,0],"uptime":765290.265},"process":{"argv":["F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\dist\\bin.js","F:\\proiecte\\fn\\backend\\src\\app.ts"],"cwd":"F:\\proiecte\\fn\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":5155435,"external":8237871,"heapTotal":117235712,"heapUsed":59502592,"rss":157114368},"pid":10932,"uid":null,"version":"v24.0.1"},"service":"finance-app-backend","stack":"Error: Cannot find module '../utils/AppError'\nRequire stack:\n- F:\\proiecte\\fn\\backend\\src\\controllers\\exportController.ts\n- F:\\proiecte\\fn\\backend\\src\\routes\\export.ts\n- F:\\proiecte\\fn\\backend\\src\\app.ts\n    at Module.<anonymous> (node:internal/modules/cjs/loader:1405:15)\n    at Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (F:\\proiecte\\fn\\backend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Module._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\src\\controllers\\exportController.ts:3:18)","timestamp":"2025-07-11 11:24:58","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":null,"line":1405,"method":null,"native":false},{"column":30,"file":"F:\\proiecte\\fn\\backend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js","function":"Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename]","line":811,"method":"installedValue [as _resolveFilename]","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"defaultResolveImpl","line":1061,"method":null,"native":false},{"column":22,"file":"node:internal/modules/cjs/loader","function":"resolveForCJSWithHooks","line":1066,"method":null,"native":false},{"column":37,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1215,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":235,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1491,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":135,"method":null,"native":false},{"column":18,"file":"F:\\proiecte\\fn\\backend\\src\\controllers\\exportController.ts","function":null,"line":3,"method":null,"native":false}]}
{"date":"Fri Jul 11 2025 11:27:17 GMT+0300 (Ora de vară a Europei de Est)","environment":"development","error":{"code":"MODULE_NOT_FOUND","requireStack":["F:\\proiecte\\fn\\backend\\src\\controllers\\subscriptionController.ts","F:\\proiecte\\fn\\backend\\src\\routes\\subscription.ts","F:\\proiecte\\fn\\backend\\src\\app.ts"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../services/stripeService'\nRequire stack:\n- F:\\proiecte\\fn\\backend\\src\\controllers\\subscriptionController.ts\n- F:\\proiecte\\fn\\backend\\src\\routes\\subscription.ts\n- F:\\proiecte\\fn\\backend\\src\\app.ts\nError: Cannot find module '../services/stripeService'\nRequire stack:\n- F:\\proiecte\\fn\\backend\\src\\controllers\\subscriptionController.ts\n- F:\\proiecte\\fn\\backend\\src\\routes\\subscription.ts\n- F:\\proiecte\\fn\\backend\\src\\app.ts\n    at Module.<anonymous> (node:internal/modules/cjs/loader:1405:15)\n    at Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (F:\\proiecte\\fn\\backend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Module._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\src\\controllers\\subscriptionController.ts:4:1)","os":{"loadavg":[0,0,0],"uptime":765430.015},"process":{"argv":["F:\\proiecte\\fn\\backend\\node_modules\\ts-node\\dist\\bin.js","F:\\proiecte\\fn\\backend\\src\\app.ts"],"cwd":"F:\\proiecte\\fn\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":5288762,"external":8371198,"heapTotal":123715584,"heapUsed":78388920,"rss":168366080},"pid":25772,"uid":null,"version":"v24.0.1"},"service":"finance-app-backend","stack":"Error: Cannot find module '../services/stripeService'\nRequire stack:\n- F:\\proiecte\\fn\\backend\\src\\controllers\\subscriptionController.ts\n- F:\\proiecte\\fn\\backend\\src\\routes\\subscription.ts\n- F:\\proiecte\\fn\\backend\\src\\app.ts\n    at Module.<anonymous> (node:internal/modules/cjs/loader:1405:15)\n    at Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename] (F:\\proiecte\\fn\\backend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js:811:30)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Module._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\src\\controllers\\subscriptionController.ts:4:1)","timestamp":"2025-07-11 11:27:17","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":null,"line":1405,"method":null,"native":false},{"column":30,"file":"F:\\proiecte\\fn\\backend\\node_modules\\@cspotcode\\source-map-support\\source-map-support.js","function":"Module._resolveFilename.sharedData.moduleResolveFilenameHook.installedValue [as _resolveFilename]","line":811,"method":"installedValue [as _resolveFilename]","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"defaultResolveImpl","line":1061,"method":null,"native":false},{"column":22,"file":"node:internal/modules/cjs/loader","function":"resolveForCJSWithHooks","line":1066,"method":null,"native":false},{"column":37,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1215,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":235,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1491,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":135,"method":null,"native":false},{"column":1,"file":"F:\\proiecte\\fn\\backend\\src\\controllers\\subscriptionController.ts","function":null,"line":4,"method":null,"native":false}]}
{"date":"Fri Jul 11 2025 16:36:19 GMT+0300 (Ora de vară a Europei de Est)","environment":"development","error":{"code":"MODULE_NOT_FOUND","requireStack":["F:\\proiecte\\fn\\backend\\dist\\routes\\subscription.js","F:\\proiecte\\fn\\backend\\dist\\app.js"]},"exception":true,"level":"error","message":"uncaughtException: Cannot find module '../middleware/subscription'\nRequire stack:\n- F:\\proiecte\\fn\\backend\\dist\\routes\\subscription.js\n- F:\\proiecte\\fn\\backend\\dist\\app.js\nError: Cannot find module '../middleware/subscription'\nRequire stack:\n- F:\\proiecte\\fn\\backend\\dist\\routes\\subscription.js\n- F:\\proiecte\\fn\\backend\\dist\\app.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Module._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\dist\\routes\\subscription.js:44:45)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","os":{"loadavg":[0,0,0],"uptime":783972.421},"process":{"argv":["C:\\Program Files\\nodejs\\node.exe","F:\\proiecte\\fn\\backend\\dist\\app.js","dist/app.js"],"cwd":"F:\\proiecte\\fn\\backend","execPath":"C:\\Program Files\\nodejs\\node.exe","gid":null,"memoryUsage":{"arrayBuffers":19071,"external":2276474,"heapTotal":55799808,"heapUsed":26066272,"rss":85803008},"pid":24584,"uid":null,"version":"v24.0.1"},"service":"finance-app-backend","stack":"Error: Cannot find module '../middleware/subscription'\nRequire stack:\n- F:\\proiecte\\fn\\backend\\dist\\routes\\subscription.js\n- F:\\proiecte\\fn\\backend\\dist\\app.js\n    at Module._resolveFilename (node:internal/modules/cjs/loader:1405:15)\n    at defaultResolveImpl (node:internal/modules/cjs/loader:1061:19)\n    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1066:22)\n    at Module._load (node:internal/modules/cjs/loader:1215:37)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)\n    at Module.require (node:internal/modules/cjs/loader:1491:12)\n    at require (node:internal/modules/helpers:135:16)\n    at Object.<anonymous> (F:\\proiecte\\fn\\backend\\dist\\routes\\subscription.js:44:45)\n    at Module._compile (node:internal/modules/cjs/loader:1734:14)","timestamp":"2025-07-11 16:36:19","trace":[{"column":15,"file":"node:internal/modules/cjs/loader","function":"Module._resolveFilename","line":1405,"method":"_resolveFilename","native":false},{"column":19,"file":"node:internal/modules/cjs/loader","function":"defaultResolveImpl","line":1061,"method":null,"native":false},{"column":22,"file":"node:internal/modules/cjs/loader","function":"resolveForCJSWithHooks","line":1066,"method":null,"native":false},{"column":37,"file":"node:internal/modules/cjs/loader","function":"Module._load","line":1215,"method":"_load","native":false},{"column":14,"file":"node:diagnostics_channel","function":"TracingChannel.traceSync","line":322,"method":"traceSync","native":false},{"column":24,"file":"node:internal/modules/cjs/loader","function":"wrapModuleLoad","line":235,"method":null,"native":false},{"column":12,"file":"node:internal/modules/cjs/loader","function":"Module.require","line":1491,"method":"require","native":false},{"column":16,"file":"node:internal/modules/helpers","function":"require","line":135,"method":null,"native":false},{"column":45,"file":"F:\\proiecte\\fn\\backend\\dist\\routes\\subscription.js","function":null,"line":44,"method":null,"native":false},{"column":14,"file":"node:internal/modules/cjs/loader","function":"Module._compile","line":1734,"method":"_compile","native":false}]}
