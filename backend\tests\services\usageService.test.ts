import usageService from '../../src/services/usageService';
import { prisma, createTestUser, createTestCategory, createTestExpense } from '../setup';

describe('UsageService', () => {
  let testUser: any;
  let testCategory: any;

  beforeEach(async () => {
    testUser = await createTestUser();
    testCategory = await createTestCategory(testUser.id);
  });

  describe('getUserUsageStats', () => {
    it('should return correct usage stats for free plan user', async () => {
      // Arrange
      const userId = testUser.id.toString();

      // Act
      const stats = await usageService.getUserUsageStats(userId);

      // Assert
      expect(stats).toMatchObject({
        current_period: {
          expenses: 0,
          categories: 1, // testCategory created
          exports: 0,
        },
        limits: {
          expenses_per_month: 50,
          categories: 5,
          exports_per_month: 3,
        },
        usage_percentage: {
          expenses: 0,
          categories: 20, // 1/5 * 100
          exports: 0,
        },
        plan_type: 'free',
      });
    });

    it('should return correct usage stats for premium plan user', async () => {
      // Arrange
      const premiumUser = await createTestUser({
        subscription_plan: 'premium',
      });
      const userId = premiumUser.id.toString();

      // Act
      const stats = await usageService.getUserUsageStats(userId);

      // Assert
      expect(stats).toMatchObject({
        limits: {
          expenses_per_month: -1, // unlimited
          categories: -1, // unlimited
          exports_per_month: -1, // unlimited
        },
        usage_percentage: {
          expenses: 0,
          categories: 0,
          exports: 0,
        },
        plan_type: 'premium',
      });
    });

    it('should calculate correct expense count for current month', async () => {
      // Arrange
      const userId = testUser.id.toString();
      
      // Create expenses in current month
      await createTestExpense(testUser.id, testCategory.id);
      await createTestExpense(testUser.id, testCategory.id, {
        amount: 15.75,
        description: 'Second expense',
      });

      // Create expense in previous month (should not be counted)
      const lastMonth = new Date();
      lastMonth.setMonth(lastMonth.getMonth() - 1);
      await createTestExpense(testUser.id, testCategory.id, {
        created_at: lastMonth,
      });

      // Act
      const stats = await usageService.getUserUsageStats(userId);

      // Assert
      expect(stats.current_period.expenses).toBe(2);
      expect(stats.usage_percentage.expenses).toBe(4); // 2/50 * 100
    });

    it('should throw error for non-existent user', async () => {
      // Arrange
      const nonExistentUserId = '99999';

      // Act & Assert
      await expect(usageService.getUserUsageStats(nonExistentUserId))
        .rejects.toThrow('User not found');
    });
  });

  describe('canPerformAction', () => {
    it('should allow creating expense when under limit', async () => {
      // Arrange
      const userId = testUser.id.toString();

      // Act
      const canCreate = await usageService.canPerformAction(userId, 'create_expense');

      // Assert
      expect(canCreate).toBe(true);
    });

    it('should deny creating expense when at limit', async () => {
      // Arrange
      const userId = testUser.id.toString();
      
      // Create 50 expenses (free plan limit)
      for (let i = 0; i < 50; i++) {
        await createTestExpense(testUser.id, testCategory.id, {
          description: `Expense ${i + 1}`,
        });
      }

      // Act
      const canCreate = await usageService.canPerformAction(userId, 'create_expense');

      // Assert
      expect(canCreate).toBe(false);
    });

    it('should allow unlimited actions for premium users', async () => {
      // Arrange
      const premiumUser = await createTestUser({
        subscription_plan: 'premium',
      });
      const userId = premiumUser.id.toString();

      // Act
      const canCreateExpense = await usageService.canPerformAction(userId, 'create_expense');
      const canCreateCategory = await usageService.canPerformAction(userId, 'create_category');
      const canExport = await usageService.canPerformAction(userId, 'export_data');

      // Assert
      expect(canCreateExpense).toBe(true);
      expect(canCreateCategory).toBe(true);
      expect(canExport).toBe(true);
    });

    it('should deny creating category when at limit', async () => {
      // Arrange
      const userId = testUser.id.toString();
      
      // Create 4 more categories (total 5, free plan limit)
      for (let i = 0; i < 4; i++) {
        await createTestCategory(testUser.id, {
          name: `Category ${i + 2}`,
        });
      }

      // Act
      const canCreate = await usageService.canPerformAction(userId, 'create_category');

      // Assert
      expect(canCreate).toBe(false);
    });

    it('should allow unknown actions by default', async () => {
      // Arrange
      const userId = testUser.id.toString();

      // Act
      const canPerform = await usageService.canPerformAction(userId, 'unknown_action');

      // Assert
      expect(canPerform).toBe(true);
    });
  });

  describe('incrementUsage', () => {
    it('should create usage log entry', async () => {
      // Arrange
      const userId = testUser.id.toString();
      const action = 'create_expense';

      // Act
      await usageService.incrementUsage(userId, action);

      // Assert
      const usageLog = await prisma.usageLog.findFirst({
        where: {
          user_id: testUser.id,
          action,
        },
      });

      expect(usageLog).toBeTruthy();
      expect(usageLog?.resource).toBe('expense');
    });

    it('should handle different action types correctly', async () => {
      // Arrange
      const userId = testUser.id.toString();

      // Act
      await usageService.incrementUsage(userId, 'create_category');
      await usageService.incrementUsage(userId, 'export_data');

      // Assert
      const categoryLog = await prisma.usageLog.findFirst({
        where: { user_id: testUser.id, action: 'create_category' },
      });
      const exportLog = await prisma.usageLog.findFirst({
        where: { user_id: testUser.id, action: 'export_data' },
      });

      expect(categoryLog?.resource).toBe('category');
      expect(exportLog?.resource).toBe('export');
    });
  });

  describe('getUsageProgress', () => {
    it('should return correct progress information', async () => {
      // Arrange
      const userId = testUser.id.toString();
      
      // Create some usage
      await createTestExpense(testUser.id, testCategory.id);
      await usageService.incrementUsage(userId, 'export_data');

      // Act
      const progress = await usageService.getUsageProgress(userId);

      // Assert
      expect(progress).toMatchObject({
        expenses: {
          current: 1,
          limit: 50,
          percentage: 2,
          unlimited: false,
        },
        categories: {
          current: 1,
          limit: 5,
          percentage: 20,
          unlimited: false,
        },
        exports: {
          current: 1,
          limit: 3,
          percentage: 33,
          unlimited: false,
        },
        plan_type: 'free',
      });
    });

    it('should show unlimited features for premium users', async () => {
      // Arrange
      const premiumUser = await createTestUser({
        subscription_plan: 'premium',
      });
      const userId = premiumUser.id.toString();

      // Act
      const progress = await usageService.getUsageProgress(userId);

      // Assert
      expect(progress.expenses.unlimited).toBe(true);
      expect(progress.categories.unlimited).toBe(true);
      expect(progress.exports.unlimited).toBe(true);
    });
  });

  describe('resetUsageForPeriod', () => {
    it('should reset usage logs for specified period', async () => {
      // Arrange
      const userId = testUser.id.toString();
      
      // Create usage logs
      await usageService.incrementUsage(userId, 'create_expense');
      await usageService.incrementUsage(userId, 'export_data');

      // Verify logs exist
      const logsBefore = await prisma.usageLog.count({
        where: { user_id: testUser.id },
      });
      expect(logsBefore).toBe(2);

      // Act
      await usageService.resetUsageForPeriod(userId, 'month');

      // Assert
      const logsAfter = await prisma.usageLog.count({
        where: { user_id: testUser.id },
      });
      expect(logsAfter).toBe(0);
    });
  });

  describe('getGlobalUsageStats', () => {
    it('should return global statistics', async () => {
      // Arrange
      const user2 = await createTestUser({
        email: '<EMAIL>',
        last_login: new Date(),
      });

      // Act
      const stats = await usageService.getGlobalUsageStats();

      // Assert
      expect(stats).toMatchObject({
        total_users: 2,
        active_users: 1, // only user2 has recent login
        total_expenses: 0,
        total_categories: 1, // testCategory
        total_exports: 0,
      });
      expect(stats.period).toBeDefined();
    });
  });
});
