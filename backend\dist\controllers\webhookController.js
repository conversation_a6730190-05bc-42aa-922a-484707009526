"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.webhookController = void 0;
const prisma_1 = require("../config/prisma");
const stripeService_1 = require("../services/stripeService");
const subscriptionService_1 = require("../services/subscriptionService");
const usageService_1 = require("../services/usageService");
const logger = {
    info: (message, data) => console.log('INFO:', message, data),
    error: (message, error) => console.error('ERROR:', message, error)
};
class WebhookController {
    async handleStripeWebhook(req, res) {
        const sig = req.headers['stripe-signature'];
        const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;
        let event;
        try {
            event = stripeService_1.stripeService.constructWebhookEvent(req.body, sig, endpointSecret);
        }
        catch (err) {
            logger.error('Webhook signature verification failed:', err.message);
            return res.status(400).send(`Webhook Error: ${err.message}`);
        }
        try {
            await this.logWebhookEvent(event);
            await this.processWebhookEvent(event);
            res.json({ received: true });
        }
        catch (error) {
            logger.error('Error processing webhook:', error);
            res.status(500).json({ error: 'Webhook processing failed' });
        }
    }
    async logWebhookEvent(event) {
        try {
            await prisma_1.prisma.webhookEvent.create({
                data: {
                    stripe_id: event.id,
                    type: event.type,
                    data: event.data,
                    processed: false,
                    created_at: new Date(event.created * 1000),
                },
            });
            logger.info(`Webhook event logged: ${event.type} - ${event.id}`);
        }
        catch (error) {
            if (error.code === 'P2002') {
                logger.info(`Webhook event already exists: ${event.id}`);
                return;
            }
            throw error;
        }
    }
    async processWebhookEvent(event) {
        logger.info(`Processing webhook: ${event.type}`);
        switch (event.type) {
            case 'customer.subscription.created':
                await this.handleSubscriptionCreated(event.data.object);
                break;
            case 'customer.subscription.updated':
                await this.handleSubscriptionUpdated(event.data.object);
                break;
            case 'customer.subscription.deleted':
                await this.handleSubscriptionDeleted(event.data.object);
                break;
            case 'customer.subscription.trial_will_end':
                await this.handleTrialWillEnd(event.data.object);
                break;
            case 'invoice.payment_succeeded':
                await this.handlePaymentSucceeded(event.data.object);
                break;
            case 'invoice.payment_failed':
                await this.handlePaymentFailed(event.data.object);
                break;
            case 'customer.created':
                await this.handleCustomerCreated(event.data.object);
                break;
            case 'customer.updated':
                await this.handleCustomerUpdated(event.data.object);
                break;
            default:
                logger.info(`Unhandled webhook type: ${event.type}`);
        }
        await this.markEventAsProcessed(event.id);
    }
    async handleSubscriptionCreated(subscription) {
        try {
            logger.info(`Processing subscription created: ${subscription.id}`);
            const user = await prisma_1.prisma.user.findFirst({
                where: { stripe_customer_id: subscription.customer },
            });
            if (!user) {
                logger.error(`User not found for customer: ${subscription.customer}`);
                return;
            }
            const plan = await prisma_1.prisma.plan.findFirst({
                where: { stripe_id: subscription.items.data[0].price.id },
            });
            if (!plan) {
                logger.error(`Plan not found for price: ${subscription.items.data[0].price.id}`);
                return;
            }
            await subscriptionService_1.subscriptionService.createSubscription({
                stripeSubscriptionId: subscription.id,
                userId: user.id,
                planId: plan.id,
                status: subscription.status,
                currentPeriodStart: new Date(subscription.current_period_start * 1000),
                currentPeriodEnd: new Date(subscription.current_period_end * 1000),
                trialStart: subscription.trial_start ? new Date(subscription.trial_start * 1000) : null,
                trialEnd: subscription.trial_end ? new Date(subscription.trial_end * 1000) : null,
                metadata: subscription.metadata,
            });
            await usageService_1.usageService.logUsage(user.id, 'subscription_created', {
                subscription_id: subscription.id,
                plan_id: plan.id,
            });
            logger.info(`Subscription created successfully for user: ${user.id}`);
        }
        catch (error) {
            logger.error('Error handling subscription created:', error);
            throw error;
        }
    }
    async handleSubscriptionUpdated(subscription) {
        try {
            logger.info(`Processing subscription updated: ${subscription.id}`);
            const updateData = {
                status: subscription.status,
                current_period_start: new Date(subscription.current_period_start * 1000),
                current_period_end: new Date(subscription.current_period_end * 1000),
                trial_start: subscription.trial_start ? new Date(subscription.trial_start * 1000) : null,
                trial_end: subscription.trial_end ? new Date(subscription.trial_end * 1000) : null,
                canceled_at: subscription.canceled_at ? new Date(subscription.canceled_at * 1000) : null,
                metadata: subscription.metadata,
                updated_at: new Date(),
            };
            await subscriptionService_1.subscriptionService.updateSubscription(subscription.id, updateData);
            const dbSubscription = await prisma_1.prisma.subscription.findUnique({
                where: { stripe_id: subscription.id },
                include: { user: true },
            });
            if (dbSubscription) {
                await usageService_1.usageService.logUsage(dbSubscription.user.id, 'subscription_updated', {
                    subscription_id: subscription.id,
                    status: subscription.status,
                });
            }
            logger.info(`Subscription updated successfully: ${subscription.id}`);
        }
        catch (error) {
            logger.error('Error handling subscription updated:', error);
            throw error;
        }
    }
    async handleSubscriptionDeleted(subscription) {
        try {
            logger.info(`Processing subscription deleted: ${subscription.id}`);
            const dbSubscription = await prisma_1.prisma.subscription.findUnique({
                where: { stripe_id: subscription.id },
                include: { user: true },
            });
            if (!dbSubscription) {
                logger.error(`Subscription not found in database: ${subscription.id}`);
                return;
            }
            await subscriptionService_1.subscriptionService.updateSubscription(subscription.id, {
                status: 'canceled',
                canceled_at: new Date(),
                updated_at: new Date(),
            });
            await subscriptionService_1.subscriptionService.downgradeToFreePlan(dbSubscription.user.id);
            await usageService_1.usageService.logUsage(dbSubscription.user.id, 'subscription_deleted', {
                subscription_id: subscription.id,
            });
            logger.info(`Subscription deleted successfully: ${subscription.id}`);
        }
        catch (error) {
            logger.error('Error handling subscription deleted:', error);
            throw error;
        }
    }
    async handleTrialWillEnd(subscription) {
        try {
            logger.info(`Processing trial will end: ${subscription.id}`);
            const dbSubscription = await prisma_1.prisma.subscription.findUnique({
                where: { stripe_id: subscription.id },
                include: { user: true },
            });
            if (!dbSubscription) {
                logger.error(`Subscription not found: ${subscription.id}`);
                return;
            }
            await usageService_1.usageService.logUsage(dbSubscription.user.id, 'trial_will_end', {
                subscription_id: subscription.id,
                trial_end: subscription.trial_end,
            });
            logger.info(`Trial will end notification logged for user: ${dbSubscription.user.id}`);
        }
        catch (error) {
            logger.error('Error handling trial will end:', error);
            throw error;
        }
    }
    async handlePaymentSucceeded(invoice) {
        try {
            logger.info(`Processing payment succeeded: ${invoice.id}`);
            if (invoice.subscription) {
                const dbSubscription = await prisma_1.prisma.subscription.findUnique({
                    where: { stripe_id: invoice.subscription },
                    include: { user: true },
                });
                if (dbSubscription) {
                    await usageService_1.usageService.logUsage(dbSubscription.user.id, 'payment_succeeded', {
                        invoice_id: invoice.id,
                        amount: invoice.amount_paid,
                        subscription_id: invoice.subscription,
                    });
                    await usageService_1.usageService.resetMonthlyUsageIfNeeded(dbSubscription.user.id);
                }
            }
            logger.info(`Payment succeeded processed: ${invoice.id}`);
        }
        catch (error) {
            logger.error('Error handling payment succeeded:', error);
            throw error;
        }
    }
    async handlePaymentFailed(invoice) {
        try {
            logger.info(`Processing payment failed: ${invoice.id}`);
            if (invoice.subscription) {
                const dbSubscription = await prisma_1.prisma.subscription.findUnique({
                    where: { stripe_id: invoice.subscription },
                    include: { user: true },
                });
                if (dbSubscription) {
                    await usageService_1.usageService.logUsage(dbSubscription.user.id, 'payment_failed', {
                        invoice_id: invoice.id,
                        amount: invoice.amount_due,
                        subscription_id: invoice.subscription,
                    });
                }
            }
            logger.info(`Payment failed processed: ${invoice.id}`);
        }
        catch (error) {
            logger.error('Error handling payment failed:', error);
            throw error;
        }
    }
    async handleCustomerCreated(customer) {
        try {
            logger.info(`Processing customer created: ${customer.id}`);
            if (customer.email) {
                await prisma_1.prisma.user.updateMany({
                    where: { email: customer.email },
                    data: { stripe_customer_id: customer.id },
                });
            }
            logger.info(`Customer created processed: ${customer.id}`);
        }
        catch (error) {
            logger.error('Error handling customer created:', error);
            throw error;
        }
    }
    async handleCustomerUpdated(customer) {
        try {
            logger.info(`Processing customer updated: ${customer.id}`);
            const user = await prisma_1.prisma.user.findFirst({
                where: { stripe_customer_id: customer.id },
            });
            if (user) {
                await usageService_1.usageService.logUsage(user.id, 'customer_updated', {
                    customer_id: customer.id,
                });
            }
            logger.info(`Customer updated processed: ${customer.id}`);
        }
        catch (error) {
            logger.error('Error handling customer updated:', error);
            throw error;
        }
    }
    async markEventAsProcessed(eventId) {
        try {
            await prisma_1.prisma.webhookEvent.update({
                where: { stripe_id: eventId },
                data: {
                    processed: true,
                    processed_at: new Date(),
                },
            });
        }
        catch (error) {
            logger.error('Error marking event as processed:', error);
        }
    }
    async getWebhookStats(req, res) {
        try {
            const { startDate, endDate } = req.query;
            const dateFilter = {};
            if (startDate || endDate) {
                dateFilter.created_at = {};
                if (startDate)
                    dateFilter.created_at.gte = new Date(startDate);
                if (endDate)
                    dateFilter.created_at.lte = new Date(endDate);
            }
            const stats = await prisma_1.prisma.webhookEvent.groupBy({
                by: ['type', 'processed'],
                where: dateFilter,
                _count: {
                    id: true,
                },
            });
            const recentEvents = await prisma_1.prisma.webhookEvent.findMany({
                where: dateFilter,
                orderBy: { created_at: 'desc' },
                take: 20,
            });
            res.json({
                success: true,
                data: {
                    stats,
                    recent_events: recentEvents,
                },
            });
        }
        catch (error) {
            logger.error('Error getting webhook stats:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to get webhook statistics',
            });
        }
    }
}
exports.webhookController = new WebhookController();
//# sourceMappingURL=webhookController.js.map