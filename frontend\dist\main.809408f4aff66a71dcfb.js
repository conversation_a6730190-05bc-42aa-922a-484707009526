(()=>{"use strict";var e,t,n,r,a,o={1664:e=>{e.exports="data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 20 20%27%3e%3cpath stroke=%27%236b7280%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%271.5%27 d=%27M6 8l4 4 4-4%27/%3e%3c/svg%3e"},2031:e=>{e.exports="data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3cpath d=%27M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z%27/%3e%3c/svg%3e"},2392:(e,t,n)=>{n.d(t,{Yq:()=>c,cn:()=>i,vv:()=>s,wI:()=>l});var r=n(4164),a=n(856),o=n(6692);function i(...e){return(0,a.QP)((0,r.$)(e))}function s(e,t="RON",n={}){const{minimumFractionDigits:r=2,maximumFractionDigits:a=2,showSymbol:i=!0,locale:s="ro-RO"}=n;if("number"!=typeof e||isNaN(e))return"0,00";const c=new Intl.NumberFormat(s,{minimumFractionDigits:r,maximumFractionDigits:a}).format(Math.abs(e)),l=i?o.uM[t]||t:"",d=e<0?"-":"";return"RON"===t?`${d}${c} ${l}`:`${d}${l}${c}`}function c(e,t={}){const{format:n="short",includeTime:r=!1,locale:a="ro-RO"}=t;if(!e)return"";const o="string"==typeof e?new Date(e):e;if(isNaN(o.getTime()))return"Dată invalidă";const i={short:{day:"2-digit",month:"2-digit",year:"numeric"},medium:{day:"2-digit",month:"short",year:"numeric"},long:{day:"2-digit",month:"long",year:"numeric"},full:{weekday:"long",day:"2-digit",month:"long",year:"numeric"}};let s=i[n]||i.short;return r&&(s={...s,hour:"2-digit",minute:"2-digit"}),new Intl.DateTimeFormat(a,s).format(o)}function l(e,t="ro-RO"){if(!e)return"";const n="string"==typeof e?new Date(e):e;if(isNaN(n.getTime()))return"Dată invalidă";const r=new Date,a=Math.floor((r-n)/1e3),o=Math.floor(a/60),i=Math.floor(o/60),s=Math.floor(i/24);if(a<60)return"acum";if(o<60)return`acum ${o} ${1===o?"minut":"minute"}`;if(i<24)return`acum ${i} ${1===i?"oră":"ore"}`;if(1===s)return"ieri";if(s<7)return`acum ${s} zile`;if(s<30){const e=Math.floor(s/7);return`acum ${e} ${1===e?"săptămână":"săptămâni"}`}if(s<365){const e=Math.floor(s/30);return`acum ${e} ${1===e?"lună":"luni"}`}const c=Math.floor(s/365);return`acum ${c} ${1===c?"an":"ani"}`}},2423:(e,t,n)=>{var r=n(4848),a=n(2691),o=n(7665),i=n(5118),s=n(6540),c=n.n(s),l=n(5338),d=n(888),p=n(4976),m=n(7767),g=n(5009),u=n(9264);const w=({children:e,roles:t=[],requireAdmin:n=!1,redirectTo:a="/login",fallback:o=null})=>{const{isAuthenticated:i,isLoading:s,user:c}=(0,g.nc)(),l=(0,m.zy)();if(s)return o||(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(u.Ay,{size:"lg"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Verificare autentificare..."})]})});if(!i)return(0,r.jsx)(m.C5,{to:a,state:{from:l.pathname},replace:!0});if(n&&c&&"admin"!==(c.role||"user"))return(0,r.jsx)(m.C5,{to:"/app/dashboard",state:{error:"Nu ai permisiunea să accesezi această pagină. Doar administratorii pot accesa această secțiune.",from:l.pathname},replace:!0});if(t.length>0&&c){const e=c.role||"user";if(!t.includes(e)){const t="admin"===e?"/app/admin/dashboard":"/app/dashboard";return(0,r.jsx)(m.C5,{to:t,state:{error:"Nu ai permisiunea să accesezi această pagină.",from:l.pathname},replace:!0})}}return e},h=({children:e,redirectTo:t="/app/dashboard",fallback:n=null,allowAuthenticated:a=!1})=>{const{isAuthenticated:o,isLoading:i,user:s}=(0,g.nc)(),c=(0,m.zy)();if(i)return n||(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(u.Ay,{size:"lg"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Verificare autentificare..."})]})});if(o&&!a){let e=t;"admin"===s?.role&&"/app/dashboard"===t&&(e="/app/admin/dashboard");const n=c.state?.from||e;return(0,r.jsx)(m.C5,{to:n,replace:!0})}return e};var b=n(2392),f=n(5763),y=n(7037),v=n(2254),x=n(4361);const k=({className:e="",minimal:t=!1,showLinks:n=!0,showContact:a=!1})=>{const o=(new Date).getFullYear(),i={product:[{name:"Funcționalități",href:"/features"},{name:"Prețuri",href:"/pricing"},{name:"Actualizări",href:"/updates"},{name:"Roadmap",href:"/roadmap"}],support:[{name:"Documentație",href:"/docs"},{name:"Ghiduri",href:"/guides"},{name:"Suport",href:"/support"},{name:"FAQ",href:"/faq"}],company:[{name:"Despre noi",href:"/about"},{name:"Blog",href:"/blog"},{name:"Cariere",href:"/careers"},{name:"Contact",href:"/contact"}],legal:[{name:"Confidențialitate",href:"/privacy"},{name:"Termeni",href:"/terms"},{name:"Cookies",href:"/cookies"},{name:"Licențe",href:"/licenses"}]},s=[{icon:f.A,label:"Email",value:"<EMAIL>",href:"mailto:<EMAIL>"},{icon:y.A,label:"Telefon",value:"+40 ***********",href:"tel:+40123456789"},{icon:v.A,label:"Adresă",value:"București, România",href:null}],c=[{name:"Facebook",href:"https://facebook.com/expensetracker",icon:e=>(0,r.jsx)("svg",{fill:"currentColor",viewBox:"0 0 24 24",...e,children:(0,r.jsx)("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})})},{name:"Twitter",href:"https://twitter.com/expensetracker",icon:e=>(0,r.jsx)("svg",{fill:"currentColor",viewBox:"0 0 24 24",...e,children:(0,r.jsx)("path",{d:"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"})})},{name:"LinkedIn",href:"https://linkedin.com/company/expensetracker",icon:e=>(0,r.jsx)("svg",{fill:"currentColor",viewBox:"0 0 24 24",...e,children:(0,r.jsx)("path",{d:"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"})})},{name:"GitHub",href:"https://github.com/expensetracker",icon:e=>(0,r.jsx)("svg",{fill:"currentColor",viewBox:"0 0 24 24",...e,children:(0,r.jsx)("path",{d:"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"})})}];return t?(0,r.jsx)("footer",{className:(0,b.cn)("bg-white border-t border-gray-200 py-4",e),children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-center space-y-2 sm:space-y-0",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[(0,r.jsxs)("span",{children:["© ",o," Expense Tracker."]}),(0,r.jsx)("span",{children:"Toate drepturile rezervate."})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-1 text-sm text-gray-600",children:[(0,r.jsx)("span",{children:"Făcut cu"}),(0,r.jsx)(x.A,{className:"h-4 w-4 text-red-500"}),(0,r.jsx)("span",{children:"în România"})]})]})})}):(0,r.jsx)("footer",{className:(0,b.cn)("bg-gray-50 border-t border-gray-200",e),children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,r.jsxs)("div",{className:"col-span-1 lg:col-span-1",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-sm",children:"ET"})}),(0,r.jsx)("span",{className:"font-bold text-xl text-gray-900",children:"Expense Tracker"})]}),(0,r.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"Aplicația ta de încredere pentru gestionarea cheltuielilor personale. Simplu, eficient și sigur."}),(0,r.jsx)("div",{className:"flex space-x-4",children:c.map(e=>(0,r.jsx)("a",{href:e.href,target:"_blank",rel:"noopener noreferrer",className:"text-gray-400 hover:text-primary-600 transition-colors","aria-label":e.name,children:(0,r.jsx)(e.icon,{className:"h-5 w-5"})},e.name))})]}),n&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900 mb-4",children:"Produs"}),(0,r.jsx)("ul",{className:"space-y-2",children:i.product.map(e=>(0,r.jsx)("li",{children:(0,r.jsx)(p.N_,{to:e.href,className:"text-gray-600 hover:text-primary-600 text-sm transition-colors",children:e.name})},e.name))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900 mb-4",children:"Suport"}),(0,r.jsx)("ul",{className:"space-y-2",children:i.support.map(e=>(0,r.jsx)("li",{children:(0,r.jsx)(p.N_,{to:e.href,className:"text-gray-600 hover:text-primary-600 text-sm transition-colors",children:e.name})},e.name))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900 mb-4",children:"Companie"}),(0,r.jsx)("ul",{className:"space-y-2",children:i.company.map(e=>(0,r.jsx)("li",{children:(0,r.jsx)(p.N_,{to:e.href,className:"text-gray-600 hover:text-primary-600 text-sm transition-colors",children:e.name})},e.name))})]})]}),a&&(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900 mb-4",children:"Contact"}),(0,r.jsx)("ul",{className:"space-y-3",children:s.map(e=>(0,r.jsxs)("li",{className:"flex items-center space-x-3",children:[(0,r.jsx)(e.icon,{className:"h-4 w-4 text-gray-400 flex-shrink-0"}),e.href?(0,r.jsx)("a",{href:e.href,className:"text-gray-600 hover:text-primary-600 text-sm transition-colors",children:e.value}):(0,r.jsx)("span",{className:"text-gray-600 text-sm",children:e.value})]},e.label))})]})]}),(0,r.jsx)("div",{className:"mt-8 pt-8 border-t border-gray-200",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0",children:[(0,r.jsx)("div",{className:"flex items-center space-x-4 text-sm text-gray-600",children:(0,r.jsxs)("span",{children:["© ",o," Expense Tracker. Toate drepturile rezervate."]})}),(0,r.jsx)("div",{className:"flex items-center space-x-6",children:i.legal.map(e=>(0,r.jsx)(p.N_,{to:e.href,className:"text-gray-600 hover:text-primary-600 text-sm transition-colors",children:e.name},e.name))}),(0,r.jsxs)("div",{className:"flex items-center space-x-1 text-sm text-gray-600",children:[(0,r.jsx)("span",{children:"Făcut cu"}),(0,r.jsx)(x.A,{className:"h-4 w-4 text-red-500"}),(0,r.jsx)("span",{children:"în România"})]})]})})]})})};var j=n(4278),z=n(3322),A=n(8054),S=n(5410),N=n(8624),C=n(9818),T=n(7060),P=n(9050),F=n(5649);const E=({onSidebarToggle:e,showSidebarToggle:t=!0,sidebarOpen:n=!1})=>{const{user:a,logout:o,isAuthenticated:i}=(0,g.nc)(),c=(0,m.Zp)(),l=(0,m.zy)(),[u,w]=(0,s.useState)(!1),[h,b]=(0,s.useState)(!1),[f,y]=(0,s.useState)(""),v=(0,s.useRef)(null),x=(0,s.useRef)(null);(0,s.useEffect)(()=>{const e=e=>{v.current&&!v.current.contains(e.target)&&w(!1),x.current&&!x.current.contains(e.target)&&b(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]),(0,s.useEffect)(()=>{w(!1),b(!1)},[l.pathname]);const k=[{id:1,title:"Cheltuială adăugată",message:"Ai adăugat o cheltuială de 150 RON",time:"2 min",read:!1},{id:2,title:"Raport lunar gata",message:"Raportul pentru decembrie este disponibil",time:"1 oră",read:!0}],E=k.filter(e=>!e.read).length;return(0,r.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-30",children:(0,r.jsx)("div",{className:"px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[t&&(0,r.jsx)("button",{onClick:e,className:"lg:hidden p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500","aria-label":"Deschide meniul",children:(0,r.jsx)(j.A,{className:"h-6 w-6"})}),(0,r.jsx)(p.N_,{to:"/",className:"flex items-center text-primary-600 hover:text-primary-700 transition-colors",children:(0,r.jsx)("div",{className:"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-sm",children:"ET"})})}),i&&(0,r.jsx)("div",{className:"hidden md:block",children:(0,r.jsx)("h1",{className:"text-xl font-semibold text-gray-900",children:{"/dashboard":"Dashboard","/expenses":"Cheltuieli","/categories":"Categorii","/reports":"Rapoarte","/profile":"Profil","/settings":"Setări"}[l.pathname]||"Expense Tracker"})})]}),i&&(0,r.jsx)("div",{className:"hidden md:flex flex-1 max-w-md mx-8",children:(0,r.jsx)("form",{onSubmit:e=>{e.preventDefault(),f.trim()&&(c(`/search?q=${encodeURIComponent(f.trim())}`),y(""))},className:"w-full",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(z.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),(0,r.jsx)("input",{type:"text",value:f,onChange:e=>y(e.target.value),placeholder:"Caută cheltuieli, categorii...",className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]})})}),(0,r.jsx)("div",{className:"flex items-center space-x-4",children:i?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"relative",ref:x,children:[(0,r.jsxs)("button",{onClick:()=>b(!h),className:"relative p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500","aria-label":"Notificări",children:[(0,r.jsx)(A.A,{className:"h-6 w-6"}),E>0&&(0,r.jsx)("span",{className:"absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center",children:E})]}),h&&(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50",children:[(0,r.jsx)("div",{className:"px-4 py-2 border-b border-gray-200",children:(0,r.jsx)("h3",{className:"font-semibold text-gray-900",children:"Notificări"})}),(0,r.jsx)("div",{className:"max-h-64 overflow-y-auto",children:k.length>0?k.map(e=>(0,r.jsx)("div",{className:(0,P.cn)("px-4 py-3 hover:bg-gray-50 cursor-pointer border-l-4",e.read?"border-transparent":"border-primary-500 bg-primary-50"),children:(0,r.jsxs)("div",{className:"flex justify-between items-start",children:[(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.message})]}),(0,r.jsx)("span",{className:"text-xs text-gray-500 ml-2",children:e.time})]})},e.id)):(0,r.jsx)("div",{className:"px-4 py-8 text-center text-gray-500",children:"Nu ai notificări noi"})}),(0,r.jsx)("div",{className:"px-4 py-2 border-t border-gray-200",children:(0,r.jsx)("button",{className:"text-sm text-primary-600 hover:text-primary-700",children:"Vezi toate notificările"})})]})]}),(0,r.jsxs)("div",{className:"relative",ref:v,children:[(0,r.jsxs)("button",{onClick:()=>w(!u),className:"flex items-center space-x-2 p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 max-w-xs","aria-label":"Meniul utilizatorului",children:[(0,r.jsx)(F.A,{user:a,size:"md",showBadge:!0}),(0,r.jsx)("span",{className:"hidden md:block font-medium text-gray-900 truncate",title:a?.name||"Utilizator",children:a?.name||"Utilizator"}),(0,r.jsx)(S.A,{className:"h-4 w-4 flex-shrink-0"})]}),u&&(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50",children:[(0,r.jsxs)("div",{className:"px-4 py-2 border-b border-gray-200",children:[(0,r.jsx)("p",{className:"font-medium text-gray-900 truncate",title:a?.name||"",children:a?.name}),(0,r.jsx)("p",{className:"text-sm text-gray-600 truncate",title:a?.email||"",children:a?.email})]}),(0,r.jsxs)(p.N_,{to:"/app/profile",className:"flex items-center space-x-2 px-4 py-2 text-gray-700 hover:bg-gray-100",children:[(0,r.jsx)(N.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Profil"})]}),(0,r.jsxs)(p.N_,{to:"/app/settings",className:"flex items-center space-x-2 px-4 py-2 text-gray-700 hover:bg-gray-100",children:[(0,r.jsx)(C.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Setări"})]}),(0,r.jsx)("hr",{className:"my-2"}),(0,r.jsxs)("button",{onClick:async()=>{try{await o(),c("/login")}catch(e){d.oR.error("Eroare la deconectare")}},className:"flex items-center space-x-2 px-4 py-2 text-red-700 hover:bg-red-50 w-full text-left",children:[(0,r.jsx)(T.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Deconectare"})]})]})]})]}):(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(p.N_,{to:"/login",className:"text-gray-600 hover:text-gray-900 font-medium",children:"Conectare"}),(0,r.jsx)(p.N_,{to:"/register",className:"bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 font-medium transition-colors",children:"Înregistrare"})]})})]})})})};var D=n(4180),R=n(2792),I=n(4266),L=n(2957),M=n(1599),_=n(5785),q=n(4576),O=n(6304),U=n(5575),B=n(3603),Y=n(395),G=n(4871),X=n(9941),H=n(6944),W=n(67);const V=({isOpen:e=!1,isCollapsed:t=!1,onClose:n,onCollapse:a})=>{const{user:o}=(0,g.nc)(),i=(0,m.zy)(),s="admin"===o?.role?[{name:"Dashboard Admin",href:"/app/admin/dashboard",icon:D.A,iconSolid:B.A,description:"Panou administrare"},{name:"Utilizatori",href:"/app/admin/users",icon:N.A,iconSolid:Y.A,description:"Gestionează utilizatorii"},{name:"Abonamente",href:"/app/admin/subscriptions",icon:R.A,iconSolid:G.A,description:"Gestionează abonamentele"},{name:"Venituri",href:"/app/admin/revenue",icon:I.A,iconSolid:I.A,description:"Analize financiare"},{name:"Statistici",href:"/app/admin/stats",icon:L.A,iconSolid:X.A,description:"Statistici detaliate"},{name:"Activitate",href:"/app/admin/activity",icon:M.A,iconSolid:M.A,description:"Monitorizare activitate"}]:[{name:"Dashboard",href:"/app/dashboard",icon:D.A,iconSolid:B.A,description:"Vedere generală"},{name:"Cheltuieli",href:"/app/expenses",icon:R.A,iconSolid:G.A,description:"Gestionează cheltuielile",badge:"Nou"},{name:"Categorii",href:"/app/categories",icon:_.A,iconSolid:H.A,description:"Organizează categoriile"},{name:"Rapoarte",href:"/app/reports",icon:L.A,iconSolid:X.A,description:"Analize și statistici"}],c="admin"===o?.role?[{name:"Test Admin",href:"/app/admin/test",icon:C.A,iconSolid:W.A,description:"Pagină de test"}]:[],l=e=>i.pathname===e||"/dashboard"!==e&&i.pathname.startsWith(e),d=({item:e,isActive:a})=>{const o=a?e.iconSolid:e.icon;return(0,r.jsxs)(p.N_,{to:e.href,onClick:()=>n&&n(),className:(0,b.cn)("group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200",a?"bg-primary-100 text-primary-700 border-r-2 border-primary-600":"text-gray-700 hover:bg-gray-100 hover:text-gray-900",t?"justify-center":"justify-start"),title:t?e.name:"",children:[(0,r.jsx)(o,{className:(0,b.cn)("flex-shrink-0 h-5 w-5 transition-colors",a?"text-primary-600":"text-gray-500 group-hover:text-gray-700",t?"":"mr-3")}),!t&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{className:"flex-1 truncate",children:e.name}),e.badge&&(0,r.jsx)("span",{className:"ml-2 px-2 py-0.5 text-xs bg-primary-100 text-primary-700 rounded-full",children:e.badge})]}),t&&(0,r.jsxs)("div",{className:"absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-50 whitespace-nowrap",children:[e.name,e.description&&(0,r.jsx)("div",{className:"text-gray-300 text-xs mt-1",children:e.description})]})]})},u=({title:e})=>t?(0,r.jsx)("div",{className:"my-4 border-t border-gray-200"}):(0,r.jsx)("div",{className:"my-4",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"flex-1 border-t border-gray-200"}),(0,r.jsx)("span",{className:"px-3 text-xs font-medium text-gray-500 uppercase tracking-wider",children:e}),(0,r.jsx)("div",{className:"flex-1 border-t border-gray-200"})]})});return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("div",{className:(0,b.cn)("fixed left-0 z-40 flex flex-col bg-white border-r border-gray-200 transition-all duration-300 ease-in-out","top-16 bottom-0 lg:translate-x-0",e?"translate-x-0":"-translate-x-full",t?"lg:w-16":"lg:w-64","w-64"),children:[(0,r.jsxs)("div",{className:(0,b.cn)("flex items-center justify-between h-16 px-4 border-b border-gray-200 lg:hidden"),children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-bold text-sm",children:"ET"})}),(0,r.jsx)("span",{className:"font-semibold text-gray-900",children:"Menu"})]}),(0,r.jsx)("button",{onClick:n,className:"p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100","aria-label":"Închide meniul",children:(0,r.jsx)(q.A,{className:"h-5 w-5"})})]}),(0,r.jsx)("div",{className:"hidden lg:flex justify-end p-2",children:(0,r.jsx)("button",{onClick:a,className:"p-1.5 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100","aria-label":t?"Extinde meniul":"Restrânge meniul",children:t?(0,r.jsx)(O.A,{className:"h-4 w-4"}):(0,r.jsx)(U.A,{className:"h-4 w-4"})})}),!t&&(0,r.jsx)("div",{className:"p-4 border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-primary-600 rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-medium",children:o?.name?.charAt(0)?.toUpperCase()||"U"})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"font-medium text-gray-900 truncate",children:o?.name||"Utilizator"}),(0,r.jsx)("p",{className:"text-sm text-gray-600 truncate",children:o?.email||"<EMAIL>"})]})]})}),t&&(0,r.jsx)("div",{className:"hidden lg:flex justify-center p-2 border-b border-gray-200",children:(0,r.jsx)("div",{className:"w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-white font-medium text-sm",children:o?.name?.charAt(0)?.toUpperCase()||"U"})})}),(0,r.jsxs)("nav",{className:"flex-1 px-4 py-4 space-y-1 overflow-y-auto",children:[(0,r.jsx)("div",{className:"space-y-1",children:s.map(e=>(0,r.jsx)(d,{item:e,isActive:l(e.href)},e.name))}),c.length>0&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(u,{title:"Admin"}),(0,r.jsx)("div",{className:"space-y-1",children:c.map(e=>(0,r.jsx)(d,{item:e,isActive:l(e.href)},e.name))})]})]}),!t&&(0,r.jsx)("div",{className:"p-4 border-t border-gray-200",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Expense Tracker v1.0"}),(0,r.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"© 2025 Toate drepturile rezervate"})]})})]})})},$=({children:e,showSidebar:t=null,showFooter:n=!0,className:a=""})=>{const{isAuthenticated:o,user:i}=(0,g.nc)(),c=(0,m.zy)(),[l,d]=(0,s.useState)(!1),[p,u]=(0,s.useState)(!1),w=null!==t?t:o;(0,s.useEffect)(()=>{d(!1)},[c.pathname]),(0,s.useEffect)(()=>{const e=()=>{window.innerWidth>=1024&&d(!1)};return window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),(0,s.useEffect)(()=>{const e=localStorage.getItem("sidebar-collapsed");null!==e&&u(JSON.parse(e))},[]);const h=()=>{d(!1)};return(0,r.jsxs)("div",{className:(0,b.cn)("min-h-screen bg-gray-50 flex flex-col",a),children:[(0,r.jsx)(E,{onSidebarToggle:()=>{d(!l)},showSidebarToggle:w,sidebarOpen:l}),(0,r.jsxs)("div",{className:"flex flex-1 relative",children:[w&&(0,r.jsxs)(r.Fragment,{children:[l&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden",onClick:h,"aria-hidden":"true"}),(0,r.jsx)(V,{isOpen:l,isCollapsed:p,onClose:h,onCollapse:()=>{const e=!p;u(e),localStorage.setItem("sidebar-collapsed",JSON.stringify(e))}})]}),(0,r.jsxs)("main",{className:(0,b.cn)("flex-1 flex flex-col min-h-0 transition-all duration-300 ease-in-out",w&&{"lg:ml-64":!p,"lg:ml-16":p}),children:[(0,r.jsx)("div",{className:"flex-1 p-4 lg:p-6 xl:p-8",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto",children:e||(0,r.jsx)(m.sv,{})})}),n&&(0,r.jsx)(k,{className:(0,b.cn)(w&&{"lg:ml-64":!p,"lg:ml-16":p}),minimal:!0,showLinks:!1,showContact:!1})]})]}),(0,r.jsx)("div",{className:"sr-only",children:(0,r.jsx)("a",{href:"#main-content",className:"skip-link",children:"Sari la conținutul principal"})})]})};var Q=n(4985);const J=()=>{const e=(()=>{const{user:e}=(0,g.nc)();return"admin"===e?.role?"/app/admin/dashboard":"/app/dashboard"})();return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-9xl font-bold text-primary-600 mb-4",children:"404"}),(0,r.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Pagina nu a fost găsită"}),(0,r.jsx)("p",{className:"text-lg text-gray-600 mb-8",children:"Ne pare rău, dar pagina pe care o căutați nu există sau a fost mutată."}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,r.jsxs)(p.N_,{to:e,className:"inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors",children:[(0,r.jsx)(D.A,{className:"w-5 h-5 mr-2"}),"Înapoi la Dashboard"]}),(0,r.jsxs)("button",{onClick:()=>window.history.back(),className:"inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors",children:[(0,r.jsx)(Q.A,{className:"w-5 h-5 mr-2"}),"Înapoi"]})]})]}),(0,r.jsxs)("div",{className:"mt-12 text-center",children:[(0,r.jsx)("div",{className:"inline-flex items-center justify-center w-32 h-32 bg-primary-100 rounded-full mb-4",children:(0,r.jsx)("svg",{className:"w-16 h-16 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.291-1.007-5.691-2.709M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"})})}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Dacă problema persistă, vă rugăm să contactați echipa de suport."})]})]})})},K=(0,s.lazy)(()=>Promise.all([n.e(96),n.e(405)]).then(n.bind(n,5080))),Z=(0,s.lazy)(()=>Promise.all([n.e(96),n.e(842)]).then(n.bind(n,5842))),ee=(0,s.lazy)(()=>Promise.all([n.e(96),n.e(470)]).then(n.bind(n,4470))),te=(0,s.lazy)(()=>Promise.all([n.e(96),n.e(333)]).then(n.bind(n,4095))),ne=(0,s.lazy)(()=>Promise.all([n.e(96),n.e(291)]).then(n.bind(n,1291))),re=(0,s.lazy)(()=>Promise.all([n.e(96),n.e(16)]).then(n.bind(n,8016))),ae=(0,s.lazy)(()=>Promise.all([n.e(96),n.e(187),n.e(633)]).then(n.bind(n,6633))),oe=(0,s.lazy)(()=>Promise.all([n.e(96),n.e(732),n.e(890)]).then(n.bind(n,3890))),ie=(0,s.lazy)(()=>Promise.all([n.e(96),n.e(187),n.e(605)]).then(n.bind(n,5605))),se=(0,s.lazy)(()=>Promise.all([n.e(96),n.e(667)]).then(n.bind(n,9667))),ce=(0,s.lazy)(()=>Promise.all([n.e(96),n.e(732),n.e(901)]).then(n.bind(n,9901))),le=(0,s.lazy)(()=>Promise.all([n.e(96),n.e(409),n.e(314),n.e(396),n.e(955)]).then(n.bind(n,6955))),de=(0,s.lazy)(()=>Promise.all([n.e(96),n.e(409),n.e(296)]).then(n.bind(n,296))),pe=(0,s.lazy)(()=>Promise.all([n.e(96),n.e(409),n.e(731)]).then(n.bind(n,2731))),me=(0,s.lazy)(()=>Promise.all([n.e(96),n.e(409),n.e(314),n.e(476)]).then(n.bind(n,476))),ge=(0,s.lazy)(()=>Promise.all([n.e(96),n.e(409),n.e(314),n.e(396)]).then(n.bind(n,2396))),ue=(0,s.lazy)(()=>Promise.all([n.e(96),n.e(409),n.e(833)]).then(n.bind(n,6833))),we=(0,s.lazy)(()=>Promise.all([n.e(96),n.e(409),n.e(314),n.e(396),n.e(955),n.e(381)]).then(n.bind(n,9381))),he=(0,s.lazy)(()=>Promise.all([n.e(96),n.e(700),n.e(807)]).then(n.bind(n,8807))),be=(0,s.lazy)(()=>Promise.all([n.e(96),n.e(700),n.e(566)]).then(n.bind(n,9566))),fe=(0,s.lazy)(()=>Promise.all([n.e(96),n.e(700),n.e(219)]).then(n.bind(n,8219))),ye=(0,s.lazy)(()=>Promise.all([n.e(96),n.e(700),n.e(875)]).then(n.bind(n,7875))),ve=(0,s.lazy)(()=>Promise.all([n.e(96),n.e(700),n.e(824)]).then(n.bind(n,6824))),xe=(0,s.lazy)(()=>Promise.all([n.e(96),n.e(700),n.e(974)]).then(n.bind(n,3974))),ke=(0,s.lazy)(()=>Promise.all([n.e(96),n.e(700),n.e(896)]).then(n.bind(n,2896))),je=(0,s.lazy)(()=>Promise.all([n.e(96),n.e(700),n.e(95)]).then(n.bind(n,3095))),ze=()=>(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(u.Ay,{size:"lg"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Se încarcă..."})]})});var Ae=n(5072),Se=n.n(Ae),Ne=n(7825),Ce=n.n(Ne),Te=n(7659),Pe=n.n(Te),Fe=n(5056),Ee=n.n(Fe),De=n(540),Re=n.n(De),Ie=n(1113),Le=n.n(Ie),Me=n(7805),_e={};_e.styleTagTransform=Le(),_e.setAttributes=Ee(),_e.insert=Pe().bind(null,"head"),_e.domAPI=Ce(),_e.insertStyleElement=Re(),Se()(Me.A,_e),Me.A&&Me.A.locals&&Me.A.locals,n(8114);const qe=new a.E({defaultOptions:{queries:{staleTime:3e5,gcTime:6e5,retry:(e,t)=>401!==t?.response?.status&&403!==t?.response?.status&&e<3,refetchOnWindowFocus:!1,refetchOnMount:!0,refetchOnReconnect:!0},mutations:{retry:!1}}}),Oe={duration:4e3,position:"top-right",style:{background:"#363636",color:"#fff",fontSize:"14px",borderRadius:"8px",padding:"12px 16px",maxWidth:"400px",marginTop:"60px"},success:{iconTheme:{primary:"#22c55e",secondary:"#fff"}},error:{iconTheme:{primary:"#ef4444",secondary:"#fff"},duration:6e3},loading:{iconTheme:{primary:"#3b82f6",secondary:"#fff"}}};class Ue extends s.Component{constructor(e){super(e),this.state={hasError:!1,error:null,errorInfo:null}}static getDerivedStateFromError(e){return{hasError:!0}}componentDidCatch(e,t){this.setState({error:e,errorInfo:t}),console.error("React Error Boundary:",e,t)}render(){return this.state.hasError?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,r.jsxs)("div",{className:"max-w-md w-full bg-white shadow-lg rounded-lg p-6",children:[(0,r.jsx)("div",{className:"flex items-center justify-center w-12 h-12 mx-auto bg-red-100 rounded-full mb-4",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,r.jsx)("h2",{className:"text-lg font-semibold text-gray-900 text-center mb-2",children:"Oops! Ceva nu a mers bine"}),(0,r.jsx)("p",{className:"text-gray-600 text-center mb-4",children:"A apărut o eroare neașteptată. Te rugăm să reîmprospătezi pagina."}),(0,r.jsxs)("div",{className:"flex space-x-3",children:[(0,r.jsx)("button",{onClick:()=>window.location.reload(),className:"flex-1 bg-primary-600 text-white py-2 px-4 rounded-md hover:bg-primary-700 transition-colors",children:"Reîmprospătează"}),(0,r.jsx)("button",{onClick:()=>this.setState({hasError:!1,error:null,errorInfo:null}),className:"flex-1 bg-gray-200 text-gray-800 py-2 px-4 rounded-md hover:bg-gray-300 transition-colors",children:"Încearcă din nou"})]}),this.state.error&&(0,r.jsxs)("details",{className:"mt-4 p-3 bg-gray-100 rounded text-sm",children:[(0,r.jsx)("summary",{className:"cursor-pointer font-medium text-gray-700 mb-2",children:"Detalii eroare (development)"}),(0,r.jsxs)("pre",{className:"whitespace-pre-wrap text-xs text-gray-600 overflow-auto max-h-32",children:[this.state.error.toString(),this.state.errorInfo?.componentStack]})]})]})}):this.props.children}}const Be=document.getElementById("root");if(!Be)throw new Error("Root element not found");l.createRoot(Be).render((0,r.jsx)(c().StrictMode,{children:(0,r.jsx)(({children:e})=>(0,r.jsx)(Ue,{children:(0,r.jsx)(o.Ht,{client:qe,children:(0,r.jsxs)(p.Kd,{future:{v7_startTransition:!0,v7_relativeSplatPath:!0},children:[e,(0,r.jsx)(d.l$,{toastOptions:Oe}),(0,r.jsx)(i.E,{initialIsOpen:!1})]})})}),{children:(0,r.jsx)(()=>{const{isAuthenticated:e,isLoading:t,initializeAuth:n}=(0,g.nc)(e=>({isAuthenticated:e.isAuthenticated,isLoading:e.isLoading,initializeAuth:e.initializeAuth}));return(0,s.useEffect)(()=>{n()},[n]),t?(0,r.jsx)(ze,{}):(0,r.jsx)("div",{className:"App",children:(0,r.jsx)(s.Suspense,{fallback:(0,r.jsx)(ze,{}),children:(0,r.jsxs)(m.BV,{children:[(0,r.jsx)(m.qh,{path:"/",element:(0,r.jsx)(h,{allowAuthenticated:!0,children:(0,r.jsx)(K,{})})}),(0,r.jsx)(m.qh,{path:"/login",element:(0,r.jsx)(h,{children:(0,r.jsx)(Z,{})})}),(0,r.jsx)(m.qh,{path:"/register",element:(0,r.jsx)(h,{children:(0,r.jsx)(ee,{})})}),(0,r.jsx)(m.qh,{path:"/forgot-password",element:(0,r.jsx)(h,{children:(0,r.jsx)(te,{})})}),(0,r.jsx)(m.qh,{path:"/reset-password",element:(0,r.jsx)(h,{children:(0,r.jsx)(ne,{})})}),(0,r.jsx)(m.qh,{path:"/terms",element:(0,r.jsx)(h,{allowAuthenticated:!0,children:(0,r.jsx)(he,{})})}),(0,r.jsx)(m.qh,{path:"/privacy",element:(0,r.jsx)(h,{allowAuthenticated:!0,children:(0,r.jsx)(be,{})})}),(0,r.jsx)(m.qh,{path:"/cookies",element:(0,r.jsx)(h,{allowAuthenticated:!0,children:(0,r.jsx)(fe,{})})}),(0,r.jsx)(m.qh,{path:"/features",element:(0,r.jsx)(h,{allowAuthenticated:!0,children:(0,r.jsx)(ye,{})})}),(0,r.jsx)(m.qh,{path:"/pricing",element:(0,r.jsx)(h,{allowAuthenticated:!0,children:(0,r.jsx)(ve,{})})}),(0,r.jsx)(m.qh,{path:"/documentation",element:(0,r.jsx)(h,{allowAuthenticated:!0,children:(0,r.jsx)(xe,{})})}),(0,r.jsx)(m.qh,{path:"/contact",element:(0,r.jsx)(h,{allowAuthenticated:!0,children:(0,r.jsx)(ke,{})})}),(0,r.jsx)(m.qh,{path:"/help",element:(0,r.jsx)(h,{allowAuthenticated:!0,children:(0,r.jsx)(je,{})})}),(0,r.jsxs)(m.qh,{path:"/app",element:(0,r.jsx)(w,{children:(0,r.jsx)($,{})}),children:[(0,r.jsx)(m.qh,{index:!0,element:(0,r.jsx)(m.C5,{to:e&&"admin"===g.nc.getState().user?.role?"/app/admin/dashboard":"/app/dashboard",replace:!0})}),(0,r.jsx)(m.qh,{path:"dashboard",element:(0,r.jsx)(re,{})}),(0,r.jsx)(m.qh,{path:"expenses",element:(0,r.jsx)(ae,{})}),(0,r.jsx)(m.qh,{path:"categories",element:(0,r.jsx)(oe,{})}),(0,r.jsx)(m.qh,{path:"reports",element:(0,r.jsx)(ie,{})}),(0,r.jsx)(m.qh,{path:"profile",element:(0,r.jsx)(se,{})}),(0,r.jsx)(m.qh,{path:"settings",element:(0,r.jsx)(ce,{})}),(0,r.jsx)(m.qh,{path:"admin/*",element:(0,r.jsx)(w,{roles:["admin"],children:(0,r.jsxs)(m.BV,{children:[(0,r.jsx)(m.qh,{index:!0,element:(0,r.jsx)(m.C5,{to:"/app/admin/dashboard",replace:!0})}),(0,r.jsx)(m.qh,{path:"dashboard",element:(0,r.jsx)(le,{})}),(0,r.jsx)(m.qh,{path:"stats",element:(0,r.jsx)(de,{dashboardStats:{}})}),(0,r.jsx)(m.qh,{path:"revenue",element:(0,r.jsx)(pe,{dashboardStats:{}})}),(0,r.jsx)(m.qh,{path:"users",element:(0,r.jsx)(me,{})}),(0,r.jsx)(m.qh,{path:"subscriptions",element:(0,r.jsx)(ge,{})}),(0,r.jsx)(m.qh,{path:"activity",element:(0,r.jsx)(ue,{})}),(0,r.jsx)(m.qh,{path:"test",element:(0,r.jsx)(we,{})})]})})})]}),(0,r.jsx)(m.qh,{path:"*",element:(0,r.jsx)(J,{})})]})})})},{})})}))},3569:e=>{e.exports="data:image/svg+xml,%3csvg xmlns=%27http://www.w3.org/2000/svg%27 fill=%27none%27 viewBox=%270 0 16 16%27%3e%3cpath stroke=%27white%27 stroke-linecap=%27round%27 stroke-linejoin=%27round%27 stroke-width=%272%27 d=%27M4 8h8%27/%3e%3c/svg%3e"},5009:(e,t,n)=>{n.d(t,{nc:()=>u});var r=n(888),a=n(1621),o=n(7134);const i=18e5,s={NO_REFRESH_TOKEN:"Nu există refresh token",TOKEN_REFRESH_FAILED:"Eroare la reîmprospătarea token-ului",INVALID_CREDENTIALS:"Credențiale invalide",NETWORK_ERROR:"Eroare de rețea",SESSION_EXPIRED:"Sesiunea a expirat",UNAUTHORIZED:"Acces neautorizat"};var c=n(1083),l=n(6692);const d=c.A.create({baseURL:l.JR,timeout:1e4,headers:{"Content-Type":"application/json"}});let p=null;d.interceptors.request.use(e=>(p&&(e.headers.Authorization=`Bearer ${p}`),e),e=>Promise.reject(e)),d.interceptors.response.use(e=>e,async e=>{const t=e.config;if(401===e.response?.status&&!t._retry){t._retry=!0,console.log("🔄 Attempting token refresh due to 401 error");try{const e=localStorage.getItem("refreshToken");if(e){console.log("🔑 Found refresh token, attempting refresh...");const n=await d.post(l.Sn.AUTH.REFRESH,{refreshToken:e});if(n.data.success){const{tokens:e}=n.data.data,{accessToken:r,refreshToken:a}=e;return console.log("✅ Token refresh successful"),p=r,d.defaults.headers.Authorization=`Bearer ${r}`,localStorage.setItem("accessToken",r),a&&localStorage.setItem("refreshToken",a),t.headers.Authorization=`Bearer ${r}`,d(t)}console.log("❌ Token refresh failed - invalid response")}else console.log("❌ No refresh token found")}catch(e){return console.log("❌ Token refresh error:",e.response?.data?.message||e.message),p=null,delete d.defaults.headers.Authorization,localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),window.location.pathname.includes("/login")||(window.location.href="/login"),Promise.reject(e)}console.log("❌ Token refresh failed - clearing auth state"),p=null,delete d.defaults.headers.Authorization,localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),window.location.pathname.includes("/login")||(window.location.href="/login")}return Promise.reject(e)});const m={setAuthToken:e=>{e?(p=e,d.defaults.headers.Authorization=`Bearer ${e}`,localStorage.setItem("accessToken",e)):(p=null,delete d.defaults.headers.Authorization,localStorage.removeItem("accessToken"))},clearAuthToken:()=>{p=null,delete d.defaults.headers.Authorization,localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken")},register:async e=>{try{const t=await d.post(l.Sn.AUTH.REGISTER,{name:e.name,email:e.email,password:e.password,confirmPassword:e.confirmPassword});return{success:!0,data:t.data.data,message:t.data.message}}catch(e){return{success:!1,message:e.response?.data?.message||"Eroare la înregistrare",errors:e.response?.data?.errors||null}}},login:async e=>{try{const t=await d.post(l.Sn.AUTH.LOGIN,{email:e.email,password:e.password}),{user:n,tokens:r}=t.data.data,{accessToken:a,refreshToken:o}=r;return localStorage.setItem("accessToken",a),localStorage.setItem("refreshToken",o),m.setAuthToken(a),{success:!0,data:{user:n,accessToken:a,refreshToken:o},message:t.data.message}}catch(e){return{success:!1,message:e.response?.data?.message||"Eroare la autentificare",errors:e.response?.data?.errors||null}}},logout:async e=>{try{return await d.post(l.Sn.AUTH.LOGOUT,{refreshToken:e}),m.clearAuthToken(),{success:!0,message:"Deconectare reușită"}}catch(e){return m.clearAuthToken(),{success:!1,message:e.response?.data?.message||"Eroare la deconectare"}}},refreshToken:async e=>{try{const t=await d.post(l.Sn.AUTH.REFRESH,{refreshToken:e}),{tokens:n}=t.data.data,{accessToken:r,refreshToken:a}=n;return m.setAuthToken(r),a&&localStorage.setItem("refreshToken",a),{success:!0,data:{accessToken:r,refreshToken:a||e},message:t.data.message}}catch(e){return m.clearAuthToken(),{success:!1,message:e.response?.data?.message||"Eroare la reîmprospătarea token-ului"}}},getProfile:async()=>{try{const e=await d.get(l.Sn.AUTH.PROFILE);return{success:!0,data:e.data.data,message:e.data.message}}catch(e){return{success:!1,message:e.response?.data?.message||"Eroare la obținerea profilului"}}},updateProfile:async e=>{try{const t=await d.put(l.Sn.AUTH.PROFILE,{name:e.name,email:e.email,phone:e.phone,dateOfBirth:e.dateOfBirth,currency:e.currency,language:e.language,timezone:e.timezone});return{success:!0,data:t.data.data,message:t.data.message}}catch(e){return{success:!1,message:e.response?.data?.message||"Eroare la actualizarea profilului",errors:e.response?.data?.errors||null}}},changePassword:async e=>{try{return{success:!0,message:(await d.put(l.Sn.AUTH.CHANGE_PASSWORD,{currentPassword:e.currentPassword,newPassword:e.newPassword,confirmPassword:e.confirmPassword})).data.message}}catch(e){return{success:!1,message:e.response?.data?.message||"Eroare la schimbarea parolei",errors:e.response?.data?.errors||null}}},forgotPassword:async e=>{try{return{success:!0,message:(await d.post(l.Sn.AUTH.FORGOT_PASSWORD,{email:e})).data.message}}catch(e){return{success:!1,message:e.response?.data?.message||"Eroare la trimiterea email-ului",errors:e.response?.data?.errors||null}}},resetPassword:async(e,t)=>{try{return{success:!0,message:(await d.post(l.Sn.AUTH.RESET_PASSWORD,{token:e,password:t,confirmPassword:t})).data.message}}catch(e){return{success:!1,message:e.response?.data?.message||"Eroare la resetarea parolei",errors:e.response?.data?.errors||null}}},verifyEmail:async e=>{try{return{success:!0,message:(await d.post(l.Sn.AUTH.VERIFY_EMAIL,{token:e})).data.message}}catch(e){return{success:!1,message:e.response?.data?.message||"Eroare la verificarea email-ului"}}},resendVerificationEmail:async()=>{try{return{success:!0,message:(await d.post(l.Sn.AUTH.RESEND_VERIFICATION)).data.message}}catch(e){return{success:!1,message:e.response?.data?.message||"Eroare la retrimiterea email-ului"}}},isAuthenticated:()=>!!localStorage.getItem("accessToken"),getAccessToken:()=>localStorage.getItem("accessToken"),getRefreshToken:()=>localStorage.getItem("refreshToken"),isTokenExpired:e=>{if(!e)return!0;try{const t=JSON.parse(atob(e.split(".")[1])),n=Math.floor(Date.now()/1e3);return t.exp<n+30}catch(e){return console.error("Error decoding token:",e),!0}},getUserFromToken:e=>{if(!e)return null;try{const t=JSON.parse(atob(e.split(".")[1]));return{id:t.userId,email:t.email,role:t.role}}catch(e){return null}}},g={user:null,accessToken:null,refreshToken:null,isAuthenticated:!1,isLoading:!1,error:null,lastActivity:null},u=(0,a.vt)()((0,o.Zr)((e,t)=>({...g,setLoading:t=>{e({isLoading:t})},setError:t=>{e({error:t}),t&&r.Ay.error(t)},clearError:()=>{e({error:null})},setUser:t=>{e({user:t,isAuthenticated:!!t,lastActivity:Date.now()})},setTokens:(t,n)=>{e({accessToken:t,refreshToken:n,lastActivity:Date.now()}),t&&m.setAuthToken(t)},clearAuth:()=>{console.log("🧹 Clearing auth state..."),m.clearAuthToken(),e(g),localStorage.removeItem("auth-storage"),console.log("✅ Auth state cleared successfully")},login:async t=>{try{e({isLoading:!0,error:null});const n=await m.login(t);if(n.success){const{user:t,accessToken:a,refreshToken:o}=n.data;return e({user:t,accessToken:a,refreshToken:o,isAuthenticated:!0,isLoading:!1,lastActivity:Date.now()}),m.setAuthToken(a),r.Ay.success("Autentificare reușită!"),{success:!0}}return e({error:n.message,isLoading:!1}),{success:!1,message:n.message}}catch(t){const n=t.response?.data?.message||"Eroare la autentificare";return e({error:n,isLoading:!1}),{success:!1,message:n}}},register:async t=>{try{e({isLoading:!0,error:null});const n=await m.register(t);return n.success?(e({isLoading:!1}),r.Ay.success("Cont creat cu succes! Te poți autentifica acum."),{success:!0}):(e({error:n.message,isLoading:!1}),{success:!1,message:n.message})}catch(t){const n=t.response?.data?.message||"Eroare la înregistrare";return e({error:n,isLoading:!1}),{success:!1,message:n}}},logout:async()=>{try{const{refreshToken:e}=t();e&&await m.logout(e),t().clearAuth(),r.Ay.success("Deconectare reușită!")}catch(e){t().clearAuth(),console.error("Eroare la logout:",e)}},refreshAccessToken:async()=>{try{const{refreshToken:n}=t();if(console.log("🔄 Attempting to refresh access token...",{hasRefreshToken:!!n}),!n)return console.log("❌ No refresh token available"),t().clearAuth(),{success:!1,message:"Nu există refresh token"};const r=await m.refreshToken(n);if(r.success){const{accessToken:t,refreshToken:n}=r.data;return console.log("✅ Token refresh successful"),e({accessToken:t,refreshToken:n,lastActivity:Date.now()}),m.setAuthToken(t),{success:!0}}return console.log("❌ Token refresh failed:",r.message),t().clearAuth(),{success:!1,message:r.message}}catch(e){return console.error("❌ Error during token refresh:",e),t().clearAuth(),{success:!1,message:"Eroare la reîmprospătarea token-ului"}}},updateProfile:async n=>{try{e({isLoading:!0,error:null});const a=await m.updateProfile(n);if(a.success){const n=t().user;return n&&e({user:{...n,...a.data},isLoading:!1}),r.Ay.success("Profil actualizat cu succes!"),{success:!0}}return e({error:a.message,isLoading:!1}),{success:!1,message:a.message}}catch(t){const n=t.response?.data?.message||"Eroare la actualizarea profilului";return e({error:n,isLoading:!1}),{success:!1,message:n}}},changePassword:async t=>{try{e({isLoading:!0,error:null});const n=await m.changePassword(t);return n.success?(e({isLoading:!1}),r.Ay.success("Parola a fost schimbată cu succes!"),{success:!0}):(e({error:n.message,isLoading:!1}),{success:!1,message:n.message})}catch(t){const n=t.response?.data?.message||"Eroare la schimbarea parolei";return e({error:n,isLoading:!1}),{success:!1,message:n}}},forgotPassword:async t=>{try{e({isLoading:!0,error:null});const n=await m.forgotPassword(t);return n.success?(e({isLoading:!1}),r.Ay.success("Link-ul de resetare a fost trimis pe email!"),{success:!0}):(e({error:n.message,isLoading:!1}),{success:!1,message:n.message})}catch(t){const n=t.response?.data?.message||"Eroare la trimiterea email-ului";return e({error:n,isLoading:!1}),{success:!1,message:n}}},resetPassword:async(t,n)=>{try{e({isLoading:!0,error:null});const a=await m.resetPassword(t,n);return a.success?(e({isLoading:!1}),r.Ay.success("Parola a fost resetată cu succes!"),{success:!0}):(e({error:a.message,isLoading:!1}),{success:!1,message:a.message})}catch(t){const n=t.response?.data?.message||"Eroare la resetarea parolei";return e({error:n,isLoading:!1}),{success:!1,message:n}}},initializeAuth:async()=>{try{e({isLoading:!0});const{accessToken:n,refreshToken:r,user:a}=t();if(console.log("🚀 Initializing auth...",{hasAccessToken:!!n,hasRefreshToken:!!r,hasUser:!!a}),n&&r&&a)if(m.isTokenExpired(n))console.log("🔄 Access token expired, attempting refresh..."),(await t().refreshAccessToken()).success?(console.log("✅ Token refreshed successfully during init"),e({isAuthenticated:!0,isLoading:!1,lastActivity:Date.now()})):(console.log("❌ Token refresh failed during init"),t().clearAuth(),e({isLoading:!1}));else{m.setAuthToken(n);try{const n=await m.getProfile();n.success?(console.log("✅ Auth initialized successfully with valid token"),e({user:n.data.user,isAuthenticated:!0,isLoading:!1,lastActivity:Date.now()})):(console.log("❌ Profile fetch failed, token might be invalid"),(await t().refreshAccessToken()).success||t().clearAuth(),e({isLoading:!1}))}catch(n){console.log("❌ Error during profile fetch:",n.message),r&&(await t().refreshAccessToken()).success||t().clearAuth(),e({isLoading:!1})}}else console.log("❌ Missing auth data, clearing auth state"),t().clearAuth(),e({isLoading:!1})}catch(n){console.error("❌ Error during auth initialization:",n),t().clearAuth(),e({isLoading:!1})}},checkUserActivity:()=>{const{lastActivity:e,isAuthenticated:n}=t();if(n&&e){const n=Date.now()-e;console.log("⏰ Checking user activity...",{timeSinceLastActivity:`${Math.floor(n/1e3)}s`,inactivityTimeout:`${Math.floor(i/1e3)}s`}),n>i&&(console.log("⏰ Session expired due to inactivity"),t().logout(),r.Ay.error(`${s.SESSION_EXPIRED} din cauza inactivității`))}},updateActivity:()=>{const{isAuthenticated:n}=t();n&&e({lastActivity:Date.now()})}}),{name:"auth-storage",storage:(0,o.KU)(()=>localStorage),partialize:e=>({user:e.user,accessToken:e.accessToken,refreshToken:e.refreshToken,isAuthenticated:e.isAuthenticated,lastActivity:e.lastActivity}),onRehydrateStorage:()=>e=>{e?.accessToken&&m.setAuthToken(e.accessToken)}}))},5270:e=>{e.exports="data:image/svg+xml,%3csvg viewBox=%270 0 16 16%27 fill=%27white%27 xmlns=%27http://www.w3.org/2000/svg%27%3e%3ccircle cx=%278%27 cy=%278%27 r=%273%27/%3e%3c/svg%3e"},5649:(e,t,n)=>{n.d(t,{A:()=>s});var r=n(4848),a=(n(6540),n(8624)),o=n(2496),i=n(9050);const s=({user:e,size:t="md",showBadge:n=!0,className:s=""})=>{const c={sm:"w-6 h-6",md:"w-8 h-8",lg:"w-10 h-10"},l={sm:"h-3 w-3",md:"h-4 w-4",lg:"h-6 w-6"},d={sm:"w-3 h-3",md:"w-4 h-4",lg:"w-5 h-5"},p={sm:"-bottom-0.5 -right-0.5",md:"-bottom-1 -right-1",lg:"-bottom-1 -right-1"};return e?(0,r.jsxs)("div",{className:(0,i.cn)("relative inline-block",s),children:[e?.avatar?(0,r.jsx)("img",{src:e.avatar,alt:e.name||"Avatar utilizator",className:(0,i.cn)("rounded-full object-cover",c[t])}):(0,r.jsx)("div",{className:(0,i.cn)("rounded-full bg-primary-600 flex items-center justify-center",c[t]),children:e?.name?(0,r.jsx)("span",{className:(0,i.cn)("text-white font-medium",{"text-xs":"sm"===t,"text-sm":"md"===t,"text-base":"lg"===t}),children:e?.name?e.name.split(" ").map(e=>e.charAt(0)).join("").toUpperCase().slice(0,2):"U"}):(0,r.jsx)(a.A,{className:(0,i.cn)("text-white",l[t])})}),n&&(()=>{const n=e?.subscription?.plan?.name?.toLowerCase();switch(n){case"premium":return(0,r.jsx)("div",{className:(0,i.cn)("absolute rounded-full bg-gradient-to-r from-yellow-400 to-yellow-600 flex items-center justify-center shadow-sm border-2 border-white",d[t],p[t]),children:(0,r.jsx)("svg",{className:(0,i.cn)("text-white",{"h-2 w-2":"sm"===t,"h-2.5 w-2.5":"md"===t,"h-3 w-3":"lg"===t}),fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M5 16L3 6l5.5 4L12 4l3.5 6L21 6l-2 10H5z"})})});case"basic":return(0,r.jsx)("div",{className:(0,i.cn)("absolute rounded-full bg-gradient-to-r from-blue-400 to-blue-600 flex items-center justify-center shadow-sm border-2 border-white",d[t],p[t]),children:(0,r.jsx)(o.A,{className:(0,i.cn)("text-white",{"h-2 w-2":"sm"===t,"h-2.5 w-2.5":"md"===t,"h-3 w-3":"lg"===t})})});case"free":return(0,r.jsx)("div",{className:(0,i.cn)("absolute rounded-full bg-gradient-to-r from-gray-400 to-gray-600 flex items-center justify-center shadow-sm border-2 border-white",d[t],p[t]),children:(0,r.jsx)("svg",{className:(0,i.cn)("text-white",{"h-2 w-2":"sm"===t,"h-2.5 w-2.5":"md"===t,"h-3 w-3":"lg"===t}),fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"})})});default:return null}})()]}):(0,r.jsx)("div",{className:(0,i.cn)("relative inline-block",s),children:(0,r.jsx)("div",{className:(0,i.cn)("rounded-full bg-gray-400 flex items-center justify-center",c[t]),children:(0,r.jsx)(a.A,{className:(0,i.cn)("text-white",l[t])})})})}},6692:(e,t,n)=>{n.d(t,{JR:()=>r,Sn:()=>a,uM:()=>l});const r="http://localhost:3000/api",a={AUTH:{REGISTER:"/auth/register",LOGIN:"/auth/login",LOGOUT:"/auth/logout",REFRESH:"/auth/refresh",PROFILE:"/auth/profile",CHANGE_PASSWORD:"/auth/change-password",FORGOT_PASSWORD:"/auth/forgot-password",RESET_PASSWORD:"/auth/reset-password",VERIFY_EMAIL:"/auth/verify-email",RESEND_VERIFICATION:"/auth/resend-verification"},CATEGORIES:{BASE:"/categories",BY_ID:e=>`/categories/${e}`,STATS:"/categories/stats",REORDER:"/categories/reorder",SET_DEFAULT:"/categories/set-default"},EXPENSES:{BASE:"/expenses",BY_ID:e=>`/expenses/${e}`,STATS:"/expenses/stats",TRENDS:"/expenses/trends",TAGS:"/expenses/tags",BULK_DELETE:"/expenses/bulk-delete",ADD_TAG:e=>`/expenses/${e}/tags`,REMOVE_TAG:(e,t)=>`/expenses/${e}/tags/${t}`}},o="RON",i="EUR",s="USD",c="GBP",l={[o]:"lei",[i]:"€",[s]:"$",[c]:"£"}},7805:(e,t,n)=>{n.d(t,{A:()=>f});var r=n(1601),a=n.n(r),o=n(6314),i=n.n(o),s=n(4417),c=n.n(s),l=new URL(n(1664),n.b),d=new URL(n(2031),n.b),p=new URL(n(5270),n.b),m=new URL(n(3569),n.b),g=i()(a());g.push([e.id,"@import url(https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap);"]);var u=c()(l),w=c()(d),h=c()(p),b=c()(m);g.push([e.id,`*, ::before, ::after {\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}\n\n::backdrop {\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}/*\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\n*//*\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\n*/\n\n*,\n::before,\n::after {\n  box-sizing: border-box; /* 1 */\n  border-width: 0; /* 2 */\n  border-style: solid; /* 2 */\n  border-color: #e5e7eb; /* 2 */\n}\n\n::before,\n::after {\n  --tw-content: '';\n}\n\n/*\n1. Use a consistent sensible line-height in all browsers.\n2. Prevent adjustments of font size after orientation changes in iOS.\n3. Use a more readable tab size.\n4. Use the user's configured \`sans\` font-family by default.\n5. Use the user's configured \`sans\` font-feature-settings by default.\n6. Use the user's configured \`sans\` font-variation-settings by default.\n7. Disable tap highlights on iOS\n*/\n\nhtml,\n:host {\n  line-height: 1.5; /* 1 */\n  -webkit-text-size-adjust: 100%; /* 2 */ /* 3 */\n  tab-size: 4; /* 3 */\n  font-family: Inter, system-ui, sans-serif; /* 4 */\n  font-feature-settings: normal; /* 5 */\n  font-variation-settings: normal; /* 6 */\n  -webkit-tap-highlight-color: transparent; /* 7 */\n}\n\n/*\n1. Remove the margin in all browsers.\n2. Inherit line-height from \`html\` so users can set them as a class directly on the \`html\` element.\n*/\n\nbody {\n  margin: 0; /* 1 */\n  line-height: inherit; /* 2 */\n}\n\n/*\n1. Add the correct height in Firefox.\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\n3. Ensure horizontal rules are visible by default.\n*/\n\nhr {\n  height: 0; /* 1 */\n  color: inherit; /* 2 */\n  border-top-width: 1px; /* 3 */\n}\n\n/*\nAdd the correct text decoration in Chrome, Edge, and Safari.\n*/\n\nabbr:where([title]) {\n  -webkit-text-decoration: underline dotted;\n          text-decoration: underline dotted;\n}\n\n/*\nRemove the default font size and weight for headings.\n*/\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  font-size: inherit;\n  font-weight: inherit;\n}\n\n/*\nReset links to optimize for opt-in styling instead of opt-out.\n*/\n\na {\n  color: inherit;\n  text-decoration: inherit;\n}\n\n/*\nAdd the correct font weight in Edge and Safari.\n*/\n\nb,\nstrong {\n  font-weight: bolder;\n}\n\n/*\n1. Use the user's configured \`mono\` font-family by default.\n2. Use the user's configured \`mono\` font-feature-settings by default.\n3. Use the user's configured \`mono\` font-variation-settings by default.\n4. Correct the odd \`em\` font sizing in all browsers.\n*/\n\ncode,\nkbd,\nsamp,\npre {\n  font-family: JetBrains Mono, Fira Code, monospace; /* 1 */\n  font-feature-settings: normal; /* 2 */\n  font-variation-settings: normal; /* 3 */\n  font-size: 1em; /* 4 */\n}\n\n/*\nAdd the correct font size in all browsers.\n*/\n\nsmall {\n  font-size: 80%;\n}\n\n/*\nPrevent \`sub\` and \`sup\` elements from affecting the line height in all browsers.\n*/\n\nsub,\nsup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\n\nsub {\n  bottom: -0.25em;\n}\n\nsup {\n  top: -0.5em;\n}\n\n/*\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\n3. Remove gaps between table borders by default.\n*/\n\ntable {\n  text-indent: 0; /* 1 */\n  border-color: inherit; /* 2 */\n  border-collapse: collapse; /* 3 */\n}\n\n/*\n1. Change the font styles in all browsers.\n2. Remove the margin in Firefox and Safari.\n3. Remove default padding in all browsers.\n*/\n\nbutton,\ninput,\noptgroup,\nselect,\ntextarea {\n  font-family: inherit; /* 1 */\n  font-feature-settings: inherit; /* 1 */\n  font-variation-settings: inherit; /* 1 */\n  font-size: 100%; /* 1 */\n  font-weight: inherit; /* 1 */\n  line-height: inherit; /* 1 */\n  letter-spacing: inherit; /* 1 */\n  color: inherit; /* 1 */\n  margin: 0; /* 2 */\n  padding: 0; /* 3 */\n}\n\n/*\nRemove the inheritance of text transform in Edge and Firefox.\n*/\n\nbutton,\nselect {\n  text-transform: none;\n}\n\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Remove default button styles.\n*/\n\nbutton,\ninput:where([type='button']),\ninput:where([type='reset']),\ninput:where([type='submit']) {\n  -webkit-appearance: button; /* 1 */\n  background-color: transparent; /* 2 */\n  background-image: none; /* 2 */\n}\n\n/*\nUse the modern Firefox focus style for all focusable elements.\n*/\n\n:-moz-focusring {\n  outline: auto;\n}\n\n/*\nRemove the additional \`:invalid\` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\n*/\n\n:-moz-ui-invalid {\n  box-shadow: none;\n}\n\n/*\nAdd the correct vertical alignment in Chrome and Firefox.\n*/\n\nprogress {\n  vertical-align: baseline;\n}\n\n/*\nCorrect the cursor style of increment and decrement buttons in Safari.\n*/\n\n::-webkit-inner-spin-button,\n::-webkit-outer-spin-button {\n  height: auto;\n}\n\n/*\n1. Correct the odd appearance in Chrome and Safari.\n2. Correct the outline style in Safari.\n*/\n\n[type='search'] {\n  -webkit-appearance: textfield; /* 1 */\n  outline-offset: -2px; /* 2 */\n}\n\n/*\nRemove the inner padding in Chrome and Safari on macOS.\n*/\n\n::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Change font properties to \`inherit\` in Safari.\n*/\n\n::-webkit-file-upload-button {\n  -webkit-appearance: button; /* 1 */\n  font: inherit; /* 2 */\n}\n\n/*\nAdd the correct display in Chrome and Safari.\n*/\n\nsummary {\n  display: list-item;\n}\n\n/*\nRemoves the default spacing and border for appropriate elements.\n*/\n\nblockquote,\ndl,\ndd,\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\nhr,\nfigure,\np,\npre {\n  margin: 0;\n}\n\nfieldset {\n  margin: 0;\n  padding: 0;\n}\n\nlegend {\n  padding: 0;\n}\n\nol,\nul,\nmenu {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n\n/*\nReset default styling for dialogs.\n*/\ndialog {\n  padding: 0;\n}\n\n/*\nPrevent resizing textareas horizontally by default.\n*/\n\ntextarea {\n  resize: vertical;\n}\n\n/*\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\n2. Set the default placeholder color to the user's configured gray 400 color.\n*/\n\ninput::placeholder,\ntextarea::placeholder {\n  opacity: 1; /* 1 */\n  color: #9ca3af; /* 2 */\n}\n\n/*\nSet the default cursor for buttons.\n*/\n\nbutton,\n[role="button"] {\n  cursor: pointer;\n}\n\n/*\nMake sure disabled buttons don't get the pointer cursor.\n*/\n:disabled {\n  cursor: default;\n}\n\n/*\n1. Make replaced elements \`display: block\` by default. (https://github.com/mozdevs/cssremedy/issues/14)\n2. Add \`vertical-align: middle\` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\n   This can trigger a poorly considered lint error in some tools but is included by design.\n*/\n\nimg,\nsvg,\nvideo,\ncanvas,\naudio,\niframe,\nembed,\nobject {\n  display: block; /* 1 */\n  vertical-align: middle; /* 2 */\n}\n\n/*\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\n*/\n\nimg,\nvideo {\n  max-width: 100%;\n  height: auto;\n}\n\n/* Make elements with the HTML hidden attribute stay hidden by default */\n[hidden]:where(:not([hidden="until-found"])) {\n  display: none;\n}\n\n[type='text'],input:where(:not([type])),[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select {\n  -webkit-appearance: none;\n          appearance: none;\n  background-color: #fff;\n  border-color: #6b7280;\n  border-width: 1px;\n  border-radius: 0px;\n  padding-top: 0.5rem;\n  padding-right: 0.75rem;\n  padding-bottom: 0.5rem;\n  padding-left: 0.75rem;\n  font-size: 1rem;\n  line-height: 1.5rem;\n  --tw-shadow: 0 0 #0000;\n}\n\n[type='text']:focus, input:where(:not([type])):focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: #2563eb;\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  border-color: #2563eb;\n}\n\ninput::placeholder,textarea::placeholder {\n  color: #6b7280;\n  opacity: 1;\n}\n\n::-webkit-datetime-edit-fields-wrapper {\n  padding: 0;\n}\n\n::-webkit-date-and-time-value {\n  min-height: 1.5em;\n  text-align: inherit;\n}\n\n::-webkit-datetime-edit {\n  display: inline-flex;\n}\n\n::-webkit-datetime-edit,::-webkit-datetime-edit-year-field,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-minute-field,::-webkit-datetime-edit-second-field,::-webkit-datetime-edit-millisecond-field,::-webkit-datetime-edit-meridiem-field {\n  padding-top: 0;\n  padding-bottom: 0;\n}\n\nselect {\n  background-image: url(${u});\n  background-position: right 0.5rem center;\n  background-repeat: no-repeat;\n  background-size: 1.5em 1.5em;\n  padding-right: 2.5rem;\n  -webkit-print-color-adjust: exact;\n          print-color-adjust: exact;\n}\n\n[multiple],[size]:where(select:not([size="1"])) {\n  background-image: initial;\n  background-position: initial;\n  background-repeat: unset;\n  background-size: initial;\n  padding-right: 0.75rem;\n  -webkit-print-color-adjust: unset;\n          print-color-adjust: unset;\n}\n\n[type='checkbox'],[type='radio'] {\n  -webkit-appearance: none;\n          appearance: none;\n  padding: 0;\n  -webkit-print-color-adjust: exact;\n          print-color-adjust: exact;\n  display: inline-block;\n  vertical-align: middle;\n  background-origin: border-box;\n  -webkit-user-select: none;\n          user-select: none;\n  flex-shrink: 0;\n  height: 1rem;\n  width: 1rem;\n  color: #2563eb;\n  background-color: #fff;\n  border-color: #6b7280;\n  border-width: 1px;\n  --tw-shadow: 0 0 #0000;\n}\n\n[type='checkbox'] {\n  border-radius: 0px;\n}\n\n[type='radio'] {\n  border-radius: 100%;\n}\n\n[type='checkbox']:focus,[type='radio']:focus {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);\n  --tw-ring-offset-width: 2px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: #2563eb;\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n}\n\n[type='checkbox']:checked,[type='radio']:checked {\n  border-color: transparent;\n  background-color: currentColor;\n  background-size: 100% 100%;\n  background-position: center;\n  background-repeat: no-repeat;\n}\n\n[type='checkbox']:checked {\n  background-image: url(${w});\n}\n\n@media (forced-colors: active)  {\n\n  [type='checkbox']:checked {\n    -webkit-appearance: auto;\n            appearance: auto;\n  }\n}\n\n[type='radio']:checked {\n  background-image: url(${h});\n}\n\n@media (forced-colors: active)  {\n\n  [type='radio']:checked {\n    -webkit-appearance: auto;\n            appearance: auto;\n  }\n}\n\n[type='checkbox']:checked:hover,[type='checkbox']:checked:focus,[type='radio']:checked:hover,[type='radio']:checked:focus {\n  border-color: transparent;\n  background-color: currentColor;\n}\n\n[type='checkbox']:indeterminate {\n  background-image: url(${b});\n  border-color: transparent;\n  background-color: currentColor;\n  background-size: 100% 100%;\n  background-position: center;\n  background-repeat: no-repeat;\n}\n\n@media (forced-colors: active)  {\n\n  [type='checkbox']:indeterminate {\n    -webkit-appearance: auto;\n            appearance: auto;\n  }\n}\n\n[type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus {\n  border-color: transparent;\n  background-color: currentColor;\n}\n\n[type='file'] {\n  background: unset;\n  border-color: inherit;\n  border-width: 0;\n  border-radius: 0;\n  padding: 0;\n  font-size: unset;\n  line-height: inherit;\n}\n\n[type='file']:focus {\n  outline: 1px solid ButtonText;\n  outline: 1px auto -webkit-focus-ring-color;\n}\n  html {\n  scroll-behavior: smooth;\n}\n\n  body {\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\n  font-family: Inter, system-ui, sans-serif;\n  --tw-text-opacity: 1;\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n    font-feature-settings: 'cv11', 'ss01';\n    font-variation-settings: 'opsz' 32;\n}\n\n  /* Stiluri pentru scrollbar */\n  ::-webkit-scrollbar {\n  width: 0.5rem;\n}\n\n  ::-webkit-scrollbar-track {\n  --tw-bg-opacity: 1;\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\n}\n\n  ::-webkit-scrollbar-thumb {\n  border-radius: 9999px;\n  --tw-bg-opacity: 1;\n  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));\n}\n\n  ::-webkit-scrollbar-thumb:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));\n}\n\n  /* Stiluri pentru focus */\n  :focus {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n\n  :focus-visible {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));\n  --tw-ring-offset-width: 2px;\n}\n\n  /* Stiluri pentru selecție text */\n  ::selection {\n  --tw-bg-opacity: 1;\n  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));\n  --tw-text-opacity: 1;\n  color: rgb(30 58 138 / var(--tw-text-opacity, 1));\n}\n\n  /* Stiluri pentru input-uri */\n  input[type="number"]::-webkit-outer-spin-button,\n  input[type="number"]::-webkit-inner-spin-button {\n    -webkit-appearance: none;\n    appearance: none;\n    margin: 0;\n  }\n\n  input[type="number"] {\n    -webkit-appearance: textfield;\n            appearance: textfield;\n  }\n.container {\n  width: 100%;\n}\n@media (min-width: 475px) {\n\n  .container {\n    max-width: 475px;\n  }\n}\n@media (min-width: 640px) {\n\n  .container {\n    max-width: 640px;\n  }\n}\n@media (min-width: 768px) {\n\n  .container {\n    max-width: 768px;\n  }\n}\n@media (min-width: 1024px) {\n\n  .container {\n    max-width: 1024px;\n  }\n}\n@media (min-width: 1280px) {\n\n  .container {\n    max-width: 1280px;\n  }\n}\n@media (min-width: 1536px) {\n\n  .container {\n    max-width: 1536px;\n  }\n}\n@media (min-width: 1600px) {\n\n  .container {\n    max-width: 1600px;\n  }\n}\n.prose {\n  color: var(--tw-prose-body);\n  max-width: 65ch;\n}\n.prose :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 1.25em;\n  margin-bottom: 1.25em;\n}\n.prose :where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  color: var(--tw-prose-lead);\n  font-size: 1.25em;\n  line-height: 1.6;\n  margin-top: 1.2em;\n  margin-bottom: 1.2em;\n}\n.prose :where(a):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  color: var(--tw-prose-links);\n  text-decoration: underline;\n  font-weight: 500;\n}\n.prose :where(strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  color: var(--tw-prose-bold);\n  font-weight: 600;\n}\n.prose :where(a strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  color: inherit;\n}\n.prose :where(blockquote strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  color: inherit;\n}\n.prose :where(thead th strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  color: inherit;\n}\n.prose :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  list-style-type: decimal;\n  margin-top: 1.25em;\n  margin-bottom: 1.25em;\n  -webkit-padding-start: 1.625em;\n          padding-inline-start: 1.625em;\n}\n.prose :where(ol[type="A"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  list-style-type: upper-alpha;\n}\n.prose :where(ol[type="a"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  list-style-type: lower-alpha;\n}\n.prose :where(ol[type="A" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  list-style-type: upper-alpha;\n}\n.prose :where(ol[type="a" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  list-style-type: lower-alpha;\n}\n.prose :where(ol[type="I"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  list-style-type: upper-roman;\n}\n.prose :where(ol[type="i"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  list-style-type: lower-roman;\n}\n.prose :where(ol[type="I" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  list-style-type: upper-roman;\n}\n.prose :where(ol[type="i" s]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  list-style-type: lower-roman;\n}\n.prose :where(ol[type="1"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  list-style-type: decimal;\n}\n.prose :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  list-style-type: disc;\n  margin-top: 1.25em;\n  margin-bottom: 1.25em;\n  -webkit-padding-start: 1.625em;\n          padding-inline-start: 1.625em;\n}\n.prose :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *))::marker {\n  font-weight: 400;\n  color: var(--tw-prose-counters);\n}\n.prose :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *))::marker {\n  color: var(--tw-prose-bullets);\n}\n.prose :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  color: var(--tw-prose-headings);\n  font-weight: 600;\n  margin-top: 1.25em;\n}\n.prose :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  border-color: var(--tw-prose-hr);\n  border-top-width: 1px;\n  margin-top: 3em;\n  margin-bottom: 3em;\n}\n.prose :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  font-weight: 500;\n  font-style: italic;\n  color: var(--tw-prose-quotes);\n  border-inline-start-width: 0.25rem;\n  border-inline-start-color: var(--tw-prose-quote-borders);\n  quotes: "\\201C""\\201D""\\2018""\\2019";\n  margin-top: 1.6em;\n  margin-bottom: 1.6em;\n  -webkit-padding-start: 1em;\n          padding-inline-start: 1em;\n}\n.prose :where(blockquote p:first-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {\n  content: open-quote;\n}\n.prose :where(blockquote p:last-of-type):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {\n  content: close-quote;\n}\n.prose :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  color: var(--tw-prose-headings);\n  font-weight: 800;\n  font-size: 2.25em;\n  margin-top: 0;\n  margin-bottom: 0.8888889em;\n  line-height: 1.1111111;\n}\n.prose :where(h1 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  font-weight: 900;\n  color: inherit;\n}\n.prose :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  color: var(--tw-prose-headings);\n  font-weight: 700;\n  font-size: 1.5em;\n  margin-top: 2em;\n  margin-bottom: 1em;\n  line-height: 1.3333333;\n}\n.prose :where(h2 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  font-weight: 800;\n  color: inherit;\n}\n.prose :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  color: var(--tw-prose-headings);\n  font-weight: 600;\n  font-size: 1.25em;\n  margin-top: 1.6em;\n  margin-bottom: 0.6em;\n  line-height: 1.6;\n}\n.prose :where(h3 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  font-weight: 700;\n  color: inherit;\n}\n.prose :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  color: var(--tw-prose-headings);\n  font-weight: 600;\n  margin-top: 1.5em;\n  margin-bottom: 0.5em;\n  line-height: 1.5;\n}\n.prose :where(h4 strong):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  font-weight: 700;\n  color: inherit;\n}\n.prose :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 2em;\n  margin-bottom: 2em;\n}\n.prose :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  display: block;\n  margin-top: 2em;\n  margin-bottom: 2em;\n}\n.prose :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 2em;\n  margin-bottom: 2em;\n}\n.prose :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  font-weight: 500;\n  font-family: inherit;\n  color: var(--tw-prose-kbd);\n  box-shadow: 0 0 0 1px rgb(var(--tw-prose-kbd-shadows) / 10%), 0 3px 0 rgb(var(--tw-prose-kbd-shadows) / 10%);\n  font-size: 0.875em;\n  border-radius: 0.3125rem;\n  padding-top: 0.1875em;\n  -webkit-padding-end: 0.375em;\n          padding-inline-end: 0.375em;\n  padding-bottom: 0.1875em;\n  -webkit-padding-start: 0.375em;\n          padding-inline-start: 0.375em;\n}\n.prose :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  color: var(--tw-prose-code);\n  font-weight: 600;\n  font-size: 0.875em;\n}\n.prose :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {\n  content: "\`";\n}\n.prose :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {\n  content: "\`";\n}\n.prose :where(a code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  color: inherit;\n}\n.prose :where(h1 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  color: inherit;\n}\n.prose :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  color: inherit;\n  font-size: 0.875em;\n}\n.prose :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  color: inherit;\n  font-size: 0.9em;\n}\n.prose :where(h4 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  color: inherit;\n}\n.prose :where(blockquote code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  color: inherit;\n}\n.prose :where(thead th code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  color: inherit;\n}\n.prose :where(pre):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  color: var(--tw-prose-pre-code);\n  background-color: var(--tw-prose-pre-bg);\n  overflow-x: auto;\n  font-weight: 400;\n  font-size: 0.875em;\n  line-height: 1.7142857;\n  margin-top: 1.7142857em;\n  margin-bottom: 1.7142857em;\n  border-radius: 0.375rem;\n  padding-top: 0.8571429em;\n  -webkit-padding-end: 1.1428571em;\n          padding-inline-end: 1.1428571em;\n  padding-bottom: 0.8571429em;\n  -webkit-padding-start: 1.1428571em;\n          padding-inline-start: 1.1428571em;\n}\n.prose :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  background-color: transparent;\n  border-width: 0;\n  border-radius: 0;\n  padding: 0;\n  font-weight: inherit;\n  color: inherit;\n  font-size: inherit;\n  font-family: inherit;\n  line-height: inherit;\n}\n.prose :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *))::before {\n  content: none;\n}\n.prose :where(pre code):not(:where([class~="not-prose"],[class~="not-prose"] *))::after {\n  content: none;\n}\n.prose :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  width: 100%;\n  table-layout: auto;\n  margin-top: 2em;\n  margin-bottom: 2em;\n  font-size: 0.875em;\n  line-height: 1.7142857;\n}\n.prose :where(thead):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  border-bottom-width: 1px;\n  border-bottom-color: var(--tw-prose-th-borders);\n}\n.prose :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  color: var(--tw-prose-headings);\n  font-weight: 600;\n  vertical-align: bottom;\n  -webkit-padding-end: 0.5714286em;\n          padding-inline-end: 0.5714286em;\n  padding-bottom: 0.5714286em;\n  -webkit-padding-start: 0.5714286em;\n          padding-inline-start: 0.5714286em;\n}\n.prose :where(tbody tr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  border-bottom-width: 1px;\n  border-bottom-color: var(--tw-prose-td-borders);\n}\n.prose :where(tbody tr:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  border-bottom-width: 0;\n}\n.prose :where(tbody td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  vertical-align: baseline;\n}\n.prose :where(tfoot):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  border-top-width: 1px;\n  border-top-color: var(--tw-prose-th-borders);\n}\n.prose :where(tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  vertical-align: top;\n}\n.prose :where(th, td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  text-align: start;\n}\n.prose :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 0;\n  margin-bottom: 0;\n}\n.prose :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  color: var(--tw-prose-captions);\n  font-size: 0.875em;\n  line-height: 1.4285714;\n  margin-top: 0.8571429em;\n}\n.prose {\n  --tw-prose-body: #374151;\n  --tw-prose-headings: #111827;\n  --tw-prose-lead: #4b5563;\n  --tw-prose-links: #111827;\n  --tw-prose-bold: #111827;\n  --tw-prose-counters: #6b7280;\n  --tw-prose-bullets: #d1d5db;\n  --tw-prose-hr: #e5e7eb;\n  --tw-prose-quotes: #111827;\n  --tw-prose-quote-borders: #e5e7eb;\n  --tw-prose-captions: #6b7280;\n  --tw-prose-kbd: #111827;\n  --tw-prose-kbd-shadows: 17 24 39;\n  --tw-prose-code: #111827;\n  --tw-prose-pre-code: #e5e7eb;\n  --tw-prose-pre-bg: #1f2937;\n  --tw-prose-th-borders: #d1d5db;\n  --tw-prose-td-borders: #e5e7eb;\n  --tw-prose-invert-body: #d1d5db;\n  --tw-prose-invert-headings: #fff;\n  --tw-prose-invert-lead: #9ca3af;\n  --tw-prose-invert-links: #fff;\n  --tw-prose-invert-bold: #fff;\n  --tw-prose-invert-counters: #9ca3af;\n  --tw-prose-invert-bullets: #4b5563;\n  --tw-prose-invert-hr: #374151;\n  --tw-prose-invert-quotes: #f3f4f6;\n  --tw-prose-invert-quote-borders: #374151;\n  --tw-prose-invert-captions: #9ca3af;\n  --tw-prose-invert-kbd: #fff;\n  --tw-prose-invert-kbd-shadows: 255 255 255;\n  --tw-prose-invert-code: #fff;\n  --tw-prose-invert-pre-code: #d1d5db;\n  --tw-prose-invert-pre-bg: rgb(0 0 0 / 50%);\n  --tw-prose-invert-th-borders: #4b5563;\n  --tw-prose-invert-td-borders: #374151;\n  font-size: 1rem;\n  line-height: 1.75;\n}\n.prose :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 0;\n  margin-bottom: 0;\n}\n.prose :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 0.5em;\n  margin-bottom: 0.5em;\n}\n.prose :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  -webkit-padding-start: 0.375em;\n          padding-inline-start: 0.375em;\n}\n.prose :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  -webkit-padding-start: 0.375em;\n          padding-inline-start: 0.375em;\n}\n.prose :where(.prose > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 0.75em;\n  margin-bottom: 0.75em;\n}\n.prose :where(.prose > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 1.25em;\n}\n.prose :where(.prose > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-bottom: 1.25em;\n}\n.prose :where(.prose > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 1.25em;\n}\n.prose :where(.prose > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-bottom: 1.25em;\n}\n.prose :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 0.75em;\n  margin-bottom: 0.75em;\n}\n.prose :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 1.25em;\n  margin-bottom: 1.25em;\n}\n.prose :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 0.5em;\n  -webkit-padding-start: 1.625em;\n          padding-inline-start: 1.625em;\n}\n.prose :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 0;\n}\n.prose :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 0;\n}\n.prose :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 0;\n}\n.prose :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 0;\n}\n.prose :where(thead th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  -webkit-padding-start: 0;\n          padding-inline-start: 0;\n}\n.prose :where(thead th:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  -webkit-padding-end: 0;\n          padding-inline-end: 0;\n}\n.prose :where(tbody td, tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  padding-top: 0.5714286em;\n  -webkit-padding-end: 0.5714286em;\n          padding-inline-end: 0.5714286em;\n  padding-bottom: 0.5714286em;\n  -webkit-padding-start: 0.5714286em;\n          padding-inline-start: 0.5714286em;\n}\n.prose :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  -webkit-padding-start: 0;\n          padding-inline-start: 0;\n}\n.prose :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  -webkit-padding-end: 0;\n          padding-inline-end: 0;\n}\n.prose :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 2em;\n  margin-bottom: 2em;\n}\n.prose :where(.prose > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 0;\n}\n.prose :where(.prose > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-bottom: 0;\n}\n.prose-lg {\n  font-size: 1.125rem;\n  line-height: 1.7777778;\n}\n.prose-lg :where(p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 1.3333333em;\n  margin-bottom: 1.3333333em;\n}\n.prose-lg :where([class~="lead"]):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  font-size: 1.2222222em;\n  line-height: 1.4545455;\n  margin-top: 1.0909091em;\n  margin-bottom: 1.0909091em;\n}\n.prose-lg :where(blockquote):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 1.6666667em;\n  margin-bottom: 1.6666667em;\n  -webkit-padding-start: 1em;\n          padding-inline-start: 1em;\n}\n.prose-lg :where(h1):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  font-size: 2.6666667em;\n  margin-top: 0;\n  margin-bottom: 0.8333333em;\n  line-height: 1;\n}\n.prose-lg :where(h2):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  font-size: 1.6666667em;\n  margin-top: 1.8666667em;\n  margin-bottom: 1.0666667em;\n  line-height: 1.3333333;\n}\n.prose-lg :where(h3):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  font-size: 1.3333333em;\n  margin-top: 1.6666667em;\n  margin-bottom: 0.6666667em;\n  line-height: 1.5;\n}\n.prose-lg :where(h4):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 1.7777778em;\n  margin-bottom: 0.4444444em;\n  line-height: 1.5555556;\n}\n.prose-lg :where(img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 1.7777778em;\n  margin-bottom: 1.7777778em;\n}\n.prose-lg :where(picture):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 1.7777778em;\n  margin-bottom: 1.7777778em;\n}\n.prose-lg :where(picture > img):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 0;\n  margin-bottom: 0;\n}\n.prose-lg :where(video):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 1.7777778em;\n  margin-bottom: 1.7777778em;\n}\n.prose-lg :where(kbd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  font-size: 0.8888889em;\n  border-radius: 0.3125rem;\n  padding-top: 0.2222222em;\n  -webkit-padding-end: 0.4444444em;\n          padding-inline-end: 0.4444444em;\n  padding-bottom: 0.2222222em;\n  -webkit-padding-start: 0.4444444em;\n          padding-inline-start: 0.4444444em;\n}\n.prose-lg :where(code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  font-size: 0.8888889em;\n}\n.prose-lg :where(h2 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  font-size: 0.8666667em;\n}\n.prose-lg :where(h3 code):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  font-size: 0.875em;\n}\n.prose-lg :where(pre):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  font-size: 0.8888889em;\n  line-height: 1.75;\n  margin-top: 2em;\n  margin-bottom: 2em;\n  border-radius: 0.375rem;\n  padding-top: 1em;\n  -webkit-padding-end: 1.5em;\n          padding-inline-end: 1.5em;\n  padding-bottom: 1em;\n  -webkit-padding-start: 1.5em;\n          padding-inline-start: 1.5em;\n}\n.prose-lg :where(ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 1.3333333em;\n  margin-bottom: 1.3333333em;\n  -webkit-padding-start: 1.5555556em;\n          padding-inline-start: 1.5555556em;\n}\n.prose-lg :where(ul):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 1.3333333em;\n  margin-bottom: 1.3333333em;\n  -webkit-padding-start: 1.5555556em;\n          padding-inline-start: 1.5555556em;\n}\n.prose-lg :where(li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 0.6666667em;\n  margin-bottom: 0.6666667em;\n}\n.prose-lg :where(ol > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  -webkit-padding-start: 0.4444444em;\n          padding-inline-start: 0.4444444em;\n}\n.prose-lg :where(ul > li):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  -webkit-padding-start: 0.4444444em;\n          padding-inline-start: 0.4444444em;\n}\n.prose-lg :where(.prose-lg > ul > li p):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 0.8888889em;\n  margin-bottom: 0.8888889em;\n}\n.prose-lg :where(.prose-lg > ul > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 1.3333333em;\n}\n.prose-lg :where(.prose-lg > ul > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-bottom: 1.3333333em;\n}\n.prose-lg :where(.prose-lg > ol > li > p:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 1.3333333em;\n}\n.prose-lg :where(.prose-lg > ol > li > p:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-bottom: 1.3333333em;\n}\n.prose-lg :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 0.8888889em;\n  margin-bottom: 0.8888889em;\n}\n.prose-lg :where(dl):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 1.3333333em;\n  margin-bottom: 1.3333333em;\n}\n.prose-lg :where(dt):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 1.3333333em;\n}\n.prose-lg :where(dd):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 0.6666667em;\n  -webkit-padding-start: 1.5555556em;\n          padding-inline-start: 1.5555556em;\n}\n.prose-lg :where(hr):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 3.1111111em;\n  margin-bottom: 3.1111111em;\n}\n.prose-lg :where(hr + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 0;\n}\n.prose-lg :where(h2 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 0;\n}\n.prose-lg :where(h3 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 0;\n}\n.prose-lg :where(h4 + *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 0;\n}\n.prose-lg :where(table):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  font-size: 0.8888889em;\n  line-height: 1.5;\n}\n.prose-lg :where(thead th):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  -webkit-padding-end: 0.75em;\n          padding-inline-end: 0.75em;\n  padding-bottom: 0.75em;\n  -webkit-padding-start: 0.75em;\n          padding-inline-start: 0.75em;\n}\n.prose-lg :where(thead th:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  -webkit-padding-start: 0;\n          padding-inline-start: 0;\n}\n.prose-lg :where(thead th:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  -webkit-padding-end: 0;\n          padding-inline-end: 0;\n}\n.prose-lg :where(tbody td, tfoot td):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  padding-top: 0.75em;\n  -webkit-padding-end: 0.75em;\n          padding-inline-end: 0.75em;\n  padding-bottom: 0.75em;\n  -webkit-padding-start: 0.75em;\n          padding-inline-start: 0.75em;\n}\n.prose-lg :where(tbody td:first-child, tfoot td:first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  -webkit-padding-start: 0;\n          padding-inline-start: 0;\n}\n.prose-lg :where(tbody td:last-child, tfoot td:last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  -webkit-padding-end: 0;\n          padding-inline-end: 0;\n}\n.prose-lg :where(figure):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 1.7777778em;\n  margin-bottom: 1.7777778em;\n}\n.prose-lg :where(figure > *):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 0;\n  margin-bottom: 0;\n}\n.prose-lg :where(figcaption):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  font-size: 0.8888889em;\n  line-height: 1.5;\n  margin-top: 1em;\n}\n.prose-lg :where(.prose-lg > :first-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-top: 0;\n}\n.prose-lg :where(.prose-lg > :last-child):not(:where([class~="not-prose"],[class~="not-prose"] *)) {\n  margin-bottom: 0;\n}\n.aspect-h-9 {\n  --tw-aspect-h: 9;\n}\n.aspect-w-16 {\n  position: relative;\n  padding-bottom: calc(var(--tw-aspect-h) / var(--tw-aspect-w) * 100%);\n  --tw-aspect-w: 16;\n}\n.aspect-w-16 > * {\n  position: absolute;\n  height: 100%;\n  width: 100%;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n}\n/* Butoane */\n/* Input-uri */\n.input {\n  display: flex;\n  height: 2.5rem;\n  width: 100%;\n  border-radius: 0.375rem;\n  border-width: 1px;\n  --tw-border-opacity: 1;\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\n  background-color: transparent;\n  padding-left: 0.75rem;\n  padding-right: 0.75rem;\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n  --tw-ring-offset-color: #fff;\n}\n.input::-webkit-file-upload-button {\n  border-width: 0px;\n  background-color: transparent;\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n  font-weight: 500;\n}\n.input::file-selector-button {\n  border-width: 0px;\n  background-color: transparent;\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n  font-weight: 500;\n}\n.input::placeholder {\n  --tw-text-opacity: 1;\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\n}\n.input:focus-visible {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));\n  --tw-ring-offset-width: 2px;\n}\n.input:disabled {\n  cursor: not-allowed;\n  opacity: 0.5;\n}\n/* Card-uri */\n.card {\n  border-radius: 0.5rem;\n  border-width: 1px;\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\n  --tw-text-opacity: 1;\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n/* Badge-uri */\n.badge {\n  display: inline-flex;\n  align-items: center;\n  border-radius: 9999px;\n  border-width: 1px;\n  padding-left: 0.625rem;\n  padding-right: 0.625rem;\n  padding-top: 0.125rem;\n  padding-bottom: 0.125rem;\n  font-size: 0.75rem;\n  line-height: 1rem;\n  font-weight: 600;\n  transition-property: color, background-color, border-color, fill, stroke, -webkit-text-decoration-color;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, -webkit-text-decoration-color;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.badge:focus {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));\n  --tw-ring-offset-width: 2px;\n}\n/* Alerte */\n.alert {\n  position: relative;\n  width: 100%;\n  border-radius: 0.5rem;\n  border-width: 1px;\n  padding: 1rem;\n}\n/* Skeleton loading */\n/* Separator */\n.separator {\n  flex-shrink: 0;\n  --tw-bg-opacity: 1;\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\n}\n.sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border-width: 0;\n}\n.pointer-events-none {\n  pointer-events: none;\n}\n.pointer-events-auto {\n  pointer-events: auto;\n}\n.collapse {\n  visibility: collapse;\n}\n.static {\n  position: static;\n}\n.fixed {\n  position: fixed;\n}\n.absolute {\n  position: absolute;\n}\n.relative {\n  position: relative;\n}\n.sticky {\n  position: -webkit-sticky;\n  position: sticky;\n}\n.inset-0 {\n  inset: 0px;\n}\n.inset-y-0 {\n  top: 0px;\n  bottom: 0px;\n}\n.-bottom-0\\.5 {\n  bottom: -0.125rem;\n}\n.-bottom-1 {\n  bottom: -0.25rem;\n}\n.-right-0\\.5 {\n  right: -0.125rem;\n}\n.-right-1 {\n  right: -0.25rem;\n}\n.-top-1 {\n  top: -0.25rem;\n}\n.-top-4 {\n  top: -1rem;\n}\n.bottom-0 {\n  bottom: 0px;\n}\n.bottom-10 {\n  bottom: 2.5rem;\n}\n.bottom-20 {\n  bottom: 5rem;\n}\n.bottom-4 {\n  bottom: 1rem;\n}\n.bottom-6 {\n  bottom: 1.5rem;\n}\n.bottom-full {\n  bottom: 100%;\n}\n.left-0 {\n  left: 0px;\n}\n.left-1\\/2 {\n  left: 50%;\n}\n.left-1\\/4 {\n  left: 25%;\n}\n.left-10 {\n  left: 2.5rem;\n}\n.left-20 {\n  left: 5rem;\n}\n.left-3 {\n  left: 0.75rem;\n}\n.left-4 {\n  left: 1rem;\n}\n.left-full {\n  left: 100%;\n}\n.right-0 {\n  right: 0px;\n}\n.right-1\\/4 {\n  right: 25%;\n}\n.right-10 {\n  right: 2.5rem;\n}\n.right-20 {\n  right: 5rem;\n}\n.right-4 {\n  right: 1rem;\n}\n.right-6 {\n  right: 1.5rem;\n}\n.right-full {\n  right: 100%;\n}\n.top-0 {\n  top: 0px;\n}\n.top-1\\/2 {\n  top: 50%;\n}\n.top-10 {\n  top: 2.5rem;\n}\n.top-16 {\n  top: 4rem;\n}\n.top-20 {\n  top: 5rem;\n}\n.top-4 {\n  top: 1rem;\n}\n.top-40 {\n  top: 10rem;\n}\n.top-full {\n  top: 100%;\n}\n.z-10 {\n  z-index: 10;\n}\n.z-30 {\n  z-index: 30;\n}\n.z-40 {\n  z-index: 40;\n}\n.z-50 {\n  z-index: 50;\n}\n.col-span-1 {\n  grid-column: span 1 / span 1;\n}\n.-mx-1\\.5 {\n  margin-left: -0.375rem;\n  margin-right: -0.375rem;\n}\n.-my-1\\.5 {\n  margin-top: -0.375rem;\n  margin-bottom: -0.375rem;\n}\n.mx-2 {\n  margin-left: 0.5rem;\n  margin-right: 0.5rem;\n}\n.mx-4 {\n  margin-left: 1rem;\n  margin-right: 1rem;\n}\n.mx-8 {\n  margin-left: 2rem;\n  margin-right: 2rem;\n}\n.mx-auto {\n  margin-left: auto;\n  margin-right: auto;\n}\n.my-1 {\n  margin-top: 0.25rem;\n  margin-bottom: 0.25rem;\n}\n.my-2 {\n  margin-top: 0.5rem;\n  margin-bottom: 0.5rem;\n}\n.my-4 {\n  margin-top: 1rem;\n  margin-bottom: 1rem;\n}\n.-mb-1 {\n  margin-bottom: -0.25rem;\n}\n.-mb-px {\n  margin-bottom: -1px;\n}\n.-mt-1 {\n  margin-top: -0.25rem;\n}\n.mb-1 {\n  margin-bottom: 0.25rem;\n}\n.mb-10 {\n  margin-bottom: 2.5rem;\n}\n.mb-12 {\n  margin-bottom: 3rem;\n}\n.mb-16 {\n  margin-bottom: 4rem;\n}\n.mb-2 {\n  margin-bottom: 0.5rem;\n}\n.mb-3 {\n  margin-bottom: 0.75rem;\n}\n.mb-4 {\n  margin-bottom: 1rem;\n}\n.mb-6 {\n  margin-bottom: 1.5rem;\n}\n.mb-8 {\n  margin-bottom: 2rem;\n}\n.ml-0 {\n  margin-left: 0px;\n}\n.ml-1 {\n  margin-left: 0.25rem;\n}\n.ml-16 {\n  margin-left: 4rem;\n}\n.ml-2 {\n  margin-left: 0.5rem;\n}\n.ml-3 {\n  margin-left: 0.75rem;\n}\n.ml-4 {\n  margin-left: 1rem;\n}\n.ml-64 {\n  margin-left: 16rem;\n}\n.ml-auto {\n  margin-left: auto;\n}\n.mr-1 {\n  margin-right: 0.25rem;\n}\n.mr-2 {\n  margin-right: 0.5rem;\n}\n.mr-3 {\n  margin-right: 0.75rem;\n}\n.mr-4 {\n  margin-right: 1rem;\n}\n.mt-0\\.5 {\n  margin-top: 0.125rem;\n}\n.mt-1 {\n  margin-top: 0.25rem;\n}\n.mt-12 {\n  margin-top: 3rem;\n}\n.mt-16 {\n  margin-top: 4rem;\n}\n.mt-2 {\n  margin-top: 0.5rem;\n}\n.mt-3 {\n  margin-top: 0.75rem;\n}\n.mt-4 {\n  margin-top: 1rem;\n}\n.mt-6 {\n  margin-top: 1.5rem;\n}\n.mt-8 {\n  margin-top: 2rem;\n}\n.line-clamp-2 {\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 2;\n}\n.block {\n  display: block;\n}\n.inline-block {\n  display: inline-block;\n}\n.inline {\n  display: inline;\n}\n.flex {\n  display: flex;\n}\n.inline-flex {\n  display: inline-flex;\n}\n.table {\n  display: table;\n}\n.grid {\n  display: grid;\n}\n.hidden {\n  display: none;\n}\n.h-0\\.5 {\n  height: 0.125rem;\n}\n.h-1 {\n  height: 0.25rem;\n}\n.h-1\\.5 {\n  height: 0.375rem;\n}\n.h-10 {\n  height: 2.5rem;\n}\n.h-12 {\n  height: 3rem;\n}\n.h-16 {\n  height: 4rem;\n}\n.h-2 {\n  height: 0.5rem;\n}\n.h-2\\.5 {\n  height: 0.625rem;\n}\n.h-24 {\n  height: 6rem;\n}\n.h-3 {\n  height: 0.75rem;\n}\n.h-32 {\n  height: 8rem;\n}\n.h-4 {\n  height: 1rem;\n}\n.h-48 {\n  height: 12rem;\n}\n.h-5 {\n  height: 1.25rem;\n}\n.h-6 {\n  height: 1.5rem;\n}\n.h-64 {\n  height: 16rem;\n}\n.h-72 {\n  height: 18rem;\n}\n.h-8 {\n  height: 2rem;\n}\n.h-80 {\n  height: 20rem;\n}\n.h-96 {\n  height: 24rem;\n}\n.h-auto {\n  height: auto;\n}\n.h-full {\n  height: 100%;\n}\n.max-h-32 {\n  max-height: 8rem;\n}\n.max-h-40 {\n  max-height: 10rem;\n}\n.max-h-60 {\n  max-height: 15rem;\n}\n.max-h-64 {\n  max-height: 16rem;\n}\n.max-h-96 {\n  max-height: 24rem;\n}\n.min-h-0 {\n  min-height: 0px;\n}\n.min-h-screen {\n  min-height: 100vh;\n}\n.w-0 {\n  width: 0px;\n}\n.w-0\\.5 {\n  width: 0.125rem;\n}\n.w-1 {\n  width: 0.25rem;\n}\n.w-1\\.5 {\n  width: 0.375rem;\n}\n.w-10 {\n  width: 2.5rem;\n}\n.w-11 {\n  width: 2.75rem;\n}\n.w-12 {\n  width: 3rem;\n}\n.w-16 {\n  width: 4rem;\n}\n.w-2 {\n  width: 0.5rem;\n}\n.w-2\\.5 {\n  width: 0.625rem;\n}\n.w-20 {\n  width: 5rem;\n}\n.w-24 {\n  width: 6rem;\n}\n.w-3 {\n  width: 0.75rem;\n}\n.w-32 {\n  width: 8rem;\n}\n.w-4 {\n  width: 1rem;\n}\n.w-5 {\n  width: 1.25rem;\n}\n.w-56 {\n  width: 14rem;\n}\n.w-6 {\n  width: 1.5rem;\n}\n.w-64 {\n  width: 16rem;\n}\n.w-72 {\n  width: 18rem;\n}\n.w-8 {\n  width: 2rem;\n}\n.w-80 {\n  width: 20rem;\n}\n.w-96 {\n  width: 24rem;\n}\n.w-auto {\n  width: auto;\n}\n.w-full {\n  width: 100%;\n}\n.w-px {\n  width: 1px;\n}\n.min-w-0 {\n  min-width: 0px;\n}\n.min-w-\\[1\\.25rem\\] {\n  min-width: 1.25rem;\n}\n.min-w-\\[1\\.5rem\\] {\n  min-width: 1.5rem;\n}\n.min-w-\\[1rem\\] {\n  min-width: 1rem;\n}\n.min-w-\\[2\\.5rem\\] {\n  min-width: 2.5rem;\n}\n.min-w-\\[200px\\] {\n  min-width: 200px;\n}\n.min-w-\\[2rem\\] {\n  min-width: 2rem;\n}\n.min-w-\\[3rem\\] {\n  min-width: 3rem;\n}\n.min-w-full {\n  min-width: 100%;\n}\n.min-w-max {\n  min-width: -webkit-max-content;\n  min-width: max-content;\n}\n.max-w-0 {\n  max-width: 0px;\n}\n.max-w-2xl {\n  max-width: 42rem;\n}\n.max-w-3xl {\n  max-width: 48rem;\n}\n.max-w-4xl {\n  max-width: 56rem;\n}\n.max-w-5xl {\n  max-width: 64rem;\n}\n.max-w-6xl {\n  max-width: 72rem;\n}\n.max-w-7xl {\n  max-width: 80rem;\n}\n.max-w-full {\n  max-width: 100%;\n}\n.max-w-lg {\n  max-width: 32rem;\n}\n.max-w-md {\n  max-width: 28rem;\n}\n.max-w-none {\n  max-width: none;\n}\n.max-w-sm {\n  max-width: 24rem;\n}\n.max-w-xl {\n  max-width: 36rem;\n}\n.max-w-xs {\n  max-width: 20rem;\n}\n.flex-1 {\n  flex: 1 1 0%;\n}\n.flex-shrink-0 {\n  flex-shrink: 0;\n}\n.origin-top {\n  transform-origin: top;\n}\n.-translate-x-1\\/2 {\n  --tw-translate-x: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.-translate-x-full {\n  --tw-translate-x: -100%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.-translate-y-1\\/2 {\n  --tw-translate-y: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-x-0 {\n  --tw-translate-x: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-x-1 {\n  --tw-translate-x: 0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-x-6 {\n  --tw-translate-x: 1.5rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-x-full {\n  --tw-translate-x: 100%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.-rotate-90 {\n  --tw-rotate: -90deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.rotate-180 {\n  --tw-rotate: 180deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.rotate-90 {\n  --tw-rotate: 90deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.scale-100 {\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.scale-105 {\n  --tw-scale-x: 1.05;\n  --tw-scale-y: 1.05;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.scale-95 {\n  --tw-scale-x: .95;\n  --tw-scale-y: .95;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.transform {\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n@keyframes pulse {\n\n  50% {\n    opacity: .5;\n  }\n}\n.animate-pulse {\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n}\n@keyframes scaleIn {\n\n  0% {\n    transform: scale(0.95);\n    opacity: 0;\n  }\n\n  100% {\n    transform: scale(1);\n    opacity: 1;\n  }\n}\n.animate-scale-in {\n  animation: scaleIn 0.2s ease-out;\n}\n@keyframes spin {\n\n  to {\n    transform: rotate(360deg);\n  }\n}\n.animate-spin {\n  animation: spin 1s linear infinite;\n}\n.cursor-default {\n  cursor: default;\n}\n.cursor-help {\n  cursor: help;\n}\n.cursor-not-allowed {\n  cursor: not-allowed;\n}\n.cursor-pointer {\n  cursor: pointer;\n}\n.select-none {\n  -webkit-user-select: none;\n          user-select: none;\n}\n.resize {\n  resize: both;\n}\n.list-inside {\n  list-style-position: inside;\n}\n.list-disc {\n  list-style-type: disc;\n}\n.grid-cols-1 {\n  grid-template-columns: repeat(1, minmax(0, 1fr));\n}\n.grid-cols-2 {\n  grid-template-columns: repeat(2, minmax(0, 1fr));\n}\n.grid-cols-3 {\n  grid-template-columns: repeat(3, minmax(0, 1fr));\n}\n.grid-cols-4 {\n  grid-template-columns: repeat(4, minmax(0, 1fr));\n}\n.flex-row {\n  flex-direction: row;\n}\n.flex-col {\n  flex-direction: column;\n}\n.flex-wrap {\n  flex-wrap: wrap;\n}\n.items-start {\n  align-items: flex-start;\n}\n.items-end {\n  align-items: flex-end;\n}\n.items-center {\n  align-items: center;\n}\n.justify-start {\n  justify-content: flex-start;\n}\n.justify-end {\n  justify-content: flex-end;\n}\n.justify-center {\n  justify-content: center;\n}\n.justify-between {\n  justify-content: space-between;\n}\n.gap-1 {\n  gap: 0.25rem;\n}\n.gap-1\\.5 {\n  gap: 0.375rem;\n}\n.gap-2 {\n  gap: 0.5rem;\n}\n.gap-3 {\n  gap: 0.75rem;\n}\n.gap-4 {\n  gap: 1rem;\n}\n.gap-6 {\n  gap: 1.5rem;\n}\n.gap-8 {\n  gap: 2rem;\n}\n.space-x-0\\.5 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.125rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.125rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-x-1 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-x-10 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(2.5rem * var(--tw-space-x-reverse));\n  margin-left: calc(2.5rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-x-2 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-x-3 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.75rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-x-4 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-x-6 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(1.5rem * var(--tw-space-x-reverse));\n  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-x-8 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(2rem * var(--tw-space-x-reverse));\n  margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-y-1 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\n}\n.space-y-2 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\n}\n.space-y-3 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\n}\n.space-y-4 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\n}\n.space-y-5 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));\n}\n.space-y-6 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\n}\n.space-y-8 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\n}\n.divide-y > :not([hidden]) ~ :not([hidden]) {\n  --tw-divide-y-reverse: 0;\n  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));\n  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));\n}\n.divide-y-0 > :not([hidden]) ~ :not([hidden]) {\n  --tw-divide-y-reverse: 0;\n  border-top-width: calc(0px * calc(1 - var(--tw-divide-y-reverse)));\n  border-bottom-width: calc(0px * var(--tw-divide-y-reverse));\n}\n.divide-gray-200 > :not([hidden]) ~ :not([hidden]) {\n  --tw-divide-opacity: 1;\n  border-color: rgb(229 231 235 / var(--tw-divide-opacity, 1));\n}\n.overflow-auto {\n  overflow: auto;\n}\n.overflow-hidden {\n  overflow: hidden;\n}\n.overflow-x-auto {\n  overflow-x: auto;\n}\n.overflow-y-auto {\n  overflow-y: auto;\n}\n.truncate {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.whitespace-nowrap {\n  white-space: nowrap;\n}\n.whitespace-pre-wrap {\n  white-space: pre-wrap;\n}\n.rounded {\n  border-radius: 0.25rem;\n}\n.rounded-2xl {\n  border-radius: 1rem;\n}\n.rounded-full {\n  border-radius: 9999px;\n}\n.rounded-lg {\n  border-radius: 0.5rem;\n}\n.rounded-md {\n  border-radius: 0.375rem;\n}\n.rounded-xl {\n  border-radius: 0.75rem;\n}\n.rounded-b-lg {\n  border-bottom-right-radius: 0.5rem;\n  border-bottom-left-radius: 0.5rem;\n}\n.rounded-l-lg {\n  border-top-left-radius: 0.5rem;\n  border-bottom-left-radius: 0.5rem;\n}\n.rounded-t-lg {\n  border-top-left-radius: 0.5rem;\n  border-top-right-radius: 0.5rem;\n}\n.rounded-t-none {\n  border-top-left-radius: 0px;\n  border-top-right-radius: 0px;\n}\n.border {\n  border-width: 1px;\n}\n.border-0 {\n  border-width: 0px;\n}\n.border-2 {\n  border-width: 2px;\n}\n.border-4 {\n  border-width: 4px;\n}\n.border-b {\n  border-bottom-width: 1px;\n}\n.border-b-0 {\n  border-bottom-width: 0px;\n}\n.border-b-2 {\n  border-bottom-width: 2px;\n}\n.border-l-4 {\n  border-left-width: 4px;\n}\n.border-r {\n  border-right-width: 1px;\n}\n.border-r-0 {\n  border-right-width: 0px;\n}\n.border-r-2 {\n  border-right-width: 2px;\n}\n.border-t {\n  border-top-width: 1px;\n}\n.border-t-0 {\n  border-top-width: 0px;\n}\n.border-blue-200 {\n  --tw-border-opacity: 1;\n  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));\n}\n.border-blue-400 {\n  --tw-border-opacity: 1;\n  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));\n}\n.border-blue-500 {\n  --tw-border-opacity: 1;\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\n}\n.border-blue-600 {\n  --tw-border-opacity: 1;\n  border-color: rgb(37 99 235 / var(--tw-border-opacity, 1));\n}\n.border-gray-100 {\n  --tw-border-opacity: 1;\n  border-color: rgb(243 244 246 / var(--tw-border-opacity, 1));\n}\n.border-gray-200 {\n  --tw-border-opacity: 1;\n  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));\n}\n.border-gray-300 {\n  --tw-border-opacity: 1;\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\n}\n.border-gray-600 {\n  --tw-border-opacity: 1;\n  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));\n}\n.border-gray-700 {\n  --tw-border-opacity: 1;\n  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));\n}\n.border-gray-800 {\n  --tw-border-opacity: 1;\n  border-color: rgb(31 41 55 / var(--tw-border-opacity, 1));\n}\n.border-green-200 {\n  --tw-border-opacity: 1;\n  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));\n}\n.border-green-300 {\n  --tw-border-opacity: 1;\n  border-color: rgb(134 239 172 / var(--tw-border-opacity, 1));\n}\n.border-green-600 {\n  --tw-border-opacity: 1;\n  border-color: rgb(22 163 74 / var(--tw-border-opacity, 1));\n}\n.border-green-700 {\n  --tw-border-opacity: 1;\n  border-color: rgb(21 128 61 / var(--tw-border-opacity, 1));\n}\n.border-primary-500 {\n  --tw-border-opacity: 1;\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\n}\n.border-primary-600 {\n  --tw-border-opacity: 1;\n  border-color: rgb(37 99 235 / var(--tw-border-opacity, 1));\n}\n.border-primary-700 {\n  --tw-border-opacity: 1;\n  border-color: rgb(29 78 216 / var(--tw-border-opacity, 1));\n}\n.border-red-200 {\n  --tw-border-opacity: 1;\n  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));\n}\n.border-red-300 {\n  --tw-border-opacity: 1;\n  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));\n}\n.border-red-600 {\n  --tw-border-opacity: 1;\n  border-color: rgb(220 38 38 / var(--tw-border-opacity, 1));\n}\n.border-red-700 {\n  --tw-border-opacity: 1;\n  border-color: rgb(185 28 28 / var(--tw-border-opacity, 1));\n}\n.border-transparent {\n  border-color: transparent;\n}\n.border-white {\n  --tw-border-opacity: 1;\n  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\n}\n.border-white\\/20 {\n  border-color: rgb(255 255 255 / 0.2);\n}\n.border-yellow-200 {\n  --tw-border-opacity: 1;\n  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));\n}\n.border-yellow-600 {\n  --tw-border-opacity: 1;\n  border-color: rgb(202 138 4 / var(--tw-border-opacity, 1));\n}\n.border-yellow-700 {\n  --tw-border-opacity: 1;\n  border-color: rgb(161 98 7 / var(--tw-border-opacity, 1));\n}\n.border-b-gray-200 {\n  --tw-border-opacity: 1;\n  border-bottom-color: rgb(229 231 235 / var(--tw-border-opacity, 1));\n}\n.border-b-white {\n  --tw-border-opacity: 1;\n  border-bottom-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\n}\n.border-r-white {\n  --tw-border-opacity: 1;\n  border-right-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\n}\n.border-t-gray-900 {\n  --tw-border-opacity: 1;\n  border-top-color: rgb(17 24 39 / var(--tw-border-opacity, 1));\n}\n.bg-black {\n  --tw-bg-opacity: 1;\n  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));\n}\n.bg-black\\/10 {\n  background-color: rgb(0 0 0 / 0.1);\n}\n.bg-blue-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));\n}\n.bg-blue-200 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(191 219 254 / var(--tw-bg-opacity, 1));\n}\n.bg-blue-300 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(147 197 253 / var(--tw-bg-opacity, 1));\n}\n.bg-blue-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));\n}\n.bg-blue-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));\n}\n.bg-blue-600 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\n}\n.bg-blue-700 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));\n}\n.bg-gray-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\n}\n.bg-gray-200 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\n}\n.bg-gray-300 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));\n}\n.bg-gray-400 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));\n}\n.bg-gray-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\n}\n.bg-gray-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));\n}\n.bg-gray-600 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));\n}\n.bg-gray-900 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(17 24 39 / var(--tw-bg-opacity, 1));\n}\n.bg-green-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));\n}\n.bg-green-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));\n}\n.bg-green-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));\n}\n.bg-green-600 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));\n}\n.bg-indigo-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(224 231 255 / var(--tw-bg-opacity, 1));\n}\n.bg-indigo-300 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(165 180 252 / var(--tw-bg-opacity, 1));\n}\n.bg-indigo-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(99 102 241 / var(--tw-bg-opacity, 1));\n}\n.bg-orange-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));\n}\n.bg-pink-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(252 231 243 / var(--tw-bg-opacity, 1));\n}\n.bg-pink-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(236 72 153 / var(--tw-bg-opacity, 1));\n}\n.bg-primary-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));\n}\n.bg-primary-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));\n}\n.bg-primary-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));\n}\n.bg-primary-600 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\n}\n.bg-purple-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));\n}\n.bg-purple-200 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(233 213 255 / var(--tw-bg-opacity, 1));\n}\n.bg-purple-300 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(216 180 254 / var(--tw-bg-opacity, 1));\n}\n.bg-purple-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));\n}\n.bg-purple-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));\n}\n.bg-red-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));\n}\n.bg-red-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));\n}\n.bg-red-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));\n}\n.bg-red-600 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));\n}\n.bg-transparent {\n  background-color: transparent;\n}\n.bg-white {\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\n}\n.bg-white\\/10 {\n  background-color: rgb(255 255 255 / 0.1);\n}\n.bg-white\\/5 {\n  background-color: rgb(255 255 255 / 0.05);\n}\n.bg-white\\/80 {\n  background-color: rgb(255 255 255 / 0.8);\n}\n.bg-yellow-100 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));\n}\n.bg-yellow-50 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));\n}\n.bg-yellow-500 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));\n}\n.bg-yellow-600 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(202 138 4 / var(--tw-bg-opacity, 1));\n}\n.bg-opacity-10 {\n  --tw-bg-opacity: 0.1;\n}\n.bg-opacity-50 {\n  --tw-bg-opacity: 0.5;\n}\n.bg-opacity-75 {\n  --tw-bg-opacity: 0.75;\n}\n.bg-gradient-to-b {\n  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));\n}\n.bg-gradient-to-br {\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\n}\n.bg-gradient-to-l {\n  background-image: linear-gradient(to left, var(--tw-gradient-stops));\n}\n.bg-gradient-to-r {\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\n}\n.from-blue-400 {\n  --tw-gradient-from: #60a5fa var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(96 165 250 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-blue-50 {\n  --tw-gradient-from: #eff6ff var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-blue-500 {\n  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-blue-600 {\n  --tw-gradient-from: #2563eb var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-gray-400 {\n  --tw-gradient-from: #9ca3af var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(156 163 175 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-gray-50 {\n  --tw-gradient-from: #f9fafb var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(249 250 251 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-gray-50\\/50 {\n  --tw-gradient-from: rgb(249 250 251 / 0.5) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(249 250 251 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-primary-50 {\n  --tw-gradient-from: #eff6ff var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-primary-500 {\n  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-primary-600 {\n  --tw-gradient-from: #2563eb var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-white {\n  --tw-gradient-from: #fff var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-yellow-400 {\n  --tw-gradient-from: #facc15 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(250 204 21 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.via-indigo-50 {\n  --tw-gradient-to: rgb(238 242 255 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), #eef2ff var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.via-purple-600 {\n  --tw-gradient-to: rgb(147 51 234 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), #9333ea var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.via-white {\n  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), #fff var(--tw-gradient-via-position), var(--tw-gradient-to);\n}\n.to-blue-600 {\n  --tw-gradient-to: #2563eb var(--tw-gradient-to-position);\n}\n.to-gray-600 {\n  --tw-gradient-to: #4b5563 var(--tw-gradient-to-position);\n}\n.to-indigo-600 {\n  --tw-gradient-to: #4f46e5 var(--tw-gradient-to-position);\n}\n.to-orange-400 {\n  --tw-gradient-to: #fb923c var(--tw-gradient-to-position);\n}\n.to-primary-100 {\n  --tw-gradient-to: #dbeafe var(--tw-gradient-to-position);\n}\n.to-primary-700 {\n  --tw-gradient-to: #1d4ed8 var(--tw-gradient-to-position);\n}\n.to-purple-50 {\n  --tw-gradient-to: #faf5ff var(--tw-gradient-to-position);\n}\n.to-purple-600 {\n  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);\n}\n.to-secondary-50 {\n  --tw-gradient-to: #f8fafc var(--tw-gradient-to-position);\n}\n.to-transparent {\n  --tw-gradient-to: transparent var(--tw-gradient-to-position);\n}\n.to-white {\n  --tw-gradient-to: #fff var(--tw-gradient-to-position);\n}\n.to-yellow-600 {\n  --tw-gradient-to: #ca8a04 var(--tw-gradient-to-position);\n}\n.bg-clip-text {\n  -webkit-background-clip: text;\n          background-clip: text;\n}\n.fill-current {\n  fill: currentColor;\n}\n.stroke-blue-600 {\n  stroke: #2563eb;\n}\n.stroke-green-600 {\n  stroke: #16a34a;\n}\n.stroke-primary-600 {\n  stroke: #2563eb;\n}\n.stroke-red-600 {\n  stroke: #dc2626;\n}\n.stroke-yellow-600 {\n  stroke: #ca8a04;\n}\n.object-cover {\n  object-fit: cover;\n}\n.p-0 {\n  padding: 0px;\n}\n.p-0\\.5 {\n  padding: 0.125rem;\n}\n.p-1 {\n  padding: 0.25rem;\n}\n.p-1\\.5 {\n  padding: 0.375rem;\n}\n.p-12 {\n  padding: 3rem;\n}\n.p-2 {\n  padding: 0.5rem;\n}\n.p-3 {\n  padding: 0.75rem;\n}\n.p-4 {\n  padding: 1rem;\n}\n.p-6 {\n  padding: 1.5rem;\n}\n.p-8 {\n  padding: 2rem;\n}\n.px-0 {\n  padding-left: 0px;\n  padding-right: 0px;\n}\n.px-1 {\n  padding-left: 0.25rem;\n  padding-right: 0.25rem;\n}\n.px-2 {\n  padding-left: 0.5rem;\n  padding-right: 0.5rem;\n}\n.px-2\\.5 {\n  padding-left: 0.625rem;\n  padding-right: 0.625rem;\n}\n.px-3 {\n  padding-left: 0.75rem;\n  padding-right: 0.75rem;\n}\n.px-4 {\n  padding-left: 1rem;\n  padding-right: 1rem;\n}\n.px-5 {\n  padding-left: 1.25rem;\n  padding-right: 1.25rem;\n}\n.px-6 {\n  padding-left: 1.5rem;\n  padding-right: 1.5rem;\n}\n.px-8 {\n  padding-left: 2rem;\n  padding-right: 2rem;\n}\n.py-0\\.5 {\n  padding-top: 0.125rem;\n  padding-bottom: 0.125rem;\n}\n.py-1 {\n  padding-top: 0.25rem;\n  padding-bottom: 0.25rem;\n}\n.py-1\\.5 {\n  padding-top: 0.375rem;\n  padding-bottom: 0.375rem;\n}\n.py-12 {\n  padding-top: 3rem;\n  padding-bottom: 3rem;\n}\n.py-16 {\n  padding-top: 4rem;\n  padding-bottom: 4rem;\n}\n.py-2 {\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n}\n.py-2\\.5 {\n  padding-top: 0.625rem;\n  padding-bottom: 0.625rem;\n}\n.py-20 {\n  padding-top: 5rem;\n  padding-bottom: 5rem;\n}\n.py-24 {\n  padding-top: 6rem;\n  padding-bottom: 6rem;\n}\n.py-3 {\n  padding-top: 0.75rem;\n  padding-bottom: 0.75rem;\n}\n.py-4 {\n  padding-top: 1rem;\n  padding-bottom: 1rem;\n}\n.py-6 {\n  padding-top: 1.5rem;\n  padding-bottom: 1.5rem;\n}\n.py-8 {\n  padding-top: 2rem;\n  padding-bottom: 2rem;\n}\n.pb-2 {\n  padding-bottom: 0.5rem;\n}\n.pb-3 {\n  padding-bottom: 0.75rem;\n}\n.pb-32 {\n  padding-bottom: 8rem;\n}\n.pb-4 {\n  padding-bottom: 1rem;\n}\n.pl-10 {\n  padding-left: 2.5rem;\n}\n.pl-3 {\n  padding-left: 0.75rem;\n}\n.pr-10 {\n  padding-right: 2.5rem;\n}\n.pr-2 {\n  padding-right: 0.5rem;\n}\n.pr-3 {\n  padding-right: 0.75rem;\n}\n.pr-4 {\n  padding-right: 1rem;\n}\n.pt-2 {\n  padding-top: 0.5rem;\n}\n.pt-20 {\n  padding-top: 5rem;\n}\n.pt-4 {\n  padding-top: 1rem;\n}\n.pt-8 {\n  padding-top: 2rem;\n}\n.text-left {\n  text-align: left;\n}\n.text-center {\n  text-align: center;\n}\n.text-right {\n  text-align: right;\n}\n.font-mono {\n  font-family: JetBrains Mono, Fira Code, monospace;\n}\n.text-2xl {\n  font-size: 1.5rem;\n  line-height: 2rem;\n}\n.text-3xl {\n  font-size: 1.875rem;\n  line-height: 2.25rem;\n}\n.text-4xl {\n  font-size: 2.25rem;\n  line-height: 2.5rem;\n}\n.text-5xl {\n  font-size: 3rem;\n  line-height: 1;\n}\n.text-9xl {\n  font-size: 8rem;\n  line-height: 1;\n}\n.text-base {\n  font-size: 1rem;\n  line-height: 1.5rem;\n}\n.text-lg {\n  font-size: 1.125rem;\n  line-height: 1.75rem;\n}\n.text-sm {\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\n.text-xl {\n  font-size: 1.25rem;\n  line-height: 1.75rem;\n}\n.text-xs {\n  font-size: 0.75rem;\n  line-height: 1rem;\n}\n.font-bold {\n  font-weight: 700;\n}\n.font-extrabold {\n  font-weight: 800;\n}\n.font-medium {\n  font-weight: 500;\n}\n.font-normal {\n  font-weight: 400;\n}\n.font-semibold {\n  font-weight: 600;\n}\n.uppercase {\n  text-transform: uppercase;\n}\n.capitalize {\n  text-transform: capitalize;\n}\n.italic {\n  font-style: italic;\n}\n.leading-5 {\n  line-height: 1.25rem;\n}\n.leading-relaxed {\n  line-height: 1.625;\n}\n.leading-tight {\n  line-height: 1.25;\n}\n.tracking-wider {\n  letter-spacing: 0.05em;\n}\n.text-blue-100 {\n  --tw-text-opacity: 1;\n  color: rgb(219 234 254 / var(--tw-text-opacity, 1));\n}\n.text-blue-200 {\n  --tw-text-opacity: 1;\n  color: rgb(191 219 254 / var(--tw-text-opacity, 1));\n}\n.text-blue-400 {\n  --tw-text-opacity: 1;\n  color: rgb(96 165 250 / var(--tw-text-opacity, 1));\n}\n.text-blue-50 {\n  --tw-text-opacity: 1;\n  color: rgb(239 246 255 / var(--tw-text-opacity, 1));\n}\n.text-blue-600 {\n  --tw-text-opacity: 1;\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\n}\n.text-blue-700 {\n  --tw-text-opacity: 1;\n  color: rgb(29 78 216 / var(--tw-text-opacity, 1));\n}\n.text-blue-800 {\n  --tw-text-opacity: 1;\n  color: rgb(30 64 175 / var(--tw-text-opacity, 1));\n}\n.text-current {\n  color: currentColor;\n}\n.text-gray-200 {\n  --tw-text-opacity: 1;\n  color: rgb(229 231 235 / var(--tw-text-opacity, 1));\n}\n.text-gray-300 {\n  --tw-text-opacity: 1;\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\n}\n.text-gray-400 {\n  --tw-text-opacity: 1;\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\n}\n.text-gray-50 {\n  --tw-text-opacity: 1;\n  color: rgb(249 250 251 / var(--tw-text-opacity, 1));\n}\n.text-gray-500 {\n  --tw-text-opacity: 1;\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\n}\n.text-gray-600 {\n  --tw-text-opacity: 1;\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\n}\n.text-gray-700 {\n  --tw-text-opacity: 1;\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\n}\n.text-gray-800 {\n  --tw-text-opacity: 1;\n  color: rgb(31 41 55 / var(--tw-text-opacity, 1));\n}\n.text-gray-900 {\n  --tw-text-opacity: 1;\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\n}\n.text-green-400 {\n  --tw-text-opacity: 1;\n  color: rgb(74 222 128 / var(--tw-text-opacity, 1));\n}\n.text-green-50 {\n  --tw-text-opacity: 1;\n  color: rgb(240 253 244 / var(--tw-text-opacity, 1));\n}\n.text-green-500 {\n  --tw-text-opacity: 1;\n  color: rgb(34 197 94 / var(--tw-text-opacity, 1));\n}\n.text-green-600 {\n  --tw-text-opacity: 1;\n  color: rgb(22 163 74 / var(--tw-text-opacity, 1));\n}\n.text-green-700 {\n  --tw-text-opacity: 1;\n  color: rgb(21 128 61 / var(--tw-text-opacity, 1));\n}\n.text-green-800 {\n  --tw-text-opacity: 1;\n  color: rgb(22 101 52 / var(--tw-text-opacity, 1));\n}\n.text-green-900 {\n  --tw-text-opacity: 1;\n  color: rgb(20 83 45 / var(--tw-text-opacity, 1));\n}\n.text-indigo-600 {\n  --tw-text-opacity: 1;\n  color: rgb(79 70 229 / var(--tw-text-opacity, 1));\n}\n.text-indigo-800 {\n  --tw-text-opacity: 1;\n  color: rgb(55 48 163 / var(--tw-text-opacity, 1));\n}\n.text-orange-600 {\n  --tw-text-opacity: 1;\n  color: rgb(234 88 12 / var(--tw-text-opacity, 1));\n}\n.text-pink-800 {\n  --tw-text-opacity: 1;\n  color: rgb(157 23 77 / var(--tw-text-opacity, 1));\n}\n.text-primary-50 {\n  --tw-text-opacity: 1;\n  color: rgb(239 246 255 / var(--tw-text-opacity, 1));\n}\n.text-primary-600 {\n  --tw-text-opacity: 1;\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\n}\n.text-primary-700 {\n  --tw-text-opacity: 1;\n  color: rgb(29 78 216 / var(--tw-text-opacity, 1));\n}\n.text-purple-600 {\n  --tw-text-opacity: 1;\n  color: rgb(147 51 234 / var(--tw-text-opacity, 1));\n}\n.text-purple-800 {\n  --tw-text-opacity: 1;\n  color: rgb(107 33 168 / var(--tw-text-opacity, 1));\n}\n.text-red-200 {\n  --tw-text-opacity: 1;\n  color: rgb(254 202 202 / var(--tw-text-opacity, 1));\n}\n.text-red-400 {\n  --tw-text-opacity: 1;\n  color: rgb(248 113 113 / var(--tw-text-opacity, 1));\n}\n.text-red-50 {\n  --tw-text-opacity: 1;\n  color: rgb(254 242 242 / var(--tw-text-opacity, 1));\n}\n.text-red-500 {\n  --tw-text-opacity: 1;\n  color: rgb(239 68 68 / var(--tw-text-opacity, 1));\n}\n.text-red-600 {\n  --tw-text-opacity: 1;\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\n}\n.text-red-700 {\n  --tw-text-opacity: 1;\n  color: rgb(185 28 28 / var(--tw-text-opacity, 1));\n}\n.text-red-800 {\n  --tw-text-opacity: 1;\n  color: rgb(153 27 27 / var(--tw-text-opacity, 1));\n}\n.text-red-900 {\n  --tw-text-opacity: 1;\n  color: rgb(127 29 29 / var(--tw-text-opacity, 1));\n}\n.text-secondary-600 {\n  --tw-text-opacity: 1;\n  color: rgb(71 85 105 / var(--tw-text-opacity, 1));\n}\n.text-transparent {\n  color: transparent;\n}\n.text-white {\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\n.text-yellow-400 {\n  --tw-text-opacity: 1;\n  color: rgb(250 204 21 / var(--tw-text-opacity, 1));\n}\n.text-yellow-50 {\n  --tw-text-opacity: 1;\n  color: rgb(254 252 232 / var(--tw-text-opacity, 1));\n}\n.text-yellow-600 {\n  --tw-text-opacity: 1;\n  color: rgb(202 138 4 / var(--tw-text-opacity, 1));\n}\n.text-yellow-700 {\n  --tw-text-opacity: 1;\n  color: rgb(161 98 7 / var(--tw-text-opacity, 1));\n}\n.text-yellow-800 {\n  --tw-text-opacity: 1;\n  color: rgb(133 77 14 / var(--tw-text-opacity, 1));\n}\n.underline {\n  -webkit-text-decoration-line: underline;\n          text-decoration-line: underline;\n}\n.placeholder-gray-500::placeholder {\n  --tw-placeholder-opacity: 1;\n  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));\n}\n.opacity-0 {\n  opacity: 0;\n}\n.opacity-100 {\n  opacity: 1;\n}\n.opacity-20 {\n  opacity: 0.2;\n}\n.opacity-25 {\n  opacity: 0.25;\n}\n.opacity-30 {\n  opacity: 0.3;\n}\n.opacity-50 {\n  opacity: 0.5;\n}\n.opacity-70 {\n  opacity: 0.7;\n}\n.opacity-75 {\n  opacity: 0.75;\n}\n.opacity-90 {\n  opacity: 0.9;\n}\n.mix-blend-multiply {\n  mix-blend-mode: multiply;\n}\n.shadow {\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-2xl {\n  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\n  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-lg {\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-sm {\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-xl {\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.outline {\n  outline-style: solid;\n}\n.ring-1 {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.ring-2 {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.ring-inset {\n  --tw-ring-inset: inset;\n}\n.ring-black {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(0 0 0 / var(--tw-ring-opacity, 1));\n}\n.ring-blue-500 {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));\n}\n.ring-blue-600\\/20 {\n  --tw-ring-color: rgb(37 99 235 / 0.2);\n}\n.ring-gray-600\\/20 {\n  --tw-ring-color: rgb(75 85 99 / 0.2);\n}\n.ring-green-600\\/20 {\n  --tw-ring-color: rgb(22 163 74 / 0.2);\n}\n.ring-primary-600\\/20 {\n  --tw-ring-color: rgb(37 99 235 / 0.2);\n}\n.ring-red-600\\/20 {\n  --tw-ring-color: rgb(220 38 38 / 0.2);\n}\n.ring-yellow-600\\/20 {\n  --tw-ring-color: rgb(202 138 4 / 0.2);\n}\n.ring-opacity-20 {\n  --tw-ring-opacity: 0.2;\n}\n.ring-opacity-5 {\n  --tw-ring-opacity: 0.05;\n}\n.blur-3xl {\n  --tw-blur: blur(64px);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.blur-xl {\n  --tw-blur: blur(24px);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.filter {\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.backdrop-blur-sm {\n  --tw-backdrop-blur: blur(4px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n.transition-all {\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-colors {\n  transition-property: color, background-color, border-color, fill, stroke, -webkit-text-decoration-color;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, -webkit-text-decoration-color;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-opacity {\n  transition-property: opacity;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-shadow {\n  transition-property: box-shadow;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-transform {\n  transition-property: transform;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.duration-150 {\n  transition-duration: 150ms;\n}\n.duration-200 {\n  transition-duration: 200ms;\n}\n.duration-300 {\n  transition-duration: 300ms;\n}\n.duration-500 {\n  transition-duration: 500ms;\n}\n.ease-in-out {\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n}\n.ease-linear {\n  transition-timing-function: linear;\n}\n.ease-out {\n  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);\n}\n/* Animații personalizate */\n.animate-scale-in {\n    animation: scaleIn 0.2s ease-out;\n  }\n/* Gradient backgrounds */\n/* Text gradients */\n/* Shadows personalizate */\n/* Responsive utilities */\n/* Print utilities */\n\n/* Variabile CSS pentru teme și culori */\n:root {\n  /* Culori principale */\n  --color-primary-50: #eff6ff;\n  --color-primary-100: #dbeafe;\n  --color-primary-200: #bfdbfe;\n  --color-primary-300: #93c5fd;\n  --color-primary-400: #60a5fa;\n  --color-primary-500: #3b82f6;\n  --color-primary-600: #2563eb;\n  --color-primary-700: #1d4ed8;\n  --color-primary-800: #1e40af;\n  --color-primary-900: #1e3a8a;\n\n  /* Shadows */\n  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n\n  /* Transitions */\n  --transition-fast: 150ms ease-in-out;\n  --transition-normal: 250ms ease-in-out;\n  --transition-slow: 350ms ease-in-out;\n\n  /* Border radius */\n  --radius-sm: 0.25rem;\n  --radius-md: 0.375rem;\n  --radius-lg: 0.5rem;\n  --radius-xl: 0.75rem;\n}\n\n/* Reset și stiluri de bază */\n\n/* Componente personalizate */\n\n/* Utilități personalizate */\n\n/* Keyframes pentru animații */\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n@keyframes slideUp {\n  from {\n    opacity: 0;\n    transform: translateY(10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes slideDown {\n  from {\n    opacity: 0;\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes scaleIn {\n  from {\n    opacity: 0;\n    transform: scale(0.95);\n  }\n  to {\n    opacity: 1;\n    transform: scale(1);\n  }\n}\n\n@keyframes bounceSoft {\n  0%, 20%, 53%, 80%, 100% {\n    transform: translate3d(0, 0, 0);\n  }\n  40%, 43% {\n    transform: translate3d(0, -8px, 0);\n  }\n  70% {\n    transform: translate3d(0, -4px, 0);\n  }\n  90% {\n    transform: translate3d(0, -2px, 0);\n  }\n}\n\n@keyframes pulseSoft {\n  0%, 100% {\n    opacity: 1;\n  }\n  50% {\n    opacity: 0.7;\n  }\n}\n\n@keyframes fadeInUp {\n  from {\n    opacity: 0;\n    transform: translateY(30px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n/* Animații pentru landing page */\n.animate-fade-in-up {\n  animation: fadeInUp 0.8s ease-out forwards;\n  opacity: 0;\n}\n\n.animation-delay-200 {\n  animation-delay: 0.2s;\n}\n\n.animation-delay-400 {\n  animation-delay: 0.4s;\n}\n\n.animation-delay-600 {\n  animation-delay: 0.6s;\n}\n\n.animation-delay-800 {\n  animation-delay: 0.8s;\n}\n\n.animation-delay-1000 {\n  animation-delay: 1s;\n}\n\n.animation-delay-2000 {\n  animation-delay: 2s;\n}\n\n.animation-delay-4000 {\n  animation-delay: 4s;\n}\n\n/* Efecte de hover îmbunătățite */\n.hover\\:shadow-3xl:hover {\n  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);\n}\n\n/* Backdrop blur pentru browsere care nu suportă */\n.backdrop-blur-sm {\n  -webkit-backdrop-filter: blur(4px);\n          backdrop-filter: blur(4px);\n}\n\n@supports not ((-webkit-backdrop-filter: blur(4px)) or (backdrop-filter: blur(4px))) {\n  .backdrop-blur-sm {\n    background-color: rgba(255, 255, 255, 0.9);\n  }\n}\n\n/* Stiluri pentru accesibilitate */\n@media (prefers-reduced-motion: reduce) {\n  *,\n  *::before,\n  *::after {\n    animation-duration: 0.01ms !important;\n    animation-iteration-count: 1 !important;\n    transition-duration: 0.01ms !important;\n    scroll-behavior: auto !important;\n  }\n}\n\n/* Stiluri pentru high contrast */\n@media (prefers-contrast: high) {\n  .btn {\n    border-width: 2px;\n  }\n\n  .input {\n    border-width: 2px;\n  }\n\n  .card {\n    border-width: 2px;\n  }\n}\n.placeholder\\:text-gray-400::placeholder {\n  --tw-text-opacity: 1;\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\n}\n.after\\:absolute::after {\n  content: var(--tw-content);\n  position: absolute;\n}\n.after\\:bottom-0::after {\n  content: var(--tw-content);\n  bottom: 0px;\n}\n.after\\:bottom-full::after {\n  content: var(--tw-content);\n  bottom: 100%;\n}\n.after\\:left-0::after {\n  content: var(--tw-content);\n  left: 0px;\n}\n.after\\:left-1\\/2::after {\n  content: var(--tw-content);\n  left: 50%;\n}\n.after\\:left-\\[2px\\]::after {\n  content: var(--tw-content);\n  left: 2px;\n}\n.after\\:left-full::after {\n  content: var(--tw-content);\n  left: 100%;\n}\n.after\\:right-0::after {\n  content: var(--tw-content);\n  right: 0px;\n}\n.after\\:right-full::after {\n  content: var(--tw-content);\n  right: 100%;\n}\n.after\\:top-0::after {\n  content: var(--tw-content);\n  top: 0px;\n}\n.after\\:top-1\\/2::after {\n  content: var(--tw-content);\n  top: 50%;\n}\n.after\\:top-\\[2px\\]::after {\n  content: var(--tw-content);\n  top: 2px;\n}\n.after\\:top-full::after {\n  content: var(--tw-content);\n  top: 100%;\n}\n.after\\:h-0\\.5::after {\n  content: var(--tw-content);\n  height: 0.125rem;\n}\n.after\\:h-5::after {\n  content: var(--tw-content);\n  height: 1.25rem;\n}\n.after\\:w-0\\.5::after {\n  content: var(--tw-content);\n  width: 0.125rem;\n}\n.after\\:w-5::after {\n  content: var(--tw-content);\n  width: 1.25rem;\n}\n.after\\:-translate-x-1\\/2::after {\n  content: var(--tw-content);\n  --tw-translate-x: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.after\\:-translate-y-1\\/2::after {\n  content: var(--tw-content);\n  --tw-translate-y: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.after\\:transform::after {\n  content: var(--tw-content);\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.after\\:rounded-full::after {\n  content: var(--tw-content);\n  border-radius: 9999px;\n}\n.after\\:border::after {\n  content: var(--tw-content);\n  border-width: 1px;\n}\n.after\\:border-4::after {\n  content: var(--tw-content);\n  border-width: 4px;\n}\n.after\\:border-gray-300::after {\n  content: var(--tw-content);\n  --tw-border-opacity: 1;\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\n}\n.after\\:border-transparent::after {\n  content: var(--tw-content);\n  border-color: transparent;\n}\n.after\\:border-b-gray-900::after {\n  content: var(--tw-content);\n  --tw-border-opacity: 1;\n  border-bottom-color: rgb(17 24 39 / var(--tw-border-opacity, 1));\n}\n.after\\:border-b-green-600::after {\n  content: var(--tw-content);\n  --tw-border-opacity: 1;\n  border-bottom-color: rgb(22 163 74 / var(--tw-border-opacity, 1));\n}\n.after\\:border-b-primary-600::after {\n  content: var(--tw-content);\n  --tw-border-opacity: 1;\n  border-bottom-color: rgb(37 99 235 / var(--tw-border-opacity, 1));\n}\n.after\\:border-b-red-600::after {\n  content: var(--tw-content);\n  --tw-border-opacity: 1;\n  border-bottom-color: rgb(220 38 38 / var(--tw-border-opacity, 1));\n}\n.after\\:border-b-white::after {\n  content: var(--tw-content);\n  --tw-border-opacity: 1;\n  border-bottom-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\n}\n.after\\:border-b-yellow-600::after {\n  content: var(--tw-content);\n  --tw-border-opacity: 1;\n  border-bottom-color: rgb(202 138 4 / var(--tw-border-opacity, 1));\n}\n.after\\:border-l-gray-900::after {\n  content: var(--tw-content);\n  --tw-border-opacity: 1;\n  border-left-color: rgb(17 24 39 / var(--tw-border-opacity, 1));\n}\n.after\\:border-l-green-600::after {\n  content: var(--tw-content);\n  --tw-border-opacity: 1;\n  border-left-color: rgb(22 163 74 / var(--tw-border-opacity, 1));\n}\n.after\\:border-l-primary-600::after {\n  content: var(--tw-content);\n  --tw-border-opacity: 1;\n  border-left-color: rgb(37 99 235 / var(--tw-border-opacity, 1));\n}\n.after\\:border-l-red-600::after {\n  content: var(--tw-content);\n  --tw-border-opacity: 1;\n  border-left-color: rgb(220 38 38 / var(--tw-border-opacity, 1));\n}\n.after\\:border-l-white::after {\n  content: var(--tw-content);\n  --tw-border-opacity: 1;\n  border-left-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\n}\n.after\\:border-l-yellow-600::after {\n  content: var(--tw-content);\n  --tw-border-opacity: 1;\n  border-left-color: rgb(202 138 4 / var(--tw-border-opacity, 1));\n}\n.after\\:border-r-gray-900::after {\n  content: var(--tw-content);\n  --tw-border-opacity: 1;\n  border-right-color: rgb(17 24 39 / var(--tw-border-opacity, 1));\n}\n.after\\:border-r-green-600::after {\n  content: var(--tw-content);\n  --tw-border-opacity: 1;\n  border-right-color: rgb(22 163 74 / var(--tw-border-opacity, 1));\n}\n.after\\:border-r-primary-600::after {\n  content: var(--tw-content);\n  --tw-border-opacity: 1;\n  border-right-color: rgb(37 99 235 / var(--tw-border-opacity, 1));\n}\n.after\\:border-r-red-600::after {\n  content: var(--tw-content);\n  --tw-border-opacity: 1;\n  border-right-color: rgb(220 38 38 / var(--tw-border-opacity, 1));\n}\n.after\\:border-r-white::after {\n  content: var(--tw-content);\n  --tw-border-opacity: 1;\n  border-right-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\n}\n.after\\:border-r-yellow-600::after {\n  content: var(--tw-content);\n  --tw-border-opacity: 1;\n  border-right-color: rgb(202 138 4 / var(--tw-border-opacity, 1));\n}\n.after\\:border-t-gray-900::after {\n  content: var(--tw-content);\n  --tw-border-opacity: 1;\n  border-top-color: rgb(17 24 39 / var(--tw-border-opacity, 1));\n}\n.after\\:border-t-green-600::after {\n  content: var(--tw-content);\n  --tw-border-opacity: 1;\n  border-top-color: rgb(22 163 74 / var(--tw-border-opacity, 1));\n}\n.after\\:border-t-primary-600::after {\n  content: var(--tw-content);\n  --tw-border-opacity: 1;\n  border-top-color: rgb(37 99 235 / var(--tw-border-opacity, 1));\n}\n.after\\:border-t-red-600::after {\n  content: var(--tw-content);\n  --tw-border-opacity: 1;\n  border-top-color: rgb(220 38 38 / var(--tw-border-opacity, 1));\n}\n.after\\:border-t-white::after {\n  content: var(--tw-content);\n  --tw-border-opacity: 1;\n  border-top-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\n}\n.after\\:border-t-yellow-600::after {\n  content: var(--tw-content);\n  --tw-border-opacity: 1;\n  border-top-color: rgb(202 138 4 / var(--tw-border-opacity, 1));\n}\n.after\\:bg-primary-500::after {\n  content: var(--tw-content);\n  --tw-bg-opacity: 1;\n  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));\n}\n.after\\:bg-white::after {\n  content: var(--tw-content);\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\n}\n.after\\:transition-all::after {\n  content: var(--tw-content);\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.after\\:content-\\[\\'\\'\\]::after {\n  --tw-content: '';\n  content: var(--tw-content);\n}\n.last\\:mb-0:last-child {\n  margin-bottom: 0px;\n}\n.last\\:border-b-0:last-child {\n  border-bottom-width: 0px;\n}\n.hover\\:-translate-y-1:hover {\n  --tw-translate-y: -0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.hover\\:-translate-y-2:hover {\n  --tw-translate-y: -0.5rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.hover\\:scale-105:hover {\n  --tw-scale-x: 1.05;\n  --tw-scale-y: 1.05;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.hover\\:scale-110:hover {\n  --tw-scale-x: 1.1;\n  --tw-scale-y: 1.1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.hover\\:scale-\\[1\\.02\\]:hover {\n  --tw-scale-x: 1.02;\n  --tw-scale-y: 1.02;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.hover\\:border-blue-300:hover {\n  --tw-border-opacity: 1;\n  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));\n}\n.hover\\:border-blue-400:hover {\n  --tw-border-opacity: 1;\n  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));\n}\n.hover\\:border-gray-300:hover {\n  --tw-border-opacity: 1;\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\n}\n.hover\\:border-gray-400:hover {\n  --tw-border-opacity: 1;\n  border-color: rgb(156 163 175 / var(--tw-border-opacity, 1));\n}\n.hover\\:border-gray-700:hover {\n  --tw-border-opacity: 1;\n  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));\n}\n.hover\\:border-green-700:hover {\n  --tw-border-opacity: 1;\n  border-color: rgb(21 128 61 / var(--tw-border-opacity, 1));\n}\n.hover\\:border-primary-700:hover {\n  --tw-border-opacity: 1;\n  border-color: rgb(29 78 216 / var(--tw-border-opacity, 1));\n}\n.hover\\:border-red-700:hover {\n  --tw-border-opacity: 1;\n  border-color: rgb(185 28 28 / var(--tw-border-opacity, 1));\n}\n.hover\\:border-yellow-700:hover {\n  --tw-border-opacity: 1;\n  border-color: rgb(161 98 7 / var(--tw-border-opacity, 1));\n}\n.hover\\:bg-black:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-blue-50:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-blue-700:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-blue-800:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(30 64 175 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-gray-100:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-gray-200:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-gray-300:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-gray-50:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-gray-500:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-gray-700:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-gray-800:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-green-700:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-primary-100:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-primary-200:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(191 219 254 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-primary-50:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-primary-600:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-primary-700:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-red-50:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-red-700:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(185 28 28 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-transparent:hover {\n  background-color: transparent;\n}\n.hover\\:bg-white:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-yellow-700:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(161 98 7 / var(--tw-bg-opacity, 1));\n}\n.hover\\:bg-opacity-10:hover {\n  --tw-bg-opacity: 0.1;\n}\n.hover\\:bg-opacity-50:hover {\n  --tw-bg-opacity: 0.5;\n}\n.hover\\:from-primary-700:hover {\n  --tw-gradient-from: #1d4ed8 var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(29 78 216 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.hover\\:to-primary-800:hover {\n  --tw-gradient-to: #1e40af var(--tw-gradient-to-position);\n}\n.hover\\:text-blue-600:hover {\n  --tw-text-opacity: 1;\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-blue-800:hover {\n  --tw-text-opacity: 1;\n  color: rgb(30 64 175 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-gray-600:hover {\n  --tw-text-opacity: 1;\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-gray-700:hover {\n  --tw-text-opacity: 1;\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-gray-800:hover {\n  --tw-text-opacity: 1;\n  color: rgb(31 41 55 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-gray-900:hover {\n  --tw-text-opacity: 1;\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-primary-500:hover {\n  --tw-text-opacity: 1;\n  color: rgb(59 130 246 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-primary-600:hover {\n  --tw-text-opacity: 1;\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-primary-700:hover {\n  --tw-text-opacity: 1;\n  color: rgb(29 78 216 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-primary-900:hover {\n  --tw-text-opacity: 1;\n  color: rgb(30 58 138 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-red-600:hover {\n  --tw-text-opacity: 1;\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-red-900:hover {\n  --tw-text-opacity: 1;\n  color: rgb(127 29 29 / var(--tw-text-opacity, 1));\n}\n.hover\\:text-white:hover {\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\n.hover\\:underline:hover {\n  -webkit-text-decoration-line: underline;\n          text-decoration-line: underline;\n}\n.hover\\:opacity-100:hover {\n  opacity: 1;\n}\n.hover\\:opacity-75:hover {\n  opacity: 0.75;\n}\n.hover\\:shadow-2xl:hover {\n  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\n  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.hover\\:shadow-lg:hover {\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.hover\\:shadow-md:hover {\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.hover\\:shadow-xl:hover {\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.focus\\:border-blue-500:focus {\n  --tw-border-opacity: 1;\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\n}\n.focus\\:border-green-500:focus {\n  --tw-border-opacity: 1;\n  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));\n}\n.focus\\:border-primary-500:focus {\n  --tw-border-opacity: 1;\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\n}\n.focus\\:border-red-500:focus {\n  --tw-border-opacity: 1;\n  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));\n}\n.focus\\:border-transparent:focus {\n  border-color: transparent;\n}\n.focus\\:bg-black:focus {\n  --tw-bg-opacity: 1;\n  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));\n}\n.focus\\:bg-gray-100:focus {\n  --tw-bg-opacity: 1;\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\n}\n.focus\\:bg-gray-50:focus {\n  --tw-bg-opacity: 1;\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\n}\n.focus\\:bg-opacity-10:focus {\n  --tw-bg-opacity: 0.1;\n}\n.focus\\:text-gray-900:focus {\n  --tw-text-opacity: 1;\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\n}\n.focus\\:placeholder-gray-400:focus::placeholder {\n  --tw-placeholder-opacity: 1;\n  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));\n}\n.focus\\:outline-none:focus {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n.focus\\:ring-1:focus {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.focus\\:ring-2:focus {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.focus\\:ring-blue-500:focus {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));\n}\n.focus\\:ring-gray-500:focus {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(107 114 128 / var(--tw-ring-opacity, 1));\n}\n.focus\\:ring-green-500:focus {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(34 197 94 / var(--tw-ring-opacity, 1));\n}\n.focus\\:ring-primary-500:focus {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));\n}\n.focus\\:ring-red-500:focus {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(239 68 68 / var(--tw-ring-opacity, 1));\n}\n.focus\\:ring-yellow-500:focus {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(234 179 8 / var(--tw-ring-opacity, 1));\n}\n.focus\\:ring-offset-0:focus {\n  --tw-ring-offset-width: 0px;\n}\n.focus\\:ring-offset-2:focus {\n  --tw-ring-offset-width: 2px;\n}\n.disabled\\:cursor-not-allowed:disabled {\n  cursor: not-allowed;\n}\n.disabled\\:border-gray-300:disabled {\n  --tw-border-opacity: 1;\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\n}\n.disabled\\:border-green-300:disabled {\n  --tw-border-opacity: 1;\n  border-color: rgb(134 239 172 / var(--tw-border-opacity, 1));\n}\n.disabled\\:border-primary-300:disabled {\n  --tw-border-opacity: 1;\n  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));\n}\n.disabled\\:border-red-300:disabled {\n  --tw-border-opacity: 1;\n  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));\n}\n.disabled\\:border-yellow-300:disabled {\n  --tw-border-opacity: 1;\n  border-color: rgb(253 224 71 / var(--tw-border-opacity, 1));\n}\n.disabled\\:bg-gray-100:disabled {\n  --tw-bg-opacity: 1;\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\n}\n.disabled\\:bg-gray-300:disabled {\n  --tw-bg-opacity: 1;\n  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));\n}\n.disabled\\:bg-green-300:disabled {\n  --tw-bg-opacity: 1;\n  background-color: rgb(134 239 172 / var(--tw-bg-opacity, 1));\n}\n.disabled\\:bg-primary-300:disabled {\n  --tw-bg-opacity: 1;\n  background-color: rgb(147 197 253 / var(--tw-bg-opacity, 1));\n}\n.disabled\\:bg-red-300:disabled {\n  --tw-bg-opacity: 1;\n  background-color: rgb(252 165 165 / var(--tw-bg-opacity, 1));\n}\n.disabled\\:bg-yellow-300:disabled {\n  --tw-bg-opacity: 1;\n  background-color: rgb(253 224 71 / var(--tw-bg-opacity, 1));\n}\n.disabled\\:text-gray-400:disabled {\n  --tw-text-opacity: 1;\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\n}\n.disabled\\:text-primary-300:disabled {\n  --tw-text-opacity: 1;\n  color: rgb(147 197 253 / var(--tw-text-opacity, 1));\n}\n.disabled\\:opacity-50:disabled {\n  opacity: 0.5;\n}\n.disabled\\:opacity-60:disabled {\n  opacity: 0.6;\n}\n.group:hover .group-hover\\:-translate-x-1 {\n  --tw-translate-x: -0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group:hover .group-hover\\:translate-x-1 {\n  --tw-translate-x: 0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group:hover .group-hover\\:scale-110 {\n  --tw-scale-x: 1.1;\n  --tw-scale-y: 1.1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.group:hover .group-hover\\:text-blue-600 {\n  --tw-text-opacity: 1;\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\n}\n.group:hover .group-hover\\:text-gray-600 {\n  --tw-text-opacity: 1;\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\n}\n.group:hover .group-hover\\:text-gray-700 {\n  --tw-text-opacity: 1;\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\n}\n.group:hover .group-hover\\:text-primary-600 {\n  --tw-text-opacity: 1;\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\n}\n.group:hover .group-hover\\:opacity-100 {\n  opacity: 1;\n}\n.peer:checked ~ .peer-checked\\:bg-blue-600 {\n  --tw-bg-opacity: 1;\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\n}\n.peer:checked ~ .peer-checked\\:after\\:translate-x-full::after {\n  content: var(--tw-content);\n  --tw-translate-x: 100%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.peer:checked ~ .peer-checked\\:after\\:border-white::after {\n  content: var(--tw-content);\n  --tw-border-opacity: 1;\n  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\n}\n.peer:focus ~ .peer-focus\\:outline-none {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n.peer:focus ~ .peer-focus\\:ring-4 {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.peer:focus ~ .peer-focus\\:ring-blue-300 {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(147 197 253 / var(--tw-ring-opacity, 1));\n}\n@media (min-width: 640px) {\n\n  .sm\\:mx-auto {\n    margin-left: auto;\n    margin-right: auto;\n  }\n\n  .sm\\:mt-0 {\n    margin-top: 0px;\n  }\n\n  .sm\\:flex {\n    display: flex;\n  }\n\n  .sm\\:hidden {\n    display: none;\n  }\n\n  .sm\\:w-full {\n    width: 100%;\n  }\n\n  .sm\\:max-w-md {\n    max-width: 28rem;\n  }\n\n  .sm\\:flex-1 {\n    flex: 1 1 0%;\n  }\n\n  .sm\\:grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .sm\\:flex-row {\n    flex-direction: row;\n  }\n\n  .sm\\:items-center {\n    align-items: center;\n  }\n\n  .sm\\:justify-between {\n    justify-content: space-between;\n  }\n\n  .sm\\:space-x-4 > :not([hidden]) ~ :not([hidden]) {\n    --tw-space-x-reverse: 0;\n    margin-right: calc(1rem * var(--tw-space-x-reverse));\n    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\n  }\n\n  .sm\\:space-y-0 > :not([hidden]) ~ :not([hidden]) {\n    --tw-space-y-reverse: 0;\n    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));\n    margin-bottom: calc(0px * var(--tw-space-y-reverse));\n  }\n\n  .sm\\:px-6 {\n    padding-left: 1.5rem;\n    padding-right: 1.5rem;\n  }\n}\n@media (min-width: 768px) {\n\n  .md\\:col-span-2 {\n    grid-column: span 2 / span 2;\n  }\n\n  .md\\:block {\n    display: block;\n  }\n\n  .md\\:flex {\n    display: flex;\n  }\n\n  .md\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .md\\:grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .md\\:grid-cols-4 {\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n\n  .md\\:flex-row {\n    flex-direction: row;\n  }\n\n  .md\\:space-y-0 > :not([hidden]) ~ :not([hidden]) {\n    --tw-space-y-reverse: 0;\n    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));\n    margin-bottom: calc(0px * var(--tw-space-y-reverse));\n  }\n\n  .md\\:text-2xl {\n    font-size: 1.5rem;\n    line-height: 2rem;\n  }\n\n  .md\\:text-4xl {\n    font-size: 2.25rem;\n    line-height: 2.5rem;\n  }\n\n  .md\\:text-5xl {\n    font-size: 3rem;\n    line-height: 1;\n  }\n\n  .md\\:text-6xl {\n    font-size: 3.75rem;\n    line-height: 1;\n  }\n\n  .md\\:text-lg {\n    font-size: 1.125rem;\n    line-height: 1.75rem;\n  }\n\n  .md\\:text-xl {\n    font-size: 1.25rem;\n    line-height: 1.75rem;\n  }\n}\n@media (min-width: 1024px) {\n\n  .lg\\:col-span-1 {\n    grid-column: span 1 / span 1;\n  }\n\n  .lg\\:col-span-2 {\n    grid-column: span 2 / span 2;\n  }\n\n  .lg\\:col-span-3 {\n    grid-column: span 3 / span 3;\n  }\n\n  .lg\\:ml-16 {\n    margin-left: 4rem;\n  }\n\n  .lg\\:ml-64 {\n    margin-left: 16rem;\n  }\n\n  .lg\\:flex {\n    display: flex;\n  }\n\n  .lg\\:hidden {\n    display: none;\n  }\n\n  .lg\\:w-16 {\n    width: 4rem;\n  }\n\n  .lg\\:w-64 {\n    width: 16rem;\n  }\n\n  .lg\\:translate-x-0 {\n    --tw-translate-x: 0px;\n    transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n  }\n\n  .lg\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .lg\\:grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .lg\\:grid-cols-4 {\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n\n  .lg\\:p-6 {\n    padding: 1.5rem;\n  }\n\n  .lg\\:px-8 {\n    padding-left: 2rem;\n    padding-right: 2rem;\n  }\n}\n@media (min-width: 1280px) {\n\n  .xl\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .xl\\:p-8 {\n    padding: 2rem;\n  }\n}\n@media print {\n\n  .print\\:block {\n    display: block;\n  }\n}\n.\\[\\&\\>button\\:first-child\\]\\:rounded-l-lg>button:first-child {\n  border-top-left-radius: 0.5rem;\n  border-bottom-left-radius: 0.5rem;\n}\n.\\[\\&\\>button\\:first-child\\]\\:rounded-l-none>button:first-child {\n  border-top-left-radius: 0px;\n  border-bottom-left-radius: 0px;\n}\n.\\[\\&\\>button\\:first-child\\]\\:rounded-t-lg>button:first-child {\n  border-top-left-radius: 0.5rem;\n  border-top-right-radius: 0.5rem;\n}\n.\\[\\&\\>button\\:last-child\\]\\:rounded-b-lg>button:last-child {\n  border-bottom-right-radius: 0.5rem;\n  border-bottom-left-radius: 0.5rem;\n}\n.\\[\\&\\>button\\:last-child\\]\\:rounded-r-lg>button:last-child {\n  border-top-right-radius: 0.5rem;\n  border-bottom-right-radius: 0.5rem;\n}\n.\\[\\&\\>button\\:last-child\\]\\:rounded-r-none>button:last-child {\n  border-top-right-radius: 0px;\n  border-bottom-right-radius: 0px;\n}\n.\\[\\&\\>button\\:not\\(\\:first-child\\)\\]\\:border-l>button:not(:first-child) {\n  border-left-width: 1px;\n}\n.\\[\\&\\>button\\:not\\(\\:first-child\\)\\]\\:border-l-0>button:not(:first-child) {\n  border-left-width: 0px;\n}\n.\\[\\&\\>button\\:not\\(\\:first-child\\)\\]\\:border-t-0>button:not(:first-child) {\n  border-top-width: 0px;\n}\n.\\[\\&\\>button\\]\\:rounded-none>button {\n  border-radius: 0px;\n}\n.\\[\\&\\>tr\\:nth-child\\(even\\)\\]\\:bg-gray-50>tr:nth-child(even) {\n  --tw-bg-opacity: 1;\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\n}\n.\\[\\&\\>tr\\]\\:transition-colors>tr {\n  transition-property: color, background-color, border-color, fill, stroke, -webkit-text-decoration-color;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, -webkit-text-decoration-color;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.\\[\\&\\>tr\\]\\:hover\\:bg-gray-50:hover>tr {\n  --tw-bg-opacity: 1;\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\n}`,""]);const f=g},8114:(e,t,n)=>{n.d(t,{v2:()=>c,UK:()=>l,qX:()=>d});var r=n(2635),a=n(4997),o=n(2389);const i=JSON.parse('{"common":{"save":"Save","cancel":"Cancel","delete":"Delete","edit":"Edit","add":"Add","close":"Close","confirm":"Confirm","loading":"Loading...","error":"Error","success":"Success","warning":"Warning","info":"Information","back":"Back"},"navigation":{"dashboard":"Dashboard","expenses":"Expenses","categories":"Categories","reports":"Reports","profile":"Profile","settings":"Settings","logout":"Logout"},"auth":{"login":"Login","register":"Register","email":"Email","password":"Password","confirmPassword":"Confirm Password","forgotPassword":"Forgot Password?","rememberMe":"Remember Me","loginSuccess":"Successfully logged in!","logoutSuccess":"You have been logged out successfully!","loginError":"Login error","registerSuccess":"Account created successfully!"},"settings":{"title":"Settings","subtitle":"Customize your app experience","general":{"title":"General Settings","currency":"Currency","language":"Language","theme":"Theme","dateFormat":"Date Format","themes":{"light":"Light","dark":"Dark","auto":"Auto"}},"notifications":{"title":"Notifications","email":{"title":"Email Notifications","description":"Receive notifications via email"},"push":{"title":"Push Notifications","description":"Receive notifications in browser"},"weeklyReport":{"title":"Weekly Reports","description":"Receive a weekly summary of your activity"},"budgetAlerts":{"title":"Budget Alerts","description":"Receive alerts when you exceed your budget"}},"privacy":{"title":"Privacy","dataSharing":{"title":"Data Sharing","description":"Allow data sharing for improvements"},"analytics":{"title":"Analytics","description":"Allow usage data collection"},"marketing":{"title":"Marketing","description":"Receive marketing communications"}},"export":{"title":"Export Data","description":"Download a copy of all your data in JSON format","button":"Export Data","allData":"Export All Data","success":"Data exported successfully!","error":"Error exporting data"},"dangerZone":{"title":"Danger Zone","logout":{"title":"Logout","description":"Sign out of your account","button":"Logout","confirmTitle":"Confirm Logout","confirmMessage":"Are you sure you want to logout?","confirmButton":"Logout"},"deleteAccount":{"title":"Delete Account","description":"Permanently delete your account and all data","button":"Delete Account","confirmTitle":"Confirm Account Deletion","warningTitle":"Warning! This action is irreversible.","warningMessage":"All your data will be permanently deleted and cannot be recovered.","confirmInstructions":"To confirm, type DELETE in the field below:","confirmPlaceholder":"Type DELETE to confirm","confirmButton":"Delete Account","success":"Account deleted successfully!","error":"Error deleting account"}},"saveButton":"Save Settings","saveSuccess":"Settings saved successfully!","saveError":"Error saving settings"},"currencies":{"RON":"RON - Romanian Leu","EUR":"EUR - Euro","USD":"USD - US Dollar","GBP":"GBP - British Pound"},"languages":{"ro":"Română","en":"English","fr":"Français","de":"Deutsch"},"landing":{"header":{"features":"Features","testimonials":"Testimonials","pricing":"Pricing","login":"Login","register":"Register","startFree":"Start Free"},"hero":{"title":{"part1":"Take Control of Your","part2":"Finances with Ease"},"subtitle":"FinanceFlow is the modern app that helps you track expenses, analyze financial trends, and achieve your savings goals.","cta":{"primary":"Start Free","secondary":"View Demo"},"startFree":"Start Free","viewDemo":"View Demo","disclaimer":"No credit card required • 2-minute setup • Cancel anytime","features":"No credit card required • 2-minute setup • Cancel anytime"},"features":{"title":"Powerful Features","subtitle":"All the tools you need to manage your finances in one application.","items":{"analytics":{"title":"Advanced Analytics","description":"Visualize spending trends with interactive charts and detailed reports."},"categories":{"title":"Category Management","description":"Organize expenses into customizable categories for better control."},"security":{"title":"Maximum Security","description":"Your data is protected with advanced encryption and secure authentication."},"mobile":{"title":"Mobile Access","description":"The app is optimized for all devices - desktop, tablet, and mobile."},"realtime":{"title":"Real Time","description":"Track expenses in real time and receive notifications for your budget."},"sharing":{"title":"Easy Sharing","description":"Share reports with family or accountant for collaborative management."}}},"stats":{"activeUsers":{"number":"10,000+","label":"Active Users"},"expensesTracked":{"number":"1M+","label":"Expenses Tracked"},"averageSavings":{"number":"25%","label":"Average Savings"},"uptime":{"number":"99.9%","label":"Uptime"}},"testimonials":{"title":"What Users Say","subtitle":"Thousands of people already manage their finances with FinanceFlow","items":{"maria":{"name":"Maria Popescu","role":"Entrepreneur","content":"FinanceFlow helped me reduce my expenses by 30% in just 3 months. The interface is intuitive and the reports are very useful."},"alexandru":{"name":"Alexandru Ionescu","role":"IT Manager","content":"The best financial tracking app I\'ve ever used. Data security and advanced features are impressive."},"elena":{"name":"Elena Dumitrescu","role":"Freelancer","content":"Perfect for freelancers! I can track all business expenses and generate reports for accounting."}}},"pricing":{"title":"Simple and Transparent Plans","subtitle":"Choose the plan that fits your needs","popular":"Most popular","perMonth":"per month","plans":{"free":{"name":"Free","period":"per month","features":{"transactions":"Up to 50 transactions/month","categories":"3 custom categories","reports":"Basic reports","support":"Email support"}},"pro":{"name":"Pro","period":"per month","description":"Most popular","features":{"transactions":"Unlimited transactions","categories":"Unlimited categories","reports":"Advanced reports","export":"Excel/PDF export","support":"Priority support","backup":"Automatic backup"}},"business":{"name":"Business","period":"per month","description":"For teams and companies","features":{"allPro":"All Pro features","multipleAccounts":"Multiple accounts","apiAccess":"API access","integrations":"Advanced integrations","manager":"Dedicated manager","sla":"99.9% SLA"}}},"currency":"USD","buttons":{"free":"Start Free","choose":"Choose Plan"},"choosePlan":"Choose Plan"},"cta":{"title":"Ready to Transform Your Finances?","subtitle":"Join thousands of users who have improved their financial situation with FinanceFlow.","button":"Start Free Now","features":"2-minute setup • No obligations • Cancel anytime","disclaimer":"2-minute setup • No obligations • Cancel anytime"},"footer":{"description":"The modern app for personal finance management.","product":{"title":"Product","features":"Features","pricing":"Pricing","api":"API","integrations":"Integrations"},"support":{"title":"Support","documentation":"Documentation","guides":"Guides","contact":"Contact","status":"Status"},"legal":{"title":"Legal","terms":"Terms","privacy":"Privacy","cookies":"Cookies","gdpr":"GDPR"},"copyright":"© 2024 FinanceFlow. All rights reserved."}},"legal":{"terms":{"title":"Terms and Conditions","subtitle":"Terms and conditions for using the FinanceFlow service","lastUpdated":"Last updated: December 15, 2024","sections":{"acceptance":{"title":"1. Acceptance of Terms","content":"By accessing and using the FinanceFlow service, you agree to be bound by these terms and conditions. If you do not agree with any of these terms, please do not use our service."},"service":{"title":"2. Service Description","content":"FinanceFlow is a personal finance management application that allows you to track expenses, analyze financial trends, and manage your budget. The service is provided \'as is\' and may be modified or discontinued at any time."},"account":{"title":"3. User Account","content":"To use certain features of the service, you must create an account. You are responsible for maintaining the confidentiality of your password and for all activities that occur under your account."},"data":{"title":"4. User Data","content":"You retain rights to the data you enter into the service. By using the service, you grant us a limited license to process this data for the purpose of providing the service."},"prohibited":{"title":"5. Prohibited Uses","content":"You may not use the service for illegal activities, to violate the rights of others, or to compromise system security. We reserve the right to suspend or close accounts that violate these terms."},"liability":{"title":"6. Limitation of Liability","content":"FinanceFlow will not be liable for direct, indirect, incidental, or consequential damages resulting from the use or inability to use the service."},"changes":{"title":"7. Changes to Terms","content":"We reserve the right to modify these terms at any time. Changes will be communicated via email or in-app notification at least 30 days before taking effect."},"termination":{"title":"8. Service Termination","content":"You may terminate use of the service at any time by deleting your account. We reserve the right to suspend or close accounts that violate these terms."},"contact":{"title":"9. Contact","content":"For questions about these terms, you can contact <NAME_EMAIL> or through the contact form in the application."}}},"privacy":{"title":"Privacy Policy","subtitle":"How we collect, use, and protect your personal information","lastUpdated":"Last updated: December 15, 2024","sections":{"introduction":{"title":"1. Introduction","content":"This privacy policy describes how FinanceFlow collects, uses, and protects your personal information when you use our service."},"collection":{"title":"2. Information We Collect","content":"We collect information you provide directly to us (such as name, email, and financial data), information about service usage, and technical information about your device."},"usage":{"title":"3. How We Use Information","content":"We use information to provide and improve the service, to contact you regarding your account, and to comply with legal obligations."},"sharing":{"title":"4. Information Sharing","content":"We do not sell, rent, or share your personal information with third parties, except as provided in this policy or when we have your consent."},"security":{"title":"5. Data Security","content":"We implement technical and organizational security measures to protect your information against unauthorized access, modification, disclosure, or destruction."},"retention":{"title":"6. Data Retention","content":"We retain your personal information only as long as necessary to fulfill the purposes for which it was collected or as required by legal requirements."},"rights":{"title":"7. Your Rights","content":"You have the right to access, correct, delete, or restrict the processing of your personal information. You also have the right to data portability."},"cookies":{"title":"8. Cookies and Similar Technologies","content":"We use cookies and similar technologies to improve your experience, analyze service usage, and personalize content."},"contact":{"title":"9. Contact","content":"For questions about this privacy policy, you can contact <NAME_EMAIL>."}}},"cookies":{"title":"Cookie Policy","subtitle":"How we use cookies and similar technologies","lastUpdated":"Last updated: December 15, 2024","manage":{"title":"Manage Cookie Preferences","description":"You can control what types of cookies you accept:","necessary":{"title":"Necessary Cookies","description":"Essential for website functionality","required":"(Required)"},"functional":{"title":"Functional Cookies","description":"Enhance user experience"},"analytics":{"title":"Analytics Cookies","description":"Help us understand how you use the site"},"marketing":{"title":"Marketing Cookies","description":"Used for personalized advertising"},"savePreferences":"Save Preferences","acceptAll":"Accept All","rejectAll":"Reject All"},"sections":{"what":{"title":"1. What are Cookies?","content":"Cookies are small text files that are stored on your device when you visit a website. They help us provide you with a better experience and understand how you use our service."},"how":{"title":"2. How We Use Cookies","content":"We use cookies to authenticate you, remember your preferences, analyze site traffic, and improve service functionality."},"types":{"title":"3. Types of Cookies","content":"We use necessary cookies (for basic functionality), functional cookies (for experience enhancement), analytics cookies (for statistics), and marketing cookies (for relevant advertising)."},"thirdParty":{"title":"4. Third-Party Cookies","content":"Some cookies are set by third-party services we use, such as Google Analytics for analysis and payment services for transaction processing."},"control":{"title":"5. Cookie Control","content":"You can control and delete cookies through your browser settings. You can also use our preferences panel to manage the types of cookies you accept."},"contact":{"title":"6. Contact","content":"For questions about cookie usage, you can contact <NAME_EMAIL>."}}}},"product":{"features":{"title":"Features","subtitle":"Discover all the powerful features of FinanceFlow","hero":{"title":"Complete Features for Financial Management","subtitle":"From expense tracking to advanced analytics, FinanceFlow provides all the tools you need to control your finances."},"sections":{"analytics":{"title":"Advanced Analytics","description":"Get detailed insights into your spending with interactive charts and customizable reports.","features":["Interactive charts and visualizations","Customizable reports","Trend analysis","Period comparisons","Export in multiple formats"]},"transactions":{"title":"Transaction Management","description":"Add, edit, and organize transactions with ease using our intuitive interface.","features":["Quick transaction entry","Customizable categories","Tags and notes","Advanced search and filtering","CSV file import"]},"notifications":{"title":"Smart Notifications","description":"Stay on top of your spending with personalized notifications and budget alerts.","features":["Budget alerts","Large expense notifications","Weekly reports","Bill reminders","Push and email notifications"]},"security":{"title":"Security and Privacy","description":"Your data is protected with the highest industry security standards.","features":["End-to-end encryption","Two-factor authentication","Automatic backup","GDPR compliance","Regular security audits"]},"budgeting":{"title":"Smart Budgeting","description":"Create and manage budgets for different categories and track progress in real-time.","features":["Category budgets","Real-time tracking","Overspend alerts","Recurring budgets","Performance analysis"]},"reports":{"title":"Detailed Reports","description":"Generate comprehensive reports to better understand your financial habits.","features":["Monthly and annual reports","Category analysis","Previous period comparisons","PDF and Excel export","Custom reports"]},"collaboration":{"title":"Family Collaboration","description":"Share budgets and expenses with family for collaborative financial management.","features":["Family accounts","Granular permissions","Shared budgets","Member notifications","Consolidated reports"]},"sync":{"title":"Cloud Sync","description":"Access your data from any device with automatic cloud synchronization.","features":["Real-time sync","Multi-device access","Automatic backup","Version history","Offline functionality"]},"mobile":{"title":"Mobile App","description":"Manage finances on the go with our optimized mobile application.","features":["Responsive design","Quick expense entry","Push notifications","Home screen widget","Offline functionality"]}},"integration":{"title":"Powerful Integrations","subtitle":"Connect FinanceFlow with your favorite services","description":"Integrate with banks, payment services, and accounting applications for a complete experience."},"cta":{"title":"Ready to Get Started?","subtitle":"Try all these features free for 30 days.","button":"Start Free Trial"}},"pricing":{"title":"Pricing","subtitle":"Simple and transparent plans for all needs","hero":{"title":"Choose the Perfect Plan for You","subtitle":"From individual users to large teams, we have the right plan for every need."},"billing":{"monthly":"Monthly","annually":"Annually","save":"Save 20%"},"plans":{"free":{"name":"Free","description":"Perfect for beginners","price":"0","period":"per month","features":["Up to 50 transactions/month","3 custom categories","Basic reports","Email support","Mobile app"],"button":"Start Free"},"personal":{"name":"Personal","description":"For active users","price":"29","priceAnnual":"24","period":"per month","popular":"Most popular","features":["Unlimited transactions","Unlimited categories","Advanced reports","Budgets and goals","Excel/PDF export","Priority support","Automatic backup"],"button":"Choose Personal"},"family":{"name":"Family","description":"For families and couples","price":"49","priceAnnual":"39","period":"per month","features":["All Personal features","Up to 5 members","Shared budgets","Granular permissions","Consolidated reports","In-app chat","Family manager"],"button":"Choose Family"},"business":{"name":"Business","description":"For teams and companies","price":"99","priceAnnual":"79","period":"per month","features":["All Family features","Unlimited members","API access","Advanced integrations","SSO and SAML","Dedicated manager","99.9% SLA"],"button":"Contact Sales"}},"comparison":{"title":"Detailed Comparison","features":{"transactions":"Transactions","categories":"Categories","reports":"Reports","budgets":"Budgets","export":"Data Export","support":"Support","backup":"Backup","members":"Members","api":"API Access","sso":"SSO/SAML","manager":"Dedicated Manager","sla":"SLA"},"values":{"limited":"Limited","unlimited":"Unlimited","basic":"Basic","advanced":"Advanced","email":"Email","priority":"Priority","dedicated":"Dedicated","yes":"Yes","no":"No"}},"faq":{"title":"Frequently Asked Questions","items":{"trial":{"question":"Is there a free trial?","answer":"Yes, we offer a 30-day free trial for all paid plans. No credit card required."},"cancel":{"question":"Can I cancel my subscription anytime?","answer":"Absolutely! You can cancel your subscription anytime from your account settings. No penalties or cancellation fees."},"upgrade":{"question":"Can I change my plan anytime?","answer":"Yes, you can upgrade or downgrade your plan anytime. Changes apply immediately and billing is adjusted proportionally."},"data":{"question":"What happens to my data if I cancel?","answer":"Your data remains available for 90 days after cancellation. You can export all data before permanent deletion."},"security":{"question":"How secure is my data?","answer":"We use bank-level encryption and comply with all industry security standards. Data is stored in certified data centers."},"support":{"question":"What type of support do you offer?","answer":"We offer email support for all plans, priority support for paid plans, and dedicated manager for Business plan."}}},"cta":{"title":"Ready to Get Started?","subtitle":"Join thousands of users managing their finances with FinanceFlow.","button":"Start Free Trial"}}},"support":{"documentation":{"title":"Documentation","subtitle":"Complete guides and technical documentation for FinanceFlow","search":{"placeholder":"Search documentation...","button":"Search"},"categories":{"all":"All","getting_started":"Getting Started","features":"Features","api":"API","security":"Security","faq":"FAQ"},"sections":{"getting_started":{"title":"Getting Started","description":"Everything you need to know to get started with FinanceFlow","articles":{"setup":{"title":"Account Setup","description":"Step-by-step guide to setting up your first account","readTime":"5 min"},"first_transaction":{"title":"Adding Your First Transaction","description":"Learn to add and manage transactions","readTime":"3 min"},"categories":{"title":"Organizing with Categories","description":"How to create and use categories effectively","readTime":"7 min"}}},"features":{"title":"Features","description":"Detailed guides for all features","articles":{"budgets":{"title":"Budget Management","description":"Create and monitor effective budgets","readTime":"10 min"},"reports":{"title":"Report Generation","description":"How to generate useful and customized reports","readTime":"8 min"},"notifications":{"title":"Notification Setup","description":"Set up personalized alerts and notifications","readTime":"5 min"}}},"api":{"title":"API Documentation","description":"Integrate FinanceFlow into your applications","articles":{"authentication":{"title":"Authentication","description":"How to authenticate and manage tokens","readTime":"15 min"},"endpoints":{"title":"Available Endpoints","description":"Complete list of API endpoints","readTime":"20 min"},"examples":{"title":"Code Examples","description":"Practical examples in different languages","readTime":"25 min"}}},"security":{"title":"Security","description":"Information about data security and privacy","articles":{"data_protection":{"title":"Data Protection","description":"How we protect your data and personal information","readTime":"12 min"},"two_factor":{"title":"Two-Factor Authentication","description":"Setting up and using 2FA","readTime":"6 min"},"best_practices":{"title":"Best Practices","description":"Recommendations for account security","readTime":"8 min"}}},"faq":{"title":"Frequently Asked Questions","description":"Answers to the most common questions","articles":{"general":{"title":"General Questions","description":"Frequently asked questions about the service","readTime":"10 min"},"billing":{"title":"Billing and Payments","description":"Questions about plans and billing","readTime":"8 min"},"technical":{"title":"Technical Issues","description":"Solutions for common technical problems","readTime":"15 min"}}}},"quickStart":{"title":"Quick Start","subtitle":"Start using FinanceFlow in just a few steps","steps":["Create your free account","Add your first transaction","Set up categories","Create your first budget"],"button":"Start Now"},"help":{"title":"Need Help?","subtitle":"Can\'t find what you\'re looking for? Our team is here to help.","contact":"Contact Support","chat":"Live Chat"}},"contact":{"title":"Contact","subtitle":"We\'re here to help! Choose the contact method that works best for you."},"help":{"title":"Help Center","subtitle":"Find answers to your questions and learn to use FinanceFlow to its fullest."}},"contact":{"categories":{"general":"General Question","technical":"Technical Issue","billing":"Billing","feature":"Feature Request"},"priority":{"low":"Low","medium":"Medium","high":"High"},"methods":{"title":"Contact Methods","chat":{"title":"Live Chat","description":"Immediate response for urgent questions","availability":"Monday - Friday, 9:00 AM - 6:00 PM","action":"Start Chat"},"email":{"title":"Email","description":"For detailed questions and documentation","availability":"Response within 24 hours"},"phone":{"title":"Phone","description":"Phone support for Premium customers","availability":"Monday - Friday, 10:00 AM - 5:00 PM"}},"office":{"title":"Our Office","address":{"title":"Address","line1":"123 Example Street","line2":"Sector 1, Bucharest","line3":"Romania, 010101"},"hours":{"title":"Hours","weekdays":"Monday - Friday: 9:00 AM - 6:00 PM","weekend":"Saturday - Sunday: Closed"}},"form":{"title":"Send us a Message","name":"Full Name","name_placeholder":"Enter your name","email":"Email","email_placeholder":"<EMAIL>","subject":"Subject","subject_placeholder":"Briefly describe the issue","category":"Category","priority":"Priority","message":"Message","message_placeholder":"Describe your issue or question in detail...","required":"Required fields","send":"Send Message","sending":"Sending...","success":"Message sent successfully! We\'ll get back to you soon.","error":"An error occurred. Please try again."},"faq":{"title":"Frequently Asked Questions","subtitle":"You might find the answer here before contacting us","response_time":{"question":"How long does it take to get a response?","answer":"We typically respond within 24 hours for email and immediately for live chat during business hours."},"technical_support":{"question":"Do you provide technical support?","answer":"Yes, our technical team is available to help you with any technical issues."},"billing_support":{"question":"Can I get help with billing?","answer":"Absolutely! Our billing team can help you with any account and payment related questions."},"feature_request":{"question":"How can I request a new feature?","answer":"Use the contact form and select \\"Feature Request\\" as the category. We appreciate feedback!"}}},"help":{"categories":{"all":"All","getting_started":"Getting Started","features":"Features","settings":"Settings","security":"Security","billing":"Billing","mobile":"Mobile App"},"search":{"placeholder":"Search help center..."},"sections":{"quick_start":{"title":"Quick Start","description":"Guides to get started quickly with FinanceFlow","items":{"setup":"Account Setup","setup_desc":"How to set up your account in 5 minutes","first_transaction":"First Transaction","first_transaction_desc":"Add your first transaction","categories":"Organizing Categories","categories_desc":"Create and manage categories"}},"features":{"title":"Features","description":"Explore all available features","items":{"budgets":"Budget Management","budgets_desc":"Create and monitor budgets","reports":"Reports and Analytics","reports_desc":"Generate detailed reports","goals":"Financial Goals","goals_desc":"Set and track goals","notifications":"Smart Notifications","notifications_desc":"Configure personalized alerts"}},"troubleshooting":{"title":"Troubleshooting","description":"Solutions for common problems","items":{"sync":"Sync Issues","sync_desc":"Resolve synchronization problems","login":"Login Issues","login_desc":"Can\'t log into your account?","performance":"App Performance","performance_desc":"App running slowly?"}},"advanced":{"title":"Advanced Features","description":"For experienced users","items":{"api":"API Integration","api_desc":"Connect external applications","automation":"Automation","automation_desc":"Set up automatic rules","export":"Data Export","export_desc":"Export data in various formats"}}},"popular":{"title":"Popular Articles","views":"views","article1":{"title":"How to set up my first budget?"},"article2":{"title":"Connecting bank accounts"},"article3":{"title":"Financial data security"},"article4":{"title":"Using the mobile app"},"article5":{"title":"Managing subscriptions"}},"quick_actions":{"title":"Quick Actions","contact":"Contact Support","contact_desc":"Get personalized help","docs":"API Documentation","docs_desc":"For developers","video_tour":"Video Tour","video_tour_desc":"General overview"},"cta":{"title":"Didn\'t find what you were looking for?","description":"Our support team is ready to help you with any question or issue.","contact":"Contact Support","chat":"Live Chat"}}}'),s={ro:{translation:JSON.parse('{"common":{"save":"Salvează","cancel":"Anulează","delete":"Șterge","edit":"Editează","add":"Adaugă","close":"Închide","confirm":"Confirmă","loading":"Se încarcă...","error":"Eroare","success":"Succes","warning":"Atenție","info":"Informație","back":"Înapoi"},"navigation":{"dashboard":"Tablou de bord","expenses":"Cheltuieli","categories":"Categorii","reports":"Rapoarte","profile":"Profil","settings":"Setări","logout":"Deconectare"},"auth":{"login":"Autentificare","logout":"Deconectare","register":"Înregistrare","email":"Email","password":"Parolă","confirmPassword":"Confirmă parola","forgotPassword":"Ai uitat parola?","loginButton":"Conectează-te","registerButton":"Înregistrează-te","loginError":"Eroare la autentificare","registerError":"Eroare la înregistrare","logoutSuccess":"Te-ai deconectat cu succes!"},"settings":{"title":"Setări","subtitle":"Personalizează experiența aplicației","general":{"title":"Setări generale","currency":"Moneda","language":"Limba","theme":"Tema","dateFormat":"Format dată","themes":{"light":"Luminos","dark":"Întunecat","auto":"Automat"}},"notifications":{"title":"Notificări","email":{"title":"Notificări email","description":"Primește notificări prin email"},"push":{"title":"Notificări push","description":"Primește notificări în browser"},"weeklyReport":{"title":"Rapoarte săptămânale","description":"Primește un raport săptămânal cu activitatea ta"},"budgetAlerts":{"title":"Alerte buget","description":"Primește alerte când depășești bugetul"}},"privacy":{"title":"Confidențialitate","dataSharing":{"title":"Partajare date","description":"Permite partajarea datelor pentru îmbunătățiri"},"analytics":{"title":"Analytics","description":"Permite colectarea datelor de utilizare"},"marketing":{"title":"Marketing","description":"Primește comunicări de marketing"}},"export":{"title":"Export date","description":"Descarcă o copie a tuturor datelor tale într-un format JSON","button":"Exportă datele","allData":"Exportă toate datele","success":"Datele au fost exportate cu succes!","error":"Eroare la exportul datelor"},"dangerZone":{"title":"Zonă periculoasă","logout":{"title":"Deconectare","description":"Deconectează-te din cont","button":"Deconectare","confirmTitle":"Confirmare deconectare","confirmMessage":"Ești sigur că vrei să te deconectezi?","confirmButton":"Deconectează-te"},"deleteAccount":{"title":"Șterge contul","description":"Șterge permanent contul și toate datele","button":"Șterge contul","confirmTitle":"Confirmare ștergere cont","warningTitle":"Atenție! Această acțiune este ireversibilă.","warningMessage":"Toate datele tale vor fi șterse permanent și nu vor putea fi recuperate.","confirmInstructions":"Pentru a confirma, scrie ȘTERGE în câmpul de mai jos:","confirmPlaceholder":"Scrie ȘTERGE pentru a confirma","confirmButton":"Șterge contul","success":"Contul a fost șters cu succes!","error":"Eroare la ștergerea contului"}},"saveButton":"Salvează setările","saveSuccess":"Setările au fost salvate cu succes!","saveError":"Eroare la salvarea setărilor"},"currencies":{"RON":"RON - Leu românesc","EUR":"EUR - Euro","USD":"USD - Dolar american","GBP":"GBP - Liră sterlină"},"languages":{"ro":"Română","en":"English","fr":"Français","de":"Deutsch"},"landing":{"header":{"features":"Funcționalități","testimonials":"Testimoniale","pricing":"Prețuri","login":"Conectare","register":"Înregistrare","startFree":"Începe Gratuit"},"hero":{"title":{"part1":"Controlează-ți","part2":"Finanțele cu Ușurință"},"subtitle":"FinanceFlow este aplicația modernă care te ajută să urmărești cheltuielile, să analizezi tendințele financiare și să îți atingi obiectivele de economisire.","cta":{"primary":"Începe Gratuit","secondary":"Vezi Demo"},"startFree":"Începe Gratuit","viewDemo":"Vezi Demo","disclaimer":"Fără card de credit • Configurare în 2 minute • Anulare oricând","features":"Fără card de credit • Configurare în 2 minute • Anulare oricând"},"features":{"title":"Funcționalități Puternice","subtitle":"Toate instrumentele de care ai nevoie pentru a-ți gestiona finanțele într-o singură aplicație.","items":{"analytics":{"title":"Analiză Avansată","description":"Vizualizează tendințele cheltuielilor cu grafice interactive și rapoarte detaliate."},"categories":{"title":"Gestionare Categorii","description":"Organizează cheltuielile pe categorii personalizabile pentru un control mai bun."},"security":{"title":"Securitate Maximă","description":"Datele tale sunt protejate cu criptare avansată și autentificare securizată."},"mobile":{"title":"Acces Mobil","description":"Aplicația este optimizată pentru toate dispozitivele - desktop, tabletă și mobil."},"realtime":{"title":"Timp Real","description":"Urmărește cheltuielile în timp real și primește notificări pentru bugetul tău."},"sharing":{"title":"Partajare Facilă","description":"Partajează rapoartele cu familia sau contabilul pentru o gestionare colaborativă."}}},"stats":{"activeUsers":{"number":"10,000+","label":"Utilizatori Activi"},"expensesTracked":{"number":"1M+","label":"Cheltuieli Urmărite"},"averageSavings":{"number":"25%","label":"Economii Medii"},"uptime":{"number":"99.9%","label":"Uptime"}},"testimonials":{"title":"Ce Spun Utilizatorii","subtitle":"Mii de oameni își gestionează deja finanțele cu FinanceFlow","items":{"maria":{"name":"Maria Popescu","role":"Antreprenor","content":"FinanceFlow m-a ajutat să îmi reduc cheltuielile cu 30% în doar 3 luni. Interfața este intuitivă și rapoartele sunt foarte utile."},"alexandru":{"name":"Alexandru Ionescu","role":"Manager IT","content":"Cea mai bună aplicație de tracking financiar pe care am folosit-o. Securitatea datelor și funcționalitățile avansate sunt impresionante."},"elena":{"name":"Elena Dumitrescu","role":"Freelancer","content":"Perfect pentru freelanceri! Pot urmări toate cheltuielile de business și să generez rapoarte pentru contabilitate."}}},"pricing":{"title":"Planuri Simple și Transparente","subtitle":"Alege planul care se potrivește nevoilor tale","popular":"Cel mai popular","perMonth":"pe lună","plans":{"free":{"name":"Gratuit","period":"pe lună","features":{"transactions":"Până la 50 de tranzacții/lună","categories":"3 categorii personalizate","reports":"Rapoarte de bază","support":"Suport email"}},"pro":{"name":"Pro","period":"pe lună","description":"Cel mai popular","features":{"transactions":"Tranzacții nelimitate","categories":"Categorii nelimitate","reports":"Rapoarte avansate","export":"Export în Excel/PDF","support":"Suport prioritar","backup":"Backup automat"}},"business":{"name":"Business","period":"pe lună","description":"Pentru echipe și companii","features":{"allPro":"Toate funcționalitățile Pro","multipleAccounts":"Conturi multiple","apiAccess":"API access","integrations":"Integrări avansate","manager":"Manager dedicat","sla":"SLA 99.9%"}}},"currency":"RON","buttons":{"free":"Începe Gratuit","choose":"Alege Planul"},"choosePlan":"Alege Planul"},"cta":{"title":"Gata să Îți Transformi Finanțele?","subtitle":"Alătură-te miilor de utilizatori care și-au îmbunătățit situația financiară cu FinanceFlow.","button":"Începe Gratuit Acum","features":"Configurare în 2 minute • Fără obligații • Anulare oricând","disclaimer":"Configurare în 2 minute • Fără obligații • Anulare oricând"},"footer":{"description":"Aplicația modernă pentru gestionarea finanțelor personale.","product":{"title":"Produs","features":"Funcționalități","pricing":"Prețuri","api":"API","integrations":"Integrări"},"support":{"title":"Suport","documentation":"Documentație","guides":"Ghiduri","contact":"Contact","status":"Status"},"legal":{"title":"Legal","terms":"Termeni","privacy":"Confidențialitate","cookies":"Cookies","gdpr":"GDPR"},"copyright":"© 2024 FinanceFlow. Toate drepturile rezervate."}},"legal":{"terms":{"title":"Termeni și Condiții","subtitle":"Termenii și condițiile de utilizare a serviciului FinanceFlow","lastUpdated":"Ultima actualizare: 15 decembrie 2024","sections":{"acceptance":{"title":"1. Acceptarea Termenilor","content":"Prin accesarea și utilizarea serviciului FinanceFlow, acceptați să fiți legați de acești termeni și condiții. Dacă nu sunteți de acord cu oricare dintre acești termeni, vă rugăm să nu utilizați serviciul nostru."},"service":{"title":"2. Descrierea Serviciului","content":"FinanceFlow este o aplicație de gestionare a finanțelor personale care vă permite să urmăriți cheltuielile, să analizați tendințele financiare și să vă gestionați bugetul. Serviciul este furnizat \'ca atare\' și poate fi modificat sau întrerupt în orice moment."},"account":{"title":"3. Contul Utilizatorului","content":"Pentru a utiliza anumite funcționalități ale serviciului, trebuie să vă creați un cont. Sunteți responsabil pentru menținerea confidențialității parolei și pentru toate activitățile care au loc sub contul dumneavoastră."},"data":{"title":"4. Datele Utilizatorului","content":"Vă păstrați drepturile asupra datelor pe care le introduceți în serviciu. Prin utilizarea serviciului, ne acordați o licență limitată pentru a procesa aceste date în scopul furnizării serviciului."},"prohibited":{"title":"5. Utilizări Interzise","content":"Nu puteți utiliza serviciul pentru activități ilegale, pentru a încălca drepturile altora sau pentru a compromite securitatea sistemului. Ne rezervăm dreptul de a suspenda sau închide conturile care încalcă acești termeni."},"liability":{"title":"6. Limitarea Răspunderii","content":"FinanceFlow nu va fi responsabil pentru daune directe, indirecte, incidentale sau consecvente rezultate din utilizarea sau incapacitatea de utilizare a serviciului."},"changes":{"title":"7. Modificări ale Termenilor","content":"Ne rezervăm dreptul de a modifica acești termeni în orice moment. Modificările vor fi comunicate prin email sau prin notificare în aplicație cu cel puțin 30 de zile înainte de intrarea în vigoare."},"termination":{"title":"8. Încetarea Serviciului","content":"Puteți înceta utilizarea serviciului în orice moment prin ștergerea contului. Ne rezervăm dreptul de a suspenda sau închide conturile care încalcă acești termeni."},"contact":{"title":"9. Contact","content":"Pentru întrebări despre acești termeni, ne puteți <NAME_EMAIL> sau prin formularul de contact din aplicație."}}},"privacy":{"title":"Politica de Confidențialitate","subtitle":"Cum colectăm, utilizăm și protejăm informațiile dumneavoastră personale","lastUpdated":"Ultima actualizare: 15 decembrie 2024","sections":{"introduction":{"title":"1. Introducere","content":"Această politică de confidențialitate descrie cum FinanceFlow colectează, utilizează și protejează informațiile dumneavoastră personale când utilizați serviciul nostru."},"collection":{"title":"2. Informațiile pe care le Colectăm","content":"Colectăm informații pe care ni le furnizați direct (cum ar fi numele, emailul și datele financiare), informații despre utilizarea serviciului și informații tehnice despre dispozitivul dumneavoastră."},"usage":{"title":"3. Cum Utilizăm Informațiile","content":"Utilizăm informațiile pentru a furniza și îmbunătăți serviciul, pentru a vă contacta cu privire la contul dumneavoastră și pentru a respecta obligațiile legale."},"sharing":{"title":"4. Partajarea Informațiilor","content":"Nu vindem, nu închiriem și nu partajăm informațiile dumneavoastră personale cu terțe părți, cu excepția cazurilor prevăzute în această politică sau când avem consimțământul dumneavoastră."},"security":{"title":"5. Securitatea Datelor","content":"Implementăm măsuri de securitate tehnice și organizatorice pentru a proteja informațiile dumneavoastră împotriva accesului neautorizat, modificării, divulgării sau distrugerii."},"retention":{"title":"6. Păstrarea Datelor","content":"Păstrăm informațiile dumneavoastră personale doar atât timp cât este necesar pentru îndeplinirea scopurilor pentru care au fost colectate sau conform cerințelor legale."},"rights":{"title":"7. Drepturile Dumneavoastră","content":"Aveți dreptul să accesați, să corectați, să ștergeți sau să restricționați procesarea informațiilor dumneavoastră personale. De asemenea, aveți dreptul la portabilitatea datelor."},"cookies":{"title":"8. Cookies și Tehnologii Similare","content":"Utilizăm cookies și tehnologii similare pentru a îmbunătăți experiența dumneavoastră, pentru a analiza utilizarea serviciului și pentru a personaliza conținutul."},"contact":{"title":"9. Contact","content":"Pentru întrebări despre această politică de confidențialitate, ne puteți <NAME_EMAIL>."}}},"cookies":{"title":"Politica Cookies","subtitle":"Cum utilizăm cookies-urile și tehnologiile similare","lastUpdated":"Ultima actualizare: 15 decembrie 2024","manage":{"title":"Gestionează Preferințele Cookies","description":"Puteți controla ce tipuri de cookies acceptați:","necessary":{"title":"Cookies Necesare","description":"Esențiale pentru funcționarea site-ului","required":"(Obligatorii)"},"functional":{"title":"Cookies Funcționale","description":"Îmbunătățesc experiența utilizatorului"},"analytics":{"title":"Cookies de Analiză","description":"Ne ajută să înțelegem cum utilizați site-ul"},"marketing":{"title":"Cookies de Marketing","description":"Utilizate pentru publicitate personalizată"},"savePreferences":"Salvează Preferințele","acceptAll":"Acceptă Toate","rejectAll":"Respinge Toate"},"sections":{"what":{"title":"1. Ce sunt Cookies-urile?","content":"Cookies-urile sunt fișiere mici de text care sunt stocate pe dispozitivul dumneavoastră când vizitați un site web. Ele ne ajută să vă oferim o experiență mai bună și să înțelegem cum utilizați serviciul nostru."},"how":{"title":"2. Cum Utilizăm Cookies-urile","content":"Utilizăm cookies-urile pentru a vă autentifica, a vă reține preferințele, a analiza traficul pe site și a îmbunătăți funcționalitatea serviciului."},"types":{"title":"3. Tipuri de Cookies","content":"Utilizăm cookies necesare (pentru funcționarea de bază), cookies funcționale (pentru îmbunătățirea experienței), cookies de analiză (pentru statistici) și cookies de marketing (pentru publicitate relevantă)."},"thirdParty":{"title":"4. Cookies de la Terțe Părți","content":"Unele cookies sunt setate de servicii terțe pe care le utilizăm, cum ar fi Google Analytics pentru analiză și servicii de plată pentru procesarea tranzacțiilor."},"control":{"title":"5. Controlul Cookies-urilor","content":"Puteți controla și șterge cookies-urile prin setările browserului dumneavoastră. De asemenea, puteți utiliza panoul nostru de preferințe pentru a gestiona tipurile de cookies pe care le acceptați."},"contact":{"title":"6. Contact","content":"Pentru întrebări despre utilizarea cookies-urilor, ne puteți <NAME_EMAIL>."}}}},"product":{"features":{"title":"Funcționalități","subtitle":"Descoperă toate funcționalitățile puternice ale FinanceFlow","hero":{"title":"Funcționalități Complete pentru Gestionarea Finanțelor","subtitle":"De la urmărirea cheltuielilor la analize avansate, FinanceFlow oferă toate instrumentele de care ai nevoie pentru a-ți controla finanțele."},"sections":{"analytics":{"title":"Analiză Avansată","description":"Obține perspective detaliate asupra cheltuielilor tale cu grafice interactive și rapoarte personalizabile.","features":["Grafice interactive și vizualizări","Rapoarte personalizabile","Analiză de tendințe","Comparații pe perioade","Export în multiple formate"]},"transactions":{"title":"Gestionarea Tranzacțiilor","description":"Adaugă, editează și organizează tranzacțiile cu ușurință folosind interfața noastră intuitivă.","features":["Adăugare rapidă de tranzacții","Categorii personalizabile","Etichete și note","Căutare și filtrare avansată","Import din fișiere CSV"]},"notifications":{"title":"Notificări Inteligente","description":"Rămâi la curent cu cheltuielile tale prin notificări personalizate și alerte de buget.","features":["Alerte de buget","Notificări de cheltuieli mari","Rapoarte săptămânale","Reminder-uri pentru facturi","Notificări push și email"]},"security":{"title":"Securitate și Confidențialitate","description":"Datele tale sunt protejate cu cele mai înalte standarde de securitate din industrie.","features":["Criptare end-to-end","Autentificare cu doi factori","Backup automat","Conformitate GDPR","Audit de securitate regulat"]},"budgeting":{"title":"Bugetare Inteligentă","description":"Creează și gestionează bugete pentru diferite categorii și urmărește progresul în timp real.","features":["Bugete pe categorii","Urmărire în timp real","Alerte de depășire","Bugete recurente","Analiză de performanță"]},"reports":{"title":"Rapoarte Detaliate","description":"Generează rapoarte comprehensive pentru a înțelege mai bine obiceiurile tale financiare.","features":["Rapoarte lunare și anuale","Analiză pe categorii","Comparații cu perioadele anterioare","Export în PDF și Excel","Rapoarte personalizate"]},"collaboration":{"title":"Colaborare Familială","description":"Partajează bugete și cheltuieli cu familia pentru o gestionare financiară colaborativă.","features":["Conturi familiale","Permisiuni granulare","Bugete partajate","Notificări pentru membri","Rapoarte consolidate"]},"sync":{"title":"Sincronizare Cloud","description":"Accesează datele tale de pe orice dispozitiv cu sincronizare automată în cloud.","features":["Sincronizare în timp real","Acces multi-dispozitiv","Backup automat","Istoric versiuni","Funcționare offline"]},"mobile":{"title":"Aplicație Mobilă","description":"Gestionează finanțele în mișcare cu aplicația noastră mobilă optimizată.","features":["Design responsive","Adăugare rapidă de cheltuieli","Notificări push","Widget pentru ecranul principal","Funcționare offline"]}},"integration":{"title":"Integrări Puternice","subtitle":"Conectează FinanceFlow cu serviciile tale preferate","description":"Integrează cu băncile, serviciile de plată și aplicațiile de contabilitate pentru o experiență completă."},"cta":{"title":"Gata să Începi?","subtitle":"Încearcă toate aceste funcționalități gratuit timp de 30 de zile.","button":"Începe Perioada de Probă Gratuită"}},"pricing":{"title":"Prețuri","subtitle":"Planuri simple și transparente pentru toate nevoile","hero":{"title":"Alege Planul Perfect pentru Tine","subtitle":"De la utilizatori individuali la echipe mari, avem planul potrivit pentru fiecare nevoie."},"billing":{"monthly":"Lunar","annually":"Anual","save":"Economisești 20%"},"plans":{"free":{"name":"Gratuit","description":"Perfect pentru începători","price":"0","period":"pe lună","features":["Până la 50 de tranzacții/lună","3 categorii personalizate","Rapoarte de bază","Suport prin email","Aplicație mobilă"],"button":"Începe Gratuit"},"personal":{"name":"Personal","description":"Pentru utilizatori activi","price":"29","priceAnnual":"24","period":"pe lună","popular":"Cel mai popular","features":["Tranzacții nelimitate","Categorii nelimitate","Rapoarte avansate","Bugete și obiective","Export în Excel/PDF","Suport prioritar","Backup automat"],"button":"Alege Personal"},"family":{"name":"Familie","description":"Pentru familii și cupluri","price":"49","priceAnnual":"39","period":"pe lună","features":["Toate funcționalitățile Personal","Până la 5 membri","Bugete partajate","Permisiuni granulare","Rapoarte consolidate","Chat în aplicație","Manager de familie"],"button":"Alege Familie"},"business":{"name":"Business","description":"Pentru echipe și companii","price":"99","priceAnnual":"79","period":"pe lună","features":["Toate funcționalitățile Familie","Membri nelimitați","API access","Integrări avansate","SSO și SAML","Manager dedicat","SLA 99.9%"],"button":"Contactează Vânzările"}},"comparison":{"title":"Comparație Detaliată","features":{"transactions":"Tranzacții","categories":"Categorii","reports":"Rapoarte","budgets":"Bugete","export":"Export Date","support":"Suport","backup":"Backup","members":"Membri","api":"API Access","sso":"SSO/SAML","manager":"Manager Dedicat","sla":"SLA"},"values":{"limited":"Limitat","unlimited":"Nelimitat","basic":"De bază","advanced":"Avansat","email":"Email","priority":"Prioritar","dedicated":"Dedicat","yes":"Da","no":"Nu"}},"faq":{"title":"Întrebări Frecvente","items":{"trial":{"question":"Există o perioadă de probă gratuită?","answer":"Da, oferim o perioadă de probă gratuită de 30 de zile pentru toate planurile plătite. Nu este necesar card de credit."},"cancel":{"question":"Pot anula abonamentul oricând?","answer":"Absolut! Poți anula abonamentul oricând din setările contului. Nu există penalități sau taxe de anulare."},"upgrade":{"question":"Pot schimba planul în orice moment?","answer":"Da, poți face upgrade sau downgrade la planul tău oricând. Modificările se aplică imediat și facturarea se ajustează proporțional."},"data":{"question":"Ce se întâmplă cu datele mele dacă anulez?","answer":"Datele tale rămân disponibile timp de 90 de zile după anulare. Poți exporta toate datele înainte de ștergerea definitivă."},"security":{"question":"Cât de sigure sunt datele mele?","answer":"Utilizăm criptare de nivel bancar și respectăm toate standardele de securitate din industrie. Datele sunt stocate în centre de date certificate."},"support":{"question":"Ce tip de suport oferiti?","answer":"Oferim suport prin email pentru toate planurile, suport prioritar pentru planurile plătite și manager dedicat pentru planul Business."}}},"cta":{"title":"Gata să Începi?","subtitle":"Alătură-te miilor de utilizatori care își gestionează finanțele cu FinanceFlow.","button":"Începe Perioada de Probă Gratuită"}}},"support":{"documentation":{"title":"Documentație","subtitle":"Ghiduri complete și documentație tehnică pentru FinanceFlow","search":{"placeholder":"Căutați în documentație...","button":"Căutare"},"categories":{"all":"Toate","getting_started":"Primii Pași","features":"Funcționalități","api":"API","security":"Securitate","faq":"FAQ"},"sections":{"getting_started":{"title":"Primii Pași","description":"Tot ce trebuie să știi pentru a începe cu FinanceFlow","articles":{"setup":{"title":"Configurarea Contului","description":"Ghid pas cu pas pentru configurarea primului tău cont","readTime":"5 min"},"first_transaction":{"title":"Adăugarea Primei Tranzacții","description":"Învață să adaugi și să gestionezi tranzacțiile","readTime":"3 min"},"categories":{"title":"Organizarea cu Categorii","description":"Cum să creezi și să folosești categoriile eficient","readTime":"7 min"}}},"features":{"title":"Funcționalități","description":"Ghiduri detaliate pentru toate funcționalitățile","articles":{"budgets":{"title":"Gestionarea Bugetelor","description":"Creează și monitorizează bugete eficiente","readTime":"10 min"},"reports":{"title":"Generarea Rapoartelor","description":"Cum să generezi rapoarte utile și personalizate","readTime":"8 min"},"notifications":{"title":"Configurarea Notificărilor","description":"Setează alerte și notificări personalizate","readTime":"5 min"}}},"api":{"title":"Documentația API","description":"Integrează FinanceFlow în aplicațiile tale","articles":{"authentication":{"title":"Autentificare","description":"Cum să te autentifici și să gestionezi token-urile","readTime":"15 min"},"endpoints":{"title":"Endpoint-uri Disponibile","description":"Lista completă a endpoint-urilor API","readTime":"20 min"},"examples":{"title":"Exemple de Cod","description":"Exemple practice în diferite limbaje","readTime":"25 min"}}},"security":{"title":"Securitate","description":"Informații despre securitatea și confidențialitatea datelor","articles":{"data_protection":{"title":"Protecția Datelor","description":"Cum îți protejăm datele și informațiile personale","readTime":"12 min"},"two_factor":{"title":"Autentificare cu Doi Factori","description":"Configurarea și utilizarea 2FA","readTime":"6 min"},"best_practices":{"title":"Cele Mai Bune Practici","description":"Recomandări pentru securitatea contului","readTime":"8 min"}}},"faq":{"title":"Întrebări Frecvente","description":"Răspunsuri la cele mai comune întrebări","articles":{"general":{"title":"Întrebări Generale","description":"Întrebări frecvente despre serviciu","readTime":"10 min"},"billing":{"title":"Facturare și Plăți","description":"Întrebări despre planuri și facturare","readTime":"8 min"},"technical":{"title":"Probleme Tehnice","description":"Soluții pentru problemele tehnice comune","readTime":"15 min"}}}},"quickStart":{"title":"Start Rapid","subtitle":"Începe să folosești FinanceFlow în doar câțiva pași","steps":["Creează-ți contul gratuit","Adaugă prima tranzacție","Configurează categoriile","Setează primul buget"],"button":"Începe Acum"},"help":{"title":"Ai Nevoie de Ajutor?","subtitle":"Nu găsești ceea ce cauți? Echipa noastră este aici să te ajute.","contact":"Contactează Suportul","chat":"Chat Live"}},"contact":{"title":"Contact","subtitle":"Suntem aici să vă ajutăm! Alegeți metoda de contact care vi se potrivește cel mai bine."},"help":{"title":"Centrul de Ajutor","subtitle":"Găsiți răspunsuri la întrebările dvs. și învățați să folosiți FinanceFlow la maximum."}},"contact":{"categories":{"general":"Întrebare Generală","technical":"Problemă Tehnică","billing":"Facturare","feature":"Cerere Funcționalitate"},"priority":{"low":"Scăzută","medium":"Medie","high":"Înaltă"},"methods":{"title":"Metode de Contact","chat":{"title":"Chat Live","description":"Răspuns imediat pentru întrebări urgente","availability":"Luni - Vineri, 9:00 - 18:00","action":"Începeți Chat"},"email":{"title":"Email","description":"Pentru întrebări detaliate și documentație","availability":"Răspuns în 24 ore"},"phone":{"title":"Telefon","description":"Suport telefonic pentru clienții Premium","availability":"Luni - Vineri, 10:00 - 17:00"}},"office":{"title":"Biroul Nostru","address":{"title":"Adresa","line1":"Strada Exemplu nr. 123","line2":"Sector 1, București","line3":"România, 010101"},"hours":{"title":"Program","weekdays":"Luni - Vineri: 9:00 - 18:00","weekend":"Sâmbătă - Duminică: Închis"}},"form":{"title":"Trimiteți-ne un Mesaj","name":"Nume Complet","name_placeholder":"Introduceți numele dvs.","email":"Email","email_placeholder":"<EMAIL>","subject":"Subiect","subject_placeholder":"Descrieți pe scurt problema","category":"Categorie","priority":"Prioritate","message":"Mesaj","message_placeholder":"Descrieți detaliat problema sau întrebarea dvs...","required":"Câmpuri obligatorii","send":"Trimite Mesajul","sending":"Se trimite...","success":"Mesajul a fost trimis cu succes! Vă vom răspunde în curând.","error":"A apărut o eroare. Vă rugăm să încercați din nou."},"faq":{"title":"Întrebări Frecvente","subtitle":"Poate găsiți răspunsul aici înainte de a ne contacta","response_time":{"question":"Cât durează să primiți un răspuns?","answer":"De obicei răspundem în 24 de ore pentru email și imediat pentru chat live în timpul programului."},"technical_support":{"question":"Oferiti suport tehnic?","answer":"Da, echipa noastră tehnică este disponibilă pentru a vă ajuta cu orice problemă tehnică."},"billing_support":{"question":"Pot primi ajutor cu facturarea?","answer":"Absolut! Echipa noastră de facturare vă poate ajuta cu orice întrebări legate de cont și plăți."},"feature_request":{"question":"Cum pot cere o funcționalitate nouă?","answer":"Folosiți formularul de contact și selectați \\"Cerere Funcționalitate\\" ca categorie. Apreciem feedback-ul!"}}},"help":{"categories":{"all":"Toate","getting_started":"Primii Pași","features":"Funcționalități","settings":"Setări","security":"Securitate","billing":"Facturare","mobile":"Aplicația Mobilă"},"search":{"placeholder":"Căutați în centrul de ajutor..."},"sections":{"quick_start":{"title":"Start Rapid","description":"Ghiduri pentru a începe rapid cu FinanceFlow","items":{"setup":"Configurarea Contului","setup_desc":"Cum să vă configurați contul în 5 minute","first_transaction":"Prima Tranzacție","first_transaction_desc":"Adăugați prima dvs. tranzacție","categories":"Organizarea Categoriilor","categories_desc":"Creați și gestionați categoriile"}},"features":{"title":"Funcționalități","description":"Explorați toate funcționalitățile disponibile","items":{"budgets":"Gestionarea Bugetelor","budgets_desc":"Creați și monitorizați bugete","reports":"Rapoarte și Analize","reports_desc":"Generați rapoarte detaliate","goals":"Obiective Financiare","goals_desc":"Setați și urmăriți obiective","notifications":"Notificări Inteligente","notifications_desc":"Configurați alertele personalizate"}},"troubleshooting":{"title":"Rezolvarea Problemelor","description":"Soluții pentru problemele comune","items":{"sync":"Probleme de Sincronizare","sync_desc":"Rezolvați problemele de sincronizare","login":"Probleme de Autentificare","login_desc":"Nu vă puteți conecta la cont?","performance":"Performanță Aplicație","performance_desc":"Aplicația funcționează lent?"}},"advanced":{"title":"Funcții Avansate","description":"Pentru utilizatorii experimentați","items":{"api":"Integrare API","api_desc":"Conectați aplicații externe","automation":"Automatizări","automation_desc":"Configurați reguli automate","export":"Export Date","export_desc":"Exportați datele în diverse formate"}}},"popular":{"title":"Articole Populare","views":"vizualizări","article1":{"title":"Cum să configurez primul meu buget?"},"article2":{"title":"Conectarea conturilor bancare"},"article3":{"title":"Securitatea datelor financiare"},"article4":{"title":"Utilizarea aplicației mobile"},"article5":{"title":"Gestionarea abonamentelor"}},"quick_actions":{"title":"Acțiuni Rapide","contact":"Contactați Suportul","contact_desc":"Obțineți ajutor personalizat","docs":"Documentația API","docs_desc":"Pentru dezvoltatori","video_tour":"Tur Video","video_tour_desc":"Prezentare generală"},"cta":{"title":"Nu ați găsit ceea ce căutați?","description":"Echipa noastră de suport este gata să vă ajute cu orice întrebare sau problemă.","contact":"Contactați Suportul","chat":"Chat Live"}}}')},en:{translation:i}};r.Ay.use(a.A).use(o.r9).init({resources:s,fallbackLng:"ro",lng:"ro",detection:{order:["localStorage","navigator","htmlTag"],caches:["localStorage"],lookupLocalStorage:"i18nextLng"},debug:!0,interpolation:{escapeValue:!1},react:{useSuspense:!1}});const c=e=>r.Ay.changeLanguage(e),l=()=>r.Ay.language,d=()=>Object.keys(s)},9050:(e,t,n)=>{n.d(t,{cn:()=>o});var r=n(4164),a=n(856);function o(...e){return(0,a.QP)((0,r.$)(e))}},9264:(e,t,n)=>{n.d(t,{Ay:()=>p});var r=n(4848),a=(n(6540),n(2392));const o={xs:"w-3 h-3",sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8",xl:"w-12 h-12","2xl":"w-16 h-16"},i={primary:"text-primary-600",secondary:"text-secondary-600",success:"text-green-600",warning:"text-yellow-600",danger:"text-red-600",white:"text-white",gray:"text-gray-600",currentColor:"text-current"},s=({size:e,color:t,className:n})=>(0,r.jsxs)("svg",{className:(0,a.cn)("animate-spin",o[e],i[t],n),xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,r.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,r.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),c=({size:e,color:t,className:n})=>{const o={xs:"w-1 h-1",sm:"w-1.5 h-1.5",md:"w-2 h-2",lg:"w-2.5 h-2.5",xl:"w-3 h-3","2xl":"w-4 h-4"};return(0,r.jsx)("div",{className:(0,a.cn)("flex space-x-1",n),children:[0,1,2].map(n=>(0,r.jsx)("div",{className:(0,a.cn)("rounded-full animate-pulse",o[e],i[t].replace("text-","bg-")),style:{animationDelay:.2*n+"s",animationDuration:"1s"}},n))})},l=({size:e,color:t,className:n})=>(0,r.jsx)("div",{className:(0,a.cn)("rounded-full animate-pulse",o[e],i[t].replace("text-","bg-"),n),style:{animationDuration:"1.5s"}}),d=({size:e,color:t,className:n})=>{const o={xs:"h-2",sm:"h-3",md:"h-4",lg:"h-6",xl:"h-8","2xl":"h-10"},s={xs:"w-0.5",sm:"w-0.5",md:"w-1",lg:"w-1",xl:"w-1.5","2xl":"w-2"};return(0,r.jsx)("div",{className:(0,a.cn)("flex items-end space-x-0.5",n),children:[0,1,2,3,4].map(n=>(0,r.jsx)("div",{className:(0,a.cn)("animate-pulse",o[e],s[e],i[t].replace("text-","bg-")),style:{animationDelay:.1*n+"s",animationDuration:"1s"}},n))})},p=({size:e="md",color:t="primary",type:n="circular",className:o,label:p,inline:m=!1,overlay:g=!1,overlayClassName:u})=>{const w={circular:s,dots:c,pulse:l,bars:d}[n],h=(0,r.jsx)(w,{size:e,color:t,className:o});return m?p?(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[h,p&&(0,r.jsx)("span",{className:(0,a.cn)("text-sm",i[t]),children:p})]}):h:g?(0,r.jsx)("div",{className:(0,a.cn)("absolute inset-0 flex items-center justify-center bg-white/80 backdrop-blur-sm z-50",u),children:(0,r.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[h,p&&(0,r.jsx)("span",{className:(0,a.cn)("text-sm font-medium",i[t]),children:p})]})}):(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center space-y-2",children:[h,p&&(0,r.jsx)("span",{className:(0,a.cn)("text-sm font-medium",i[t]),children:p})]})}}},i={};function s(e){var t=i[e];if(void 0!==t)return t.exports;var n=i[e]={id:e,loaded:!1,exports:{}};return o[e](n,n.exports,s),n.loaded=!0,n.exports}s.m=o,e=[],s.O=(t,n,r,a)=>{if(!n){var o=1/0;for(d=0;d<e.length;d++){n=e[d][0],r=e[d][1],a=e[d][2];for(var i=!0,c=0;c<n.length;c++)(!1&a||o>=a)&&Object.keys(s.O).every(e=>s.O[e](n[c]))?n.splice(c--,1):(i=!1,a<o&&(o=a));if(i){e.splice(d--,1);var l=r();void 0!==l&&(t=l)}}return t}a=a||0;for(var d=e.length;d>0&&e[d-1][2]>a;d--)e[d]=e[d-1];e[d]=[n,r,a]},s.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return s.d(t,{a:t}),t},n=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,s.t=function(e,r){if(1&r&&(e=this(e)),8&r)return e;if("object"==typeof e&&e){if(4&r&&e.__esModule)return e;if(16&r&&"function"==typeof e.then)return e}var a=Object.create(null);s.r(a);var o={};t=t||[null,n({}),n([]),n(n)];for(var i=2&r&&e;"object"==typeof i&&!~t.indexOf(i);i=n(i))Object.getOwnPropertyNames(i).forEach(t=>o[t]=()=>e[t]);return o.default=()=>e,s.d(a,o),a},s.d=(e,t)=>{for(var n in t)s.o(t,n)&&!s.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},s.f={},s.e=e=>Promise.all(Object.keys(s.f).reduce((t,n)=>(s.f[n](e,t),t),[])),s.u=e=>e+"."+{16:"63837fed5e40c976f08f",95:"edec4ba22a9cc83dd713",187:"f034faea7b9f96a6b4b1",219:"b5edb8e2d208c42d6a9f",291:"e6fec3d0a00dab272660",296:"7d0f573f918626de5199",314:"9b1e2a87c245ed91c4fd",333:"d425978467484e52c291",381:"22e6d057b1c3615a14cb",396:"d120cd28827dc7020262",405:"65e8e7c314df3188f6a6",409:"259113092fe5940b4ba2",470:"7c0f75bfbe69d7924499",476:"2615b3dad0b5fde2d0f8",566:"c320dafc235821daaff3",605:"71257714e76629a6e511",633:"dceab5897498947cdc57",667:"38a82ed291a8bcd82be7",700:"917b6a624d8bf119c6e4",731:"043e23895a2c6c8ed42e",732:"96f7d84250728cd03609",807:"725f3663a2a9fa684ec1",824:"4f2c3f8921acf410be3d",833:"9b5ec1e3fce768672885",842:"981c130011d07194754f",875:"d48aafea8829214c9151",890:"d949d9a127a24218588f",896:"9992f0c9d1cc1ff13afc",901:"91f7269987fa6690db94",955:"6066b1b4efed5e60fb89",974:"0f13a81f94e5adb2584b"}[e]+".js",s.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r={},a="expense-tracker-frontend:",s.l=(e,t,n,o)=>{if(r[e])r[e].push(t);else{var i,c;if(void 0!==n)for(var l=document.getElementsByTagName("script"),d=0;d<l.length;d++){var p=l[d];if(p.getAttribute("src")==e||p.getAttribute("data-webpack")==a+n){i=p;break}}i||(c=!0,(i=document.createElement("script")).charset="utf-8",i.timeout=120,s.nc&&i.setAttribute("nonce",s.nc),i.setAttribute("data-webpack",a+n),i.src=e),r[e]=[t];var m=(t,n)=>{i.onerror=i.onload=null,clearTimeout(g);var a=r[e];if(delete r[e],i.parentNode&&i.parentNode.removeChild(i),a&&a.forEach(e=>e(n)),t)return t(n)},g=setTimeout(m.bind(null,void 0,{type:"timeout",target:i}),12e4);i.onerror=m.bind(null,i.onerror),i.onload=m.bind(null,i.onload),c&&document.head.appendChild(i)}},s.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},s.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),s.p="/",(()=>{s.b=document.baseURI||self.location.href;var e={792:0};s.f.j=(t,n)=>{var r=s.o(e,t)?e[t]:void 0;if(0!==r)if(r)n.push(r[2]);else{var a=new Promise((n,a)=>r=e[t]=[n,a]);n.push(r[2]=a);var o=s.p+s.u(t),i=new Error;s.l(o,n=>{if(s.o(e,t)&&(0!==(r=e[t])&&(e[t]=void 0),r)){var a=n&&("load"===n.type?"missing":n.type),o=n&&n.target&&n.target.src;i.message="Loading chunk "+t+" failed.\n("+a+": "+o+")",i.name="ChunkLoadError",i.type=a,i.request=o,r[1](i)}},"chunk-"+t,t)}},s.O.j=t=>0===e[t];var t=(t,n)=>{var r,a,o=n[0],i=n[1],c=n[2],l=0;if(o.some(t=>0!==e[t])){for(r in i)s.o(i,r)&&(s.m[r]=i[r]);if(c)var d=c(s)}for(t&&t(n);l<o.length;l++)a=o[l],s.o(e,a)&&e[a]&&e[a][0](),e[a]=0;return s.O(d)},n=self.webpackChunkexpense_tracker_frontend=self.webpackChunkexpense_tracker_frontend||[];n.forEach(t.bind(null,0)),n.push=t.bind(null,n.push.bind(n))})(),s.nc=void 0;var c=s.O(void 0,[96],()=>s(2423));c=s.O(c)})();