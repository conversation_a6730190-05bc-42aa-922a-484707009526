import * as winston from 'winston';
import { Request, Response } from 'express';
interface AuthenticatedRequest extends Request {
    user?: {
        id: string;
        [key: string]: any;
    };
}
interface CustomLogger {
    stream: {
        write: (message: string) => void;
    };
    logRequest: (req: AuthenticatedRequest, res: Response, responseTime: number) => void;
    logPerformance: (operation: string, duration: number | string, metadata?: Record<string, any>) => void;
    logAudit: (action: string, userId: string, details?: Record<string, any>) => void;
    logSecurity: (event: string, details?: Record<string, any>) => void;
    logDatabase: (query: string, duration: number, metadata?: Record<string, any>) => void;
    info: (message: any, ...meta: any[]) => winston.Logger;
    error: (message: any, ...meta: any[]) => winston.Logger;
    warn: (message: any, ...meta: any[]) => winston.Logger;
    debug: (message: any, ...meta: any[]) => winston.Logger;
    add: (transport: winston.transport) => winston.Logger;
}
declare const customLogger: CustomLogger;
export default customLogger;
//# sourceMappingURL=logger.d.ts.map