"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const auth_1 = require("../middleware/auth");
const usageController_1 = require("../controllers/usageController");
const router = express_1.default.Router();
router.use(auth_1.authenticateToken);
router.get('/current', usageController_1.getCurrentUsage);
router.get('/stats', usageController_1.getUsageStats);
router.post('/check-action', usageController_1.checkActionPermission);
router.get('/upgrade-recommendations', usageController_1.getUpgradeRecommendations);
exports.default = router;
//# sourceMappingURL=usage.js.map