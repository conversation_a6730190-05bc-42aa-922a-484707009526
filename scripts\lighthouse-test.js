#!/usr/bin/env node

/**
 * Script pentru rularea testelor Lighthouse în diferite configurații
 * Usage: node scripts/lighthouse-test.js [options]
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configurări pentru diferite tipuri de teste
const configurations = {
  desktop: {
    env: {
      NODE_ENV: 'development',
      LIGHTHOUSE_MOBILE: 'false',
    },
    description: 'Desktop performance testing',
  },
  mobile: {
    env: {
      NODE_ENV: 'development',
      LIGHTHOUSE_MOBILE: 'true',
    },
    description: 'Mobile performance testing',
  },
  production: {
    env: {
      NODE_ENV: 'production',
      LIGHTHOUSE_MOBILE: 'false',
    },
    description: 'Production environment testing',
  },
  ci: {
    env: {
      CI: 'true',
      NODE_ENV: 'development',
      LIGHTHOUSE_MOBILE: 'false',
    },
    description: 'CI/CD pipeline testing',
  },
};

// Funcții helper
function log(message, type = 'info') {
  const colors = {
    info: '\x1b[36m',    // cyan
    success: '\x1b[32m', // green
    warning: '\x1b[33m', // yellow
    error: '\x1b[31m',   // red
    reset: '\x1b[0m',    // reset
  };
  
  console.log(`${colors[type]}[${type.toUpperCase()}]${colors.reset} ${message}`);
}

function executeCommand(command, env = {}) {
  try {
    const result = execSync(command, {
      stdio: 'inherit',
      env: { ...process.env, ...env },
      cwd: path.resolve(__dirname, '..'),
    });
    return true;
  } catch (error) {
    log(`Command failed: ${command}`, 'error');
    log(error.message, 'error');
    return false;
  }
}

function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    log(`Created directory: ${dirPath}`, 'info');
  }
}

function generateReport(config, results) {
  const reportDir = path.resolve(__dirname, '..', 'lighthouse-reports');
  ensureDirectoryExists(reportDir);
  
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const reportFile = path.join(reportDir, `lighthouse-${config}-${timestamp}.json`);
  
  const report = {
    configuration: config,
    timestamp: new Date().toISOString(),
    description: configurations[config].description,
    results: results,
    environment: configurations[config].env,
  };
  
  fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
  log(`Report saved: ${reportFile}`, 'success');
  
  return reportFile;
}

function runLighthouseTest(config) {
  log(`Starting Lighthouse test: ${config}`, 'info');
  log(`Description: ${configurations[config].description}`, 'info');
  
  const env = configurations[config].env;
  
  // Verifică dacă serverul de development rulează
  if (config !== 'production') {
    log('Checking if development server is running...', 'info');
    try {
      execSync('curl -f http://localhost:5173 > /dev/null 2>&1');
      log('Development server is running', 'success');
    } catch (error) {
      log('Development server is not running. Starting it...', 'warning');
      log('Please run "npm run dev" in another terminal and try again', 'error');
      return false;
    }
  }
  
  // Rulează testele Lighthouse
  const command = 'npx lhci autorun';
  log(`Executing: ${command}`, 'info');
  
  const success = executeCommand(command, env);
  
  if (success) {
    log(`Lighthouse test completed successfully: ${config}`, 'success');
    return true;
  } else {
    log(`Lighthouse test failed: ${config}`, 'error');
    return false;
  }
}

function runAllTests() {
  log('Running all Lighthouse configurations...', 'info');
  
  const results = {};
  let totalTests = 0;
  let passedTests = 0;
  
  for (const [config, settings] of Object.entries(configurations)) {
    totalTests++;
    log(`\n${'='.repeat(50)}`, 'info');
    log(`Running configuration: ${config}`, 'info');
    log(`${'='.repeat(50)}`, 'info');
    
    const success = runLighthouseTest(config);
    results[config] = {
      success,
      timestamp: new Date().toISOString(),
      description: settings.description,
    };
    
    if (success) {
      passedTests++;
    }
    
    // Pauză între teste
    if (totalTests < Object.keys(configurations).length) {
      log('Waiting 5 seconds before next test...', 'info');
      execSync('sleep 5');
    }
  }
  
  // Raport final
  log(`\n${'='.repeat(50)}`, 'info');
  log('FINAL RESULTS', 'info');
  log(`${'='.repeat(50)}`, 'info');
  
  for (const [config, result] of Object.entries(results)) {
    const status = result.success ? 'PASSED' : 'FAILED';
    const color = result.success ? 'success' : 'error';
    log(`${config.padEnd(15)} - ${status}`, color);
  }
  
  log(`\nTotal: ${totalTests}, Passed: ${passedTests}, Failed: ${totalTests - passedTests}`, 'info');
  
  if (passedTests === totalTests) {
    log('All Lighthouse tests passed! 🎉', 'success');
    return true;
  } else {
    log('Some Lighthouse tests failed. Check the logs above.', 'error');
    return false;
  }
}

function showHelp() {
  console.log(`
Lighthouse Testing Script
========================

Usage: node scripts/lighthouse-test.js [command]

Commands:
  desktop     Run desktop performance tests
  mobile      Run mobile performance tests  
  production  Run production environment tests
  ci          Run CI/CD pipeline tests
  all         Run all test configurations
  help        Show this help message

Examples:
  node scripts/lighthouse-test.js desktop
  node scripts/lighthouse-test.js all
  
Available configurations:
`);

  for (const [config, settings] of Object.entries(configurations)) {
    console.log(`  ${config.padEnd(12)} - ${settings.description}`);
  }
  
  console.log(`
Environment Variables:
  LIGHTHOUSE_MOBILE=true   - Enable mobile testing
  NODE_ENV=production      - Use production URLs
  CI=true                  - Enable CI optimizations
  
Reports are saved to: ./lighthouse-reports/
`);
}

// Main execution
function main() {
  const args = process.argv.slice(2);
  const command = args[0] || 'help';
  
  log('Lighthouse Testing Script', 'info');
  log('========================', 'info');
  
  switch (command) {
    case 'desktop':
    case 'mobile':
    case 'production':
    case 'ci':
      if (configurations[command]) {
        const success = runLighthouseTest(command);
        process.exit(success ? 0 : 1);
      } else {
        log(`Unknown configuration: ${command}`, 'error');
        showHelp();
        process.exit(1);
      }
      break;
      
    case 'all':
      const success = runAllTests();
      process.exit(success ? 0 : 1);
      break;
      
    case 'help':
    case '--help':
    case '-h':
      showHelp();
      process.exit(0);
      break;
      
    default:
      log(`Unknown command: ${command}`, 'error');
      showHelp();
      process.exit(1);
  }
}

// Verifică dacă scriptul este rulat direct
if (require.main === module) {
  main();
}

module.exports = {
  configurations,
  runLighthouseTest,
  runAllTests,
};
