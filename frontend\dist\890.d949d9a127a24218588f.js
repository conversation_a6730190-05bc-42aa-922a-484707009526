"use strict";(self.webpackChunkexpense_tracker_frontend=self.webpackChunkexpense_tracker_frontend||[]).push([[890],{3890:(e,s,r)=>{r.r(s),r.d(s,{default:()=>g});var a=r(4403),t=r(794),l=r(5795),i=r(3930),c=r(6540),n=r(888),o=r(6103),d=r(125),m=r(6215),x=r(9264),u=r(2552),p=r(2392),h=r(4848);const y=[{id:1,name:"<PERSON><PERSON><PERSON>",description:"Cheltuieli pentru mâncare și băuturi",color:"#3B82F6",totalExpenses:450.25,expenseCount:15},{id:2,name:"Transport",description:"Cheltuieli pentru transport și combustibil",color:"#10B981",totalExpenses:200,expenseCount:8},{id:3,name:"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",description:"Facturi și utilități casnice",color:"#F59E0B",totalExpenses:150,expenseCount:5},{id:4,name:"Divertisment",description:"Activități de divertisment și hobby-uri",color:"#8B5CF6",totalExpenses:50,expenseCount:3}],g=()=>{const[e,s]=(0,c.useState)(!1),[r,g]=(0,c.useState)(null),[f,j]=(0,c.useState)({name:"",description:"",color:"#3B82F6"}),{data:N,isLoading:b,error:v}=(0,i.I)({queryKey:["categories"],queryFn:async()=>(await new Promise(e=>setTimeout(e,500)),y)}),w=(e=null)=>{e?(g(e),j({name:e.name,description:e.description,color:e.color})):(g(null),j({name:"",description:"",color:"#3B82F6"})),s(!0)},C=()=>{s(!1),g(null),j({name:"",description:"",color:"#3B82F6"})};return v?(0,h.jsx)("div",{className:"text-center py-12",children:(0,h.jsx)("p",{className:"text-red-600",children:"Eroare la încărcarea categoriilor"})}):(0,h.jsxs)("div",{className:"space-y-6",children:[(0,h.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between",children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Categorii"}),(0,h.jsx)("p",{className:"text-gray-600",children:"Gestionează categoriile pentru cheltuielile tale"})]}),(0,h.jsx)("div",{className:"mt-4 sm:mt-0",children:(0,h.jsxs)(o.Ay,{variant:"primary",onClick:()=>w(),className:"flex items-center",children:[(0,h.jsx)(a.A,{className:"h-5 w-5 mr-2"}),"Adaugă categorie"]})})]}),b?(0,h.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,h.jsx)(x.Ay,{size:"lg"})}):N&&N.length>0?(0,h.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:N.map(e=>(0,h.jsxs)(d.Ay,{className:"p-6",children:[(0,h.jsxs)("div",{className:"flex items-start justify-between",children:[(0,h.jsxs)("div",{className:"flex items-center",children:[(0,h.jsx)("div",{className:"w-4 h-4 rounded-full mr-3",style:{backgroundColor:e.color}}),(0,h.jsxs)("div",{children:[(0,h.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:e.name}),(0,h.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:e.description})]})]}),(0,h.jsxs)("div",{className:"flex space-x-2",children:[(0,h.jsx)("button",{onClick:()=>w(e),className:"text-gray-400 hover:text-gray-600",children:(0,h.jsx)(t.A,{className:"h-4 w-4"})}),(0,h.jsx)("button",{onClick:()=>(async()=>{if(window.confirm("Ești sigur că vrei să ștergi această categorie?"))try{n.oR.success("Categoria a fost ștearsă cu succes!")}catch(e){n.oR.error("Eroare la ștergerea categoriei")}})(e.id),className:"text-gray-400 hover:text-red-600",children:(0,h.jsx)(l.A,{className:"h-4 w-4"})})]})]}),(0,h.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-200",children:(0,h.jsxs)("div",{className:"flex justify-between items-center",children:[(0,h.jsxs)("div",{children:[(0,h.jsx)("p",{className:"text-sm text-gray-500",children:"Total cheltuieli"}),(0,h.jsx)("p",{className:"text-lg font-semibold text-gray-900",children:(0,p.vv)(e.totalExpenses)})]}),(0,h.jsxs)("div",{className:"text-right",children:[(0,h.jsx)("p",{className:"text-sm text-gray-500",children:"Numărul de cheltuieli"}),(0,h.jsx)("p",{className:"text-lg font-semibold text-gray-900",children:e.expenseCount})]})]})})]},e.id))}):(0,h.jsxs)(d.Ay,{className:"p-12 text-center",children:[(0,h.jsx)("p",{className:"text-gray-500 mb-4",children:"Nu ai încă categorii create"}),(0,h.jsxs)(o.Ay,{variant:"primary",onClick:()=>w(),className:"flex items-center mx-auto",children:[(0,h.jsx)(a.A,{className:"h-5 w-5 mr-2"}),"Creează prima categorie"]})]}),(0,h.jsx)(u.Ay,{isOpen:e,onClose:C,title:r?"Editează categoria":"Adaugă categorie nouă",children:(0,h.jsxs)("form",{onSubmit:async e=>{e.preventDefault();try{r?n.oR.success("Categoria a fost actualizată cu succes!"):n.oR.success("Categoria a fost creată cu succes!"),C()}catch(e){n.oR.error("Eroare la salvarea categoriei")}},className:"space-y-4",children:[(0,h.jsx)("div",{children:(0,h.jsx)(m.Ay,{label:"Nume categorie",type:"text",value:f.name,onChange:e=>j({...f,name:e.target.value}),placeholder:"Ex: Mâncare, Transport, etc.",required:!0})}),(0,h.jsxs)("div",{children:[(0,h.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Descriere"}),(0,h.jsx)("textarea",{value:f.description,onChange:e=>j({...f,description:e.target.value}),placeholder:"Descriere opțională pentru categorie",rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"})]}),(0,h.jsxs)("div",{children:[(0,h.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Culoare"}),(0,h.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,h.jsx)("input",{type:"color",value:f.color,onChange:e=>j({...f,color:e.target.value}),className:"w-12 h-10 border border-gray-300 rounded-md"}),(0,h.jsx)("input",{type:"text",value:f.color,onChange:e=>j({...f,color:e.target.value}),className:"flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500",placeholder:"#3B82F6"})]})]}),(0,h.jsxs)("div",{className:"flex justify-end space-x-3 pt-4",children:[(0,h.jsx)(o.Ay,{type:"button",variant:"outline",onClick:C,children:"Anulează"}),(0,h.jsx)(o.Ay,{type:"submit",variant:"primary",children:r?"Actualizează":"Creează"})]})]})})]})}},6215:(e,s,r)=>{r.d(s,{Ay:()=>x});var a=r(4848),t=r(2509),l=r(72),i=r(3956),c=r(7117),n=r(4015),o=r(6540),d=r(2392);const m=(0,o.forwardRef)(({label:e,type:s="text",placeholder:r,value:m,onChange:x,onBlur:u,onFocus:p,error:h,success:y,hint:g,required:f=!1,disabled:j=!1,readOnly:N=!1,size:b="md",leftIcon:v,rightIcon:w,className:C="",inputClassName:A="",labelClassName:k="",id:R,name:E,...B},I)=>{const[F,z]=(0,o.useState)(!1),[S,D]=(0,o.useState)(!1),T=R||`input-${Math.random().toString(36).substr(2,9)}`,q="password"===s&&F?"text":s,_={sm:"h-4 w-4",md:"h-5 w-5",lg:"h-6 w-6"},M=Boolean(h),O=Boolean(y)&&!M;return Boolean(v||w||"password"===s),(0,a.jsxs)("div",{className:(0,d.cn)("w-full",C),children:[e&&(0,a.jsxs)("label",{htmlFor:T,className:(0,d.cn)("block text-sm font-medium mb-2",M?"text-red-700":"text-gray-700",j&&"text-gray-400",k),children:[e,f&&(0,a.jsx)("span",{className:"text-red-500 ml-1","aria-label":"obligatoriu",children:"*"})]}),(0,a.jsxs)("div",{className:"relative",children:[v&&(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)("span",{className:(0,d.cn)(_[b],M?"text-red-400":"text-gray-400"),children:v})}),(0,a.jsx)("input",{ref:I,id:T,name:E,type:q,value:m,onChange:x,onFocus:e=>{D(!0),p?.(e)},onBlur:e=>{D(!1),u?.(e)},placeholder:r,required:f,disabled:j,readOnly:N,className:(0,d.cn)("block w-full border rounded-lg transition-all duration-200","focus:outline-none focus:ring-2 focus:ring-offset-0","placeholder:text-gray-400",{sm:"px-3 py-2 text-sm",md:"px-4 py-2.5 text-sm",lg:"px-4 py-3 text-base"}[b],v&&"pl-10",(w||"password"===s)&&"pr-10",!j&&!N&&[M?["border-red-300 text-red-900","focus:border-red-500 focus:ring-red-500"]:O?["border-green-300 text-green-900","focus:border-green-500 focus:ring-green-500"]:["border-gray-300 text-gray-900","focus:border-primary-500 focus:ring-primary-500","hover:border-gray-400"]],j&&["bg-gray-50 border-gray-200 text-gray-500","cursor-not-allowed"],N&&["bg-gray-50 border-gray-200","cursor-default"],A),...B}),(0,a.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:"password"===s?(0,a.jsx)("button",{type:"button",onClick:()=>{z(!F)},className:(0,d.cn)("text-gray-400 hover:text-gray-600 focus:outline-none",_[b]),"aria-label":F?"Ascunde parola":"Arată parola",children:F?(0,a.jsx)(t.A,{className:_[b]}):(0,a.jsx)(l.A,{className:_[b]})}):M?(0,a.jsx)(i.A,{className:(0,d.cn)(_[b],"text-red-400")}):O?(0,a.jsx)(c.A,{className:(0,d.cn)(_[b],"text-green-400")}):w?(0,a.jsx)("span",{className:(0,d.cn)(_[b],"text-gray-400"),children:w}):null})]}),(h||y||g)&&(0,a.jsxs)("div",{className:"mt-2 flex items-start space-x-1",children:[(h||y)&&(0,a.jsx)("span",{className:"flex-shrink-0 mt-0.5",children:h?(0,a.jsx)(i.A,{className:"h-4 w-4 text-red-400"}):(0,a.jsx)(c.A,{className:"h-4 w-4 text-green-400"})}),g&&!h&&!y&&(0,a.jsx)(n.A,{className:"h-4 w-4 text-gray-400 flex-shrink-0 mt-0.5"}),(0,a.jsx)("p",{className:(0,d.cn)("text-sm",h?"text-red-600":y?"text-green-600":"text-gray-600"),children:h||y||g})]})]})});m.displayName="Input",(0,o.forwardRef)((e,s)=>(0,a.jsx)(m,{ref:s,type:"email",placeholder:"<EMAIL>",...e})).displayName="EmailInput",(0,o.forwardRef)((e,s)=>(0,a.jsx)(m,{ref:s,type:"password",placeholder:"••••••••",...e})).displayName="PasswordInput",(0,o.forwardRef)(({min:e,max:s,step:r=1,...t},l)=>(0,a.jsx)(m,{ref:l,type:"number",min:e,max:s,step:r,...t})).displayName="NumberInput",(0,o.forwardRef)(({onSearch:e,...s},r)=>(0,a.jsx)(m,{ref:r,type:"search",placeholder:"Caută...",onKeyDown:s=>{"Enter"===s.key&&e&&e(s.currentTarget.value)},...s})).displayName="SearchInput",(0,o.forwardRef)((e,s)=>(0,a.jsx)(m,{ref:s,type:"tel",placeholder:"+40 123 456 789",...e})).displayName="PhoneInput",(0,o.forwardRef)((e,s)=>(0,a.jsx)(m,{ref:s,type:"url",placeholder:"https://exemplu.com",...e})).displayName="UrlInput",(0,o.forwardRef)((e,s)=>(0,a.jsx)(m,{ref:s,type:"date",...e})).displayName="DateInput",(0,o.forwardRef)((e,s)=>(0,a.jsx)(m,{ref:s,type:"time",...e})).displayName="TimeInput",(0,o.forwardRef)((e,s)=>(0,a.jsx)(m,{ref:s,type:"datetime-local",...e})).displayName="DateTimeInput";const x=m}}]);