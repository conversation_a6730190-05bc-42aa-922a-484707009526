"use strict";(self.webpackChunkexpense_tracker_frontend=self.webpackChunkexpense_tracker_frontend||[]).push([[901],{3573:(e,s,t)=>{t.d(s,{o:()=>i});var a=t(888),r=t(2389),n=t(8114);const i=()=>{const{t:e,i18n:s}=(0,r.Bd)(),t=(0,n.UK)(),i=(0,n.qX)(),c=s=>e(`languages.${s}`,{defaultValue:s.toUpperCase()});return{t:e,i18n:s,setLanguage:async e=>{try{await(0,n.v2)(e);const s="ro"===e?"Limba a fost schimbată cu succes!":"Language changed successfully!";a.oR.success(s)}catch(e){console.error("Error changing language:",e);const s="ro"===(0,n.UK)()?"Eroare la schimbarea limbii":"Error changing language";a.oR.error(s)}},currentLanguage:t,supportedLanguages:i,isLanguageSupported:e=>i.includes(e),getLanguageDisplayName:c,getLanguagesWithNames:()=>i.map(e=>({code:e,name:c(e)})),isRTL:()=>["ar","he","fa"].includes(t)}}},9901:(e,s,t)=>{t.r(s),t.d(s,{default:()=>f});var a=t(8830),r=t(8054),n=t(3478),i=t(4266),c=t(6156),l=t(7060),d=t(5795),m=t(6540),o=t(888),g=t(7767),x=t(6103),u=t(125),h=t(2552),y=t(3573),p=t(5009),j=t(4848);const f=()=>{const e=(0,g.Zp)(),{logout:s}=(0,p.nc)(),{t,setLanguage:f,currentLanguage:v,getLanguagesWithNames:N}=(0,y.o)(),[b,k]=(0,m.useState)(!1),[w,A]=(0,m.useState)(!1),[C,Z]=(0,m.useState)({currency:"RON",language:v,theme:"light",dateFormat:"dd/mm/yyyy",notifications:{email:!0,push:!0,weekly:!0,budget:!0},privacy:{dataSharing:!1,analytics:!0,marketing:!1}}),R=[{value:"RON",label:t("currencies.RON")},{value:"EUR",label:t("currencies.EUR")},{value:"USD",label:t("currencies.USD")},{value:"GBP",label:t("currencies.GBP")}],S=N().map(e=>({value:e.code,label:e.name})),L=[{value:"light",label:t("settings.general.themes.light")},{value:"dark",label:t("settings.general.themes.dark")},{value:"auto",label:t("settings.general.themes.auto")}],Y=(e,s,t)=>{Z(a=>({...a,[e]:"object"==typeof a[e]?{...a[e],[s]:t}:t})),"language"===e&&f(t)};return(0,j.jsxs)("div",{className:"space-y-6",children:[(0,j.jsxs)("div",{children:[(0,j.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:t("settings.title")}),(0,j.jsx)("p",{className:"text-gray-600",children:t("settings.subtitle")})]}),(0,j.jsxs)(u.Ay,{className:"p-6",children:[(0,j.jsxs)("div",{className:"flex items-center mb-6",children:[(0,j.jsx)(a.A,{className:"h-6 w-6 text-gray-400 mr-3"}),(0,j.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:t("settings.general.title")})]}),(0,j.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,j.jsxs)("div",{children:[(0,j.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:t("settings.general.currency")}),(0,j.jsx)("select",{value:C.currency,onChange:e=>Y("currency",null,e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500",children:R.map(e=>(0,j.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,j.jsxs)("div",{children:[(0,j.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:t("settings.general.language")}),(0,j.jsx)("select",{value:C.language,onChange:e=>Y("language",null,e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500",children:S.map(e=>(0,j.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,j.jsxs)("div",{children:[(0,j.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:t("settings.general.theme")}),(0,j.jsx)("select",{value:C.theme,onChange:e=>Y("theme",null,e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500",children:L.map(e=>(0,j.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,j.jsxs)("div",{children:[(0,j.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:t("settings.general.dateFormat")}),(0,j.jsx)("select",{value:C.dateFormat,onChange:e=>Y("dateFormat",null,e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500",children:[{value:"dd/mm/yyyy",label:"DD/MM/YYYY"},{value:"mm/dd/yyyy",label:"MM/DD/YYYY"},{value:"yyyy-mm-dd",label:"YYYY-MM-DD"}].map(e=>(0,j.jsx)("option",{value:e.value,children:e.label},e.value))})]})]})]}),(0,j.jsxs)(u.Ay,{className:"p-6",children:[(0,j.jsxs)("div",{className:"flex items-center mb-6",children:[(0,j.jsx)(r.A,{className:"h-6 w-6 text-gray-400 mr-3"}),(0,j.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:t("settings.notifications.title")})]}),(0,j.jsxs)("div",{className:"space-y-4",children:[(0,j.jsxs)("div",{className:"flex items-center justify-between",children:[(0,j.jsxs)("div",{children:[(0,j.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"Notificări email"}),(0,j.jsx)("p",{className:"text-sm text-gray-500",children:"Primește notificări prin email"})]}),(0,j.jsx)("input",{type:"checkbox",checked:C.notifications.email,onChange:e=>Y("notifications","email",e.target.checked),className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"})]}),(0,j.jsxs)("div",{className:"flex items-center justify-between",children:[(0,j.jsxs)("div",{children:[(0,j.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"Notificări push"}),(0,j.jsx)("p",{className:"text-sm text-gray-500",children:"Primește notificări în browser"})]}),(0,j.jsx)("input",{type:"checkbox",checked:C.notifications.push,onChange:e=>Y("notifications","push",e.target.checked),className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"})]}),(0,j.jsxs)("div",{className:"flex items-center justify-between",children:[(0,j.jsxs)("div",{children:[(0,j.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"Rapoarte săptămânale"}),(0,j.jsx)("p",{className:"text-sm text-gray-500",children:"Primește un rezumat săptămânal"})]}),(0,j.jsx)("input",{type:"checkbox",checked:C.notifications.weekly,onChange:e=>Y("notifications","weekly",e.target.checked),className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"})]}),(0,j.jsxs)("div",{className:"flex items-center justify-between",children:[(0,j.jsxs)("div",{children:[(0,j.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:"Alerte buget"}),(0,j.jsx)("p",{className:"text-sm text-gray-500",children:"Primește alerte când depășești bugetul"})]}),(0,j.jsx)("input",{type:"checkbox",checked:C.notifications.budget,onChange:e=>Y("notifications","budget",e.target.checked),className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"})]})]})]}),(0,j.jsxs)(u.Ay,{className:"p-6",children:[(0,j.jsxs)("div",{className:"flex items-center mb-6",children:[(0,j.jsx)(n.A,{className:"h-6 w-6 text-gray-400 mr-3"}),(0,j.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:t("settings.privacy.title")})]}),(0,j.jsxs)("div",{className:"space-y-4",children:[(0,j.jsxs)("div",{className:"flex items-center justify-between",children:[(0,j.jsxs)("div",{children:[(0,j.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:t("settings.privacy.dataSharing.title")}),(0,j.jsx)("p",{className:"text-sm text-gray-500",children:t("settings.privacy.dataSharing.description")})]}),(0,j.jsx)("input",{type:"checkbox",checked:C.privacy.dataSharing,onChange:e=>Y("privacy","dataSharing",e.target.checked),className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"})]}),(0,j.jsxs)("div",{className:"flex items-center justify-between",children:[(0,j.jsxs)("div",{children:[(0,j.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:t("settings.privacy.analytics.title")}),(0,j.jsx)("p",{className:"text-sm text-gray-500",children:t("settings.privacy.analytics.description")})]}),(0,j.jsx)("input",{type:"checkbox",checked:C.privacy.analytics,onChange:e=>Y("privacy","analytics",e.target.checked),className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"})]}),(0,j.jsxs)("div",{className:"flex items-center justify-between",children:[(0,j.jsxs)("div",{children:[(0,j.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:t("settings.privacy.marketing.title")}),(0,j.jsx)("p",{className:"text-sm text-gray-500",children:t("settings.privacy.marketing.description")})]}),(0,j.jsx)("input",{type:"checkbox",checked:C.privacy.marketing,onChange:e=>Y("privacy","marketing",e.target.checked),className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"})]})]})]}),(0,j.jsxs)(u.Ay,{className:"p-6",children:[(0,j.jsxs)("div",{className:"flex items-center mb-6",children:[(0,j.jsx)(i.A,{className:"h-6 w-6 text-gray-400 mr-3"}),(0,j.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:t("settings.export.title")})]}),(0,j.jsxs)("div",{className:"flex items-center justify-between",children:[(0,j.jsxs)("div",{children:[(0,j.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:t("settings.export.allData")}),(0,j.jsx)("p",{className:"text-sm text-gray-500",children:t("settings.export.description")})]}),(0,j.jsx)(x.Ay,{variant:"outline",onClick:async()=>{try{await new Promise(e=>setTimeout(e,2e3)),o.oR.success(t("settings.export.success"))}catch(e){o.oR.error(t("settings.export.error"))}},children:t("settings.export.button")})]})]}),(0,j.jsxs)(u.Ay,{className:"p-6",children:[(0,j.jsxs)("div",{className:"flex items-center mb-6",children:[(0,j.jsx)(c.A,{className:"h-6 w-6 text-red-500 mr-3"}),(0,j.jsx)("h2",{className:"text-lg font-medium text-gray-900",children:t("settings.dangerZone.title")})]}),(0,j.jsxs)("div",{className:"space-y-4",children:[(0,j.jsxs)("div",{className:"flex items-center justify-between py-4 border-b border-gray-200",children:[(0,j.jsxs)("div",{children:[(0,j.jsx)("h3",{className:"text-sm font-medium text-gray-900",children:t("settings.dangerZone.logout.title")}),(0,j.jsx)("p",{className:"text-sm text-gray-500",children:t("settings.dangerZone.logout.description")})]}),(0,j.jsxs)(x.Ay,{variant:"outline",onClick:()=>A(!0),className:"flex items-center",children:[(0,j.jsx)(l.A,{className:"h-4 w-4 mr-2"}),t("settings.dangerZone.logout.button")]})]}),(0,j.jsxs)("div",{className:"flex items-center justify-between pt-4",children:[(0,j.jsxs)("div",{children:[(0,j.jsx)("h3",{className:"text-sm font-medium text-red-900",children:t("settings.dangerZone.deleteAccount.title")}),(0,j.jsx)("p",{className:"text-sm text-red-500",children:t("settings.dangerZone.deleteAccount.description")})]}),(0,j.jsxs)(x.Ay,{variant:"danger",onClick:()=>k(!0),className:"flex items-center",children:[(0,j.jsx)(d.A,{className:"h-4 w-4 mr-2"}),t("settings.dangerZone.deleteAccount.button")]})]})]})]}),(0,j.jsx)("div",{className:"flex justify-end",children:(0,j.jsx)(x.Ay,{variant:"primary",onClick:async()=>{try{await new Promise(e=>setTimeout(e,1e3)),o.oR.success(t("settings.saveSuccess"))}catch(e){o.oR.error(t("settings.saveError"))}},children:t("settings.saveButton")})}),(0,j.jsx)(h.Ay,{isOpen:w,onClose:()=>A(!1),title:t("settings.dangerZone.logout.confirmTitle"),children:(0,j.jsxs)("div",{className:"space-y-4",children:[(0,j.jsx)("p",{className:"text-gray-600",children:t("settings.dangerZone.logout.confirmMessage")}),(0,j.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,j.jsx)(x.Ay,{variant:"outline",onClick:()=>A(!1),children:t("common.cancel")}),(0,j.jsx)(x.Ay,{variant:"primary",onClick:async()=>{try{await s(),e("/auth/login"),o.oR.success(t("auth.logoutSuccess"))}catch(e){o.oR.error(t("auth.loginError"))}A(!1)},children:t("settings.dangerZone.logout.confirmButton")})]})]})}),(0,j.jsx)(h.Ay,{isOpen:b,onClose:()=>k(!1),title:t("settings.dangerZone.deleteAccount.confirmTitle"),children:(0,j.jsxs)("div",{className:"space-y-4",children:[(0,j.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-4",children:(0,j.jsxs)("div",{className:"flex",children:[(0,j.jsx)(d.A,{className:"h-5 w-5 text-red-400 mr-2"}),(0,j.jsxs)("div",{children:[(0,j.jsx)("h3",{className:"text-sm font-medium text-red-800",children:t("settings.dangerZone.deleteAccount.warningTitle")}),(0,j.jsx)("p",{className:"text-sm text-red-700 mt-1",children:t("settings.dangerZone.deleteAccount.warningMessage")})]})]})}),(0,j.jsx)("p",{className:"text-gray-600",children:t("settings.dangerZone.deleteAccount.confirmInstructions")}),(0,j.jsx)("input",{type:"text",placeholder:t("settings.dangerZone.deleteAccount.confirmPlaceholder"),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-red-500 focus:border-red-500"}),(0,j.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,j.jsx)(x.Ay,{variant:"outline",onClick:()=>k(!1),children:t("common.cancel")}),(0,j.jsx)(x.Ay,{variant:"danger",onClick:async()=>{try{await new Promise(e=>setTimeout(e,1e3)),await s(),e("/auth/login"),o.oR.success(t("settings.dangerZone.deleteAccount.success"))}catch(e){o.oR.error(t("settings.dangerZone.deleteAccount.error"))}k(!1)},children:t("settings.dangerZone.deleteAccount.confirmButton")})]})]})})]})}}}]);