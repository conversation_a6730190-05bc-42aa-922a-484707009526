import { Request, Response, NextFunction } from 'express';
interface AuthenticatedRequest extends Request {
    user?: {
        id: number;
        email: string;
        name: string;
        [key: string]: any;
    };
}
interface JwtPayload {
    userId: number;
    email: string;
    iat?: number;
    exp?: number;
}
export declare const authenticateToken: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const generateToken: (userId: number) => string;
export declare const generateRefreshToken: (userId: number) => string;
export declare const verifyRefreshToken: (token: string) => JwtPayload;
export default authenticateToken;
export type { AuthenticatedRequest };
//# sourceMappingURL=auth.d.ts.map