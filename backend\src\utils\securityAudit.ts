import { PrismaClient } from '@prisma/client';
import { logger } from './logger';
import crypto from 'crypto';
import fs from 'fs/promises';
import path from 'path';

const prisma = new PrismaClient();

interface SecurityEvent {
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  ip?: string;
  userId?: number;
  userAgent?: string;
  path?: string;
  data?: any;
  timestamp: Date;
}

interface AuditReport {
  id: string;
  generatedAt: Date;
  summary: {
    totalEvents: number;
    criticalEvents: number;
    highEvents: number;
    mediumEvents: number;
    lowEvents: number;
  };
  events: SecurityEvent[];
  recommendations: string[];
}

class SecurityAuditService {
  private auditLogPath: string;

  constructor() {
    this.auditLogPath = path.join(process.cwd(), 'logs', 'security-audit.log');
  }

  /**
   * Înregistrează un eveniment de securitate
   */
  async logSecurityEvent(event: Omit<SecurityEvent, 'timestamp'>): Promise<void> {
    const securityEvent: SecurityEvent = {
      ...event,
      timestamp: new Date(),
    };

    try {
      // Salvează în baza de date
      await prisma.securityLog.create({
        data: {
          type: event.type,
          severity: event.severity,
          description: event.description,
          ip: event.ip,
          user_id: event.userId,
          user_agent: event.userAgent,
          path: event.path,
          data: event.data ? JSON.stringify(event.data) : null,
          created_at: securityEvent.timestamp,
        },
      });

      // Salvează în fișierul de log
      await this.writeToAuditLog(securityEvent);

      // Alertă pentru evenimente critice
      if (event.severity === 'critical') {
        await this.sendCriticalAlert(securityEvent);
      }

      logger.info('Security event logged', {
        type: event.type,
        severity: event.severity,
        ip: event.ip,
        userId: event.userId,
      });
    } catch (error) {
      logger.error('Failed to log security event', error);
    }
  }

  /**
   * Generează un raport de audit de securitate
   */
  async generateAuditReport(
    startDate: Date = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // Last 7 days
    endDate: Date = new Date()
  ): Promise<AuditReport> {
    try {
      const events = await prisma.securityLog.findMany({
        where: {
          created_at: {
            gte: startDate,
            lte: endDate,
          },
        },
        orderBy: {
          created_at: 'desc',
        },
      });

      const mappedEvents: SecurityEvent[] = events.map(event => ({
        type: event.type,
        severity: event.severity as SecurityEvent['severity'],
        description: event.description,
        ip: event.ip || undefined,
        userId: event.user_id || undefined,
        userAgent: event.user_agent || undefined,
        path: event.path || undefined,
        data: event.data ? JSON.parse(event.data) : undefined,
        timestamp: event.created_at,
      }));

      const summary = {
        totalEvents: events.length,
        criticalEvents: events.filter(e => e.severity === 'critical').length,
        highEvents: events.filter(e => e.severity === 'high').length,
        mediumEvents: events.filter(e => e.severity === 'medium').length,
        lowEvents: events.filter(e => e.severity === 'low').length,
      };

      const recommendations = this.generateRecommendations(mappedEvents);

      const report: AuditReport = {
        id: crypto.randomUUID(),
        generatedAt: new Date(),
        summary,
        events: mappedEvents,
        recommendations,
      };

      // Salvează raportul
      await this.saveAuditReport(report);

      return report;
    } catch (error) {
      logger.error('Failed to generate audit report', error);
      throw error;
    }
  }

  /**
   * Analizează pattern-uri suspecte
   */
  async analyzeSuspiciousPatterns(): Promise<{
    suspiciousIPs: string[];
    repeatedFailedLogins: any[];
    unusualUserAgents: string[];
    sqlInjectionAttempts: number;
  }> {
    try {
      const last24Hours = new Date(Date.now() - 24 * 60 * 60 * 1000);

      // IP-uri suspecte (multe evenimente de securitate)
      const suspiciousIPs = await prisma.securityLog.groupBy({
        by: ['ip'],
        where: {
          created_at: { gte: last24Hours },
          severity: { in: ['high', 'critical'] },
        },
        having: {
          ip: { _count: { gt: 10 } },
        },
        _count: { ip: true },
      });

      // Încercări repetate de login eșuate
      const repeatedFailedLogins = await prisma.securityLog.groupBy({
        by: ['ip', 'user_id'],
        where: {
          created_at: { gte: last24Hours },
          type: 'failed_login',
        },
        having: {
          ip: { _count: { gt: 5 } },
        },
        _count: { ip: true },
      });

      // User agents neobișnuiți
      const unusualUserAgents = await prisma.securityLog.findMany({
        where: {
          created_at: { gte: last24Hours },
          user_agent: {
            not: {
              contains: 'Mozilla',
            },
          },
        },
        select: { user_agent: true },
        distinct: ['user_agent'],
      });

      // Numărul de încercări de SQL injection
      const sqlInjectionAttempts = await prisma.securityLog.count({
        where: {
          created_at: { gte: last24Hours },
          type: 'sql_injection_attempt',
        },
      });

      return {
        suspiciousIPs: suspiciousIPs.map(item => item.ip).filter(Boolean) as string[],
        repeatedFailedLogins,
        unusualUserAgents: unusualUserAgents.map(item => item.user_agent).filter(Boolean) as string[],
        sqlInjectionAttempts,
      };
    } catch (error) {
      logger.error('Failed to analyze suspicious patterns', error);
      throw error;
    }
  }

  /**
   * Verifică integritatea sistemului
   */
  async checkSystemIntegrity(): Promise<{
    configurationIssues: string[];
    vulnerabilities: string[];
    recommendations: string[];
  }> {
    const issues: string[] = [];
    const vulnerabilities: string[] = [];
    const recommendations: string[] = [];

    try {
      // Verifică configurările de securitate
      if (!process.env.JWT_SECRET || process.env.JWT_SECRET.length < 32) {
        issues.push('JWT secret is too short or missing');
        recommendations.push('Use a strong JWT secret with at least 32 characters');
      }

      if (!process.env.BCRYPT_ROUNDS || parseInt(process.env.BCRYPT_ROUNDS) < 12) {
        issues.push('BCrypt rounds too low');
        recommendations.push('Use at least 12 BCrypt rounds for password hashing');
      }

      if (process.env.NODE_ENV === 'production' && !process.env.HTTPS_ONLY) {
        vulnerabilities.push('HTTPS not enforced in production');
        recommendations.push('Enable HTTPS-only mode in production');
      }

      // Verifică dependențele vulnerabile
      const packageJson = require('../../package.json');
      if (packageJson.dependencies) {
        // Aici ar trebui să verifici cu o bază de date de vulnerabilități
        // Pentru demonstrație, verificăm versiuni vechi
        const oldDependencies = Object.entries(packageJson.dependencies)
          .filter(([name, version]) => {
            // Verificări simple pentru versiuni vechi
            if (name === 'express' && (version as string).startsWith('^4.17')) {
              return true;
            }
            return false;
          });

        if (oldDependencies.length > 0) {
          vulnerabilities.push(`Outdated dependencies: ${oldDependencies.map(([name]) => name).join(', ')}`);
          recommendations.push('Update dependencies to latest secure versions');
        }
      }

      // Verifică permisiunile fișierelor
      try {
        const stats = await fs.stat(this.auditLogPath);
        if (stats.mode & 0o077) {
          issues.push('Audit log file has overly permissive permissions');
          recommendations.push('Restrict audit log file permissions to owner only');
        }
      } catch (error) {
        // Fișierul nu există încă
      }

      return {
        configurationIssues: issues,
        vulnerabilities,
        recommendations,
      };
    } catch (error) {
      logger.error('Failed to check system integrity', error);
      throw error;
    }
  }

  /**
   * Scrie evenimentul în fișierul de audit
   */
  private async writeToAuditLog(event: SecurityEvent): Promise<void> {
    try {
      const logEntry = JSON.stringify({
        timestamp: event.timestamp.toISOString(),
        type: event.type,
        severity: event.severity,
        description: event.description,
        ip: event.ip,
        userId: event.userId,
        userAgent: event.userAgent,
        path: event.path,
        data: event.data,
      }) + '\n';

      await fs.appendFile(this.auditLogPath, logEntry, 'utf8');
    } catch (error) {
      logger.error('Failed to write to audit log', error);
    }
  }

  /**
   * Trimite alertă pentru evenimente critice
   */
  private async sendCriticalAlert(event: SecurityEvent): Promise<void> {
    try {
      // Aici ai putea integra cu servicii de alertă (email, Slack, etc.)
      logger.error('CRITICAL SECURITY EVENT', {
        type: event.type,
        description: event.description,
        ip: event.ip,
        userId: event.userId,
        timestamp: event.timestamp,
      });

      // Exemplu de integrare cu webhook Slack
      if (process.env.SLACK_WEBHOOK_URL) {
        // Implementează trimiterea către Slack
      }
    } catch (error) {
      logger.error('Failed to send critical alert', error);
    }
  }

  /**
   * Generează recomandări bazate pe evenimente
   */
  private generateRecommendations(events: SecurityEvent[]): string[] {
    const recommendations: string[] = [];

    const criticalCount = events.filter(e => e.severity === 'critical').length;
    const highCount = events.filter(e => e.severity === 'high').length;
    const sqlInjectionCount = events.filter(e => e.type === 'sql_injection_attempt').length;
    const failedLoginCount = events.filter(e => e.type === 'failed_login').length;

    if (criticalCount > 0) {
      recommendations.push('Investigate and address all critical security events immediately');
    }

    if (highCount > 10) {
      recommendations.push('Review and strengthen security measures - high number of security events detected');
    }

    if (sqlInjectionCount > 0) {
      recommendations.push('Implement additional SQL injection protection and review input validation');
    }

    if (failedLoginCount > 50) {
      recommendations.push('Consider implementing CAPTCHA or additional authentication measures');
    }

    // Analizează IP-uri suspecte
    const ipCounts = events.reduce((acc, event) => {
      if (event.ip) {
        acc[event.ip] = (acc[event.ip] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    const suspiciousIPs = Object.entries(ipCounts)
      .filter(([, count]) => count > 20)
      .map(([ip]) => ip);

    if (suspiciousIPs.length > 0) {
      recommendations.push(`Consider blocking or monitoring IPs: ${suspiciousIPs.join(', ')}`);
    }

    return recommendations;
  }

  /**
   * Salvează raportul de audit
   */
  private async saveAuditReport(report: AuditReport): Promise<void> {
    try {
      const reportPath = path.join(process.cwd(), 'logs', `audit-report-${report.id}.json`);
      await fs.writeFile(reportPath, JSON.stringify(report, null, 2), 'utf8');
      
      logger.info('Audit report saved', {
        reportId: report.id,
        path: reportPath,
        totalEvents: report.summary.totalEvents,
      });
    } catch (error) {
      logger.error('Failed to save audit report', error);
    }
  }
}

export const securityAudit = new SecurityAuditService();
export { SecurityEvent, AuditReport };
