name: Release Pipeline

on:
  release:
    types: [published]
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to release (e.g., v1.0.0)'
        required: true
        type: string

env:
  NODE_VERSION: '18'

jobs:
  # Job pentru validarea release-ului
  validate-release:
    name: Validate Release
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: |
          cd backend && npm ci
          cd ../frontend && npm ci

      - name: Validate package versions
        run: |
          BACKEND_VERSION=$(node -p "require('./backend/package.json').version")
          FRONTEND_VERSION=$(node -p "require('./frontend/package.json').version")
          
          echo "Backend version: $BACKEND_VERSION"
          echo "Frontend version: $FRONTEND_VERSION"
          
          if [ "$BACKEND_VERSION" != "$FRONTEND_VERSION" ]; then
            echo "Error: Backend and frontend versions don't match!"
            exit 1
          fi

      - name: Run full test suite
        run: |
          cd backend && npm run test:coverage
          cd ../frontend && npm run test:coverage

      - name: Build applications
        run: |
          cd backend && npm run build
          cd ../frontend && npm run build

  # Job pentru crearea Docker images
  build-docker-images:
    name: Build Docker Images
    runs-on: ubuntu-latest
    needs: [validate-release]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Extract version
        id: version
        run: |
          if [ "${{ github.event_name }}" = "release" ]; then
            VERSION=${{ github.event.release.tag_name }}
          else
            VERSION=${{ github.event.inputs.version }}
          fi
          echo "version=${VERSION#v}" >> $GITHUB_OUTPUT

      - name: Build and push backend image
        uses: docker/build-push-action@v5
        with:
          context: ./backend
          file: ./backend/Dockerfile
          push: true
          tags: |
            ${{ secrets.DOCKER_USERNAME }}/expense-tracker-api:latest
            ${{ secrets.DOCKER_USERNAME }}/expense-tracker-api:${{ steps.version.outputs.version }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Build and push frontend image
        uses: docker/build-push-action@v5
        with:
          context: ./frontend
          file: ./frontend/Dockerfile
          push: true
          tags: |
            ${{ secrets.DOCKER_USERNAME }}/expense-tracker-web:latest
            ${{ secrets.DOCKER_USERNAME }}/expense-tracker-web:${{ steps.version.outputs.version }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # Job pentru deployment în producție
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [build-docker-images]
    environment: production

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Deploy frontend to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          working-directory: ./frontend
          vercel-args: '--prod'

      - name: Deploy backend to Railway
        uses: bervProject/railway-deploy@v1.2.0
        with:
          railway_token: ${{ secrets.RAILWAY_TOKEN }}
          service: 'expense-tracker-api'

      - name: Run database migrations
        working-directory: ./backend
        run: npx prisma migrate deploy
        env:
          DATABASE_URL: ${{ secrets.DATABASE_URL }}

      - name: Warm up application
        run: |
          echo "Warming up application endpoints..."
          curl -f ${{ secrets.API_BASE_URL }}/health || exit 1
          curl -f ${{ secrets.FRONTEND_URL }} || exit 1

  # Job pentru smoke tests în producție
  smoke-tests:
    name: Production Smoke Tests
    runs-on: ubuntu-latest
    needs: [deploy-production]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Install test dependencies
        run: npm install -g newman

      - name: Run API smoke tests
        run: |
          newman run tests/postman/smoke-tests.json \
            --env-var "base_url=${{ secrets.API_BASE_URL }}" \
            --reporters cli,json \
            --reporter-json-export smoke-test-results.json

      - name: Run frontend smoke tests
        run: |
          npx playwright test tests/e2e/smoke.spec.ts \
            --config=playwright.config.production.ts

      - name: Upload test results
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: smoke-test-results
          path: |
            smoke-test-results.json
            test-results/
          retention-days: 30

  # Job pentru notificări și cleanup
  post-deployment:
    name: Post Deployment Tasks
    runs-on: ubuntu-latest
    needs: [smoke-tests]
    if: always()

    steps:
      - name: Notify success
        if: needs.smoke-tests.result == 'success'
        uses: 8398a7/action-slack@v3
        with:
          status: success
          channel: '#releases'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
          message: |
            🎉 Release deployed successfully!
            Version: ${{ github.event.release.tag_name || github.event.inputs.version }}
            Frontend: ${{ secrets.FRONTEND_URL }}
            API: ${{ secrets.API_BASE_URL }}

      - name: Notify failure
        if: needs.smoke-tests.result == 'failure'
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          channel: '#releases'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
          message: |
            ❌ Release deployment failed!
            Version: ${{ github.event.release.tag_name || github.event.inputs.version }}
            Please check the logs and consider rollback.

      - name: Create GitHub deployment status
        uses: actions/github-script@v6
        with:
          script: |
            const deployment = await github.rest.repos.createDeployment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              ref: context.sha,
              environment: 'production',
              auto_merge: false,
              required_contexts: []
            });

            await github.rest.repos.createDeploymentStatus({
              owner: context.repo.owner,
              repo: context.repo.repo,
              deployment_id: deployment.data.id,
              state: '${{ needs.smoke-tests.result == 'success' && 'success' || 'failure' }}',
              environment_url: '${{ secrets.FRONTEND_URL }}',
              description: 'Deployment ${{ needs.smoke-tests.result == 'success' && 'completed successfully' || 'failed' }}'
            });

      - name: Update release notes
        if: github.event_name == 'release' && needs.smoke-tests.result == 'success'
        uses: actions/github-script@v6
        with:
          script: |
            const release = await github.rest.repos.getRelease({
              owner: context.repo.owner,
              repo: context.repo.repo,
              release_id: ${{ github.event.release.id }}
            });

            const updatedBody = release.data.body + '\n\n---\n\n✅ **Deployment Status**: Successfully deployed to production\n📅 **Deployed at**: ' + new Date().toISOString();

            await github.rest.repos.updateRelease({
              owner: context.repo.owner,
              repo: context.repo.repo,
              release_id: ${{ github.event.release.id }},
              body: updatedBody
            });

  # Job pentru rollback în caz de eșec
  rollback:
    name: Rollback on Failure
    runs-on: ubuntu-latest
    needs: [smoke-tests]
    if: failure() && github.event_name == 'release'

    steps:
      - name: Trigger rollback
        run: |
          echo "Triggering rollback procedures..."
          # Add rollback logic here
          
      - name: Notify rollback
        uses: 8398a7/action-slack@v3
        with:
          status: custom
          custom_payload: |
            {
              "text": "🔄 Rollback initiated for failed release",
              "attachments": [
                {
                  "color": "warning",
                  "fields": [
                    {
                      "title": "Version",
                      "value": "${{ github.event.release.tag_name }}",
                      "short": true
                    },
                    {
                      "title": "Status",
                      "value": "Rolling back to previous version",
                      "short": true
                    }
                  ]
                }
              ]
            }
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
