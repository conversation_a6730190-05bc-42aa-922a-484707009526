import { Request, Response } from 'express';
interface AuthenticatedRequest extends Request {
    user?: any;
    userId?: number;
}
export declare const expenseController: {
    getExpenses: (req: AuthenticatedRequest, res: Response) => Promise<void>;
    getExpense: (req: AuthenticatedRequest, res: Response) => Promise<void>;
    createExpense: (req: AuthenticatedRequest, res: Response) => Promise<void>;
    updateExpense: (req: AuthenticatedRequest, res: Response) => Promise<void>;
    deleteExpense: (req: AuthenticatedRequest, res: Response) => Promise<void>;
    getExpenseStats: (req: AuthenticatedRequest, res: Response) => Promise<void>;
    getMonthlyTrends: (req: AuthenticatedRequest, res: Response) => Promise<void>;
    addTag: (req: AuthenticatedRequest, res: Response) => Promise<void>;
    removeTag: (req: AuthenticatedRequest, res: Response) => Promise<void>;
    getPopularTags: (req: AuthenticatedRequest, res: Response) => Promise<void>;
    bulkDeleteExpenses: (req: AuthenticatedRequest, res: Response) => Promise<void>;
};
export {};
//# sourceMappingURL=expenseController.d.ts.map