"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const prisma_1 = require("../config/prisma");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
router.get('/profile', auth_1.authenticateToken, async (req, res) => {
    try {
        const user = await prisma_1.prisma.user.findUnique({
            where: { id: req.user.id },
            select: {
                id: true,
                email: true,
                name: true,
                currency: true,
                timezone: true,
                created_at: true,
                updated_at: true
            }
        });
        if (!user) {
            res.status(404).json({ error: 'User not found' });
            return;
        }
        res.json(user);
    }
    catch (error) {
        console.error('Get profile error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
router.put('/profile', [
    auth_1.authenticateToken,
    (0, express_validator_1.body)('firstName')
        .optional()
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('First name must be between 2 and 50 characters'),
    (0, express_validator_1.body)('lastName')
        .optional()
        .trim()
        .isLength({ min: 2, max: 50 })
        .withMessage('Last name must be between 2 and 50 characters'),
    (0, express_validator_1.body)('email')
        .optional()
        .isEmail()
        .normalizeEmail()
        .withMessage('Please provide a valid email'),
    (0, express_validator_1.body)('currency')
        .optional()
        .isIn(['RON', 'EUR', 'USD', 'GBP'])
        .withMessage('Currency must be one of: RON, EUR, USD, GBP'),
    (0, express_validator_1.body)('timezone')
        .optional()
        .isString()
        .withMessage('Timezone must be a valid string')
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            res.status(400).json({
                error: 'Validation failed',
                details: errors.array()
            });
            return;
        }
        const { firstName, lastName, email, currency, timezone } = req.body;
        if (email) {
            const existingUser = await prisma_1.prisma.user.findFirst({
                where: {
                    email,
                    NOT: {
                        id: req.user.id
                    }
                }
            });
            if (existingUser) {
                res.status(400).json({ error: 'Email already in use' });
                return;
            }
        }
        const user = await prisma_1.prisma.user.findUnique({
            where: { id: req.user.id }
        });
        if (!user) {
            res.status(404).json({ error: 'User not found' });
            return;
        }
        const updateData = {};
        if (firstName !== undefined || lastName !== undefined) {
            const fullName = `${firstName || ''} ${lastName || ''}`.trim();
            if (fullName)
                updateData.name = fullName;
        }
        if (email !== undefined)
            updateData.email = email;
        if (currency !== undefined)
            updateData.currency = currency;
        if (timezone !== undefined)
            updateData.timezone = timezone;
        await prisma_1.prisma.user.update({
            where: { id: req.user.id },
            data: updateData
        });
        const updatedUser = await prisma_1.prisma.user.findUnique({
            where: { id: req.user.id },
            select: {
                id: true,
                email: true,
                name: true,
                currency: true,
                timezone: true,
                created_at: true,
                updated_at: true
            }
        });
        res.json({
            message: 'Profile updated successfully',
            user: updatedUser
        });
    }
    catch (error) {
        console.error('Update profile error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
router.put('/password', [
    auth_1.authenticateToken,
    (0, express_validator_1.body)('currentPassword')
        .notEmpty()
        .withMessage('Current password is required'),
    (0, express_validator_1.body)('newPassword')
        .isLength({ min: 6 })
        .withMessage('New password must be at least 6 characters long')
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
        .withMessage('New password must contain at least one lowercase letter, one uppercase letter, and one number')
], async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            res.status(400).json({
                error: 'Validation failed',
                details: errors.array()
            });
            return;
        }
        const { currentPassword, newPassword } = req.body;
        const user = await prisma_1.prisma.user.findUnique({
            where: { id: req.user.id }
        });
        if (!user) {
            res.status(404).json({ error: 'User not found' });
            return;
        }
        const isCurrentPasswordValid = await bcryptjs_1.default.compare(currentPassword, user.password);
        if (!isCurrentPasswordValid) {
            res.status(400).json({ error: 'Current password is incorrect' });
            return;
        }
        const saltRounds = parseInt(process.env.BCRYPT_ROUNDS || '12');
        const hashedNewPassword = await bcryptjs_1.default.hash(newPassword, saltRounds);
        await prisma_1.prisma.user.update({
            where: { id: req.user.id },
            data: { password: hashedNewPassword }
        });
        res.json({ message: 'Password updated successfully' });
    }
    catch (error) {
        console.error('Change password error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
router.delete('/account', auth_1.authenticateToken, async (req, res) => {
    try {
        const user = await prisma_1.prisma.user.findUnique({
            where: { id: req.user.id }
        });
        if (!user) {
            res.status(404).json({ error: 'User not found' });
            return;
        }
        await prisma_1.prisma.user.delete({
            where: { id: req.user.id }
        });
        res.json({ message: 'Account deleted successfully' });
    }
    catch (error) {
        console.error('Delete account error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
router.get('/stats', auth_1.authenticateToken, async (req, res) => {
    try {
        const userId = req.user.id;
        const totalExpenses = await prisma_1.prisma.expense.count({
            where: { user_id: userId }
        });
        const totalAmountResult = await prisma_1.prisma.expense.aggregate({
            where: { user_id: userId },
            _sum: { amount: true }
        });
        const totalAmount = totalAmountResult._sum?.amount || 0;
        const categoriesCount = await prisma_1.prisma.category.count({
            where: { user_id: userId }
        });
        const currentMonth = new Date();
        const startOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
        const endOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0);
        const monthlyExpensesResult = await prisma_1.prisma.expense.aggregate({
            where: {
                user_id: userId,
                date: {
                    gte: startOfMonth,
                    lte: endOfMonth
                }
            },
            _sum: { amount: true }
        });
        const monthlyExpenses = monthlyExpensesResult._sum?.amount || 0;
        res.json({
            totalExpenses,
            totalAmount,
            categoriesCount,
            monthlyExpenses,
            currency: req.user.currency || 'USD'
        });
    }
    catch (error) {
        console.error('Get user stats error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});
exports.default = router;
//# sourceMappingURL=users.js.map