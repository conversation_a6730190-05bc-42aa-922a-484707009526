{"compilerOptions": {"target": "ES2022", "lib": ["DOM", "DOM.Iterable", "ES2022"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "exactOptionalPropertyTypes": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "importHelpers": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/components/*": ["src/components/*"], "@/hooks/*": ["src/hooks/*"], "@/services/*": ["src/services/*"], "@/utils/*": ["src/utils/*"], "@/types/*": ["src/types/*"], "@/store/*": ["src/store/*"], "@/config/*": ["src/config/*"], "@/assets/*": ["src/assets/*"]}, "types": ["vitest/globals", "node"]}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.d.ts"], "exclude": ["node_modules", "dist", "build", "**/*.js", "**/*.jsx"]}