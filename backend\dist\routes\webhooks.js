"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const subscriptionController_1 = __importDefault(require("../controllers/subscriptionController"));
const logger_1 = __importDefault(require("../utils/logger"));
const router = express_1.default.Router();
router.post('/stripe', express_1.default.raw({ type: 'application/json' }), async (req, res) => {
    try {
        const signature = req.headers['stripe-signature'];
        const payload = req.body;
        if (!signature) {
            logger_1.default.warn('Missing Stripe signature in webhook request');
            return res.status(400).json({
                success: false,
                message: 'Missing Stripe signature'
            });
        }
        return await subscriptionController_1.default.handleWebhook(req, res);
    }
    catch (error) {
        logger_1.default.error('Error in Stripe webhook:', error);
        res.status(400).json({
            success: false,
            message: 'Webhook processing failed',
            error: error.message
        });
    }
});
router.get('/health', (req, res) => {
    res.json({
        success: true,
        message: 'Webhook endpoint is healthy',
        timestamp: new Date().toISOString()
    });
});
exports.default = router;
//# sourceMappingURL=webhooks.js.map