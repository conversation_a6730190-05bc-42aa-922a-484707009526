import { Request, Response } from 'express';
interface AuthenticatedRequest extends Request {
    user?: any;
    userId?: number;
}
declare const getCategories: (req: AuthenticatedRequest, res: Response) => Promise<void>;
declare const getCategory: (req: AuthenticatedRequest, res: Response) => Promise<void>;
declare const createCategory: (req: AuthenticatedRequest, res: Response) => Promise<void>;
declare const updateCategory: (req: AuthenticatedRequest, res: Response) => Promise<void>;
declare const deleteCategory: (req: AuthenticatedRequest, res: Response) => Promise<void>;
declare const getCategoryStats: (req: AuthenticatedRequest, res: Response) => Promise<void>;
declare const getCategoriesWithStats: (req: AuthenticatedRequest, res: Response) => Promise<void>;
declare const reorderCategories: (req: AuthenticatedRequest, res: Response) => Promise<void>;
declare const setDefaultCategory: (req: AuthenticatedRequest, res: Response) => Promise<void>;
export { getCategories, getCategory, createCategory, updateCategory, deleteCategory, getCategoryStats, getCategoriesWithStats, reorderCategories, setDefaultCategory, };
//# sourceMappingURL=categoryController.d.ts.map