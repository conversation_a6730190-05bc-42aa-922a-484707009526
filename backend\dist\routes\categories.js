"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const categoryController_1 = require("../controllers/categoryController");
const auth_1 = require("../middleware/auth");
const validation_1 = require("../middleware/validation");
const subscriptionLimits_1 = require("../middleware/subscriptionLimits");
const router = express_1.default.Router();
router.get('/', auth_1.authenticateToken, categoryController_1.getCategories);
router.get('/stats', auth_1.authenticateToken, categoryController_1.getCategoriesWithStats);
router.get('/:id', auth_1.authenticateToken, (0, validation_1.validate)(validation_1.paramSchemas.id, 'params'), categoryController_1.getCategory);
router.get('/:id/stats', auth_1.authenticateToken, (0, validation_1.validate)(validation_1.paramSchemas.id, 'params'), categoryController_1.getCategoryStats);
router.post('/', auth_1.authenticateToken, subscriptionLimits_1.checkCategoryLimit, (0, validation_1.validate)(validation_1.categorySchemas.create), categoryController_1.createCategory);
router.put('/:id', auth_1.authenticateToken, (0, validation_1.validate)(validation_1.paramSchemas.id, 'params'), (0, validation_1.validate)(validation_1.categorySchemas.update), categoryController_1.updateCategory);
router.delete('/:id', auth_1.authenticateToken, (0, validation_1.validate)(validation_1.paramSchemas.id, 'params'), categoryController_1.deleteCategory);
router.post('/reorder', auth_1.authenticateToken, categoryController_1.reorderCategories);
router.post('/:id/set-default', auth_1.authenticateToken, (0, validation_1.validate)(validation_1.paramSchemas.id, 'params'), categoryController_1.setDefaultCategory);
exports.default = router;
//# sourceMappingURL=categories.js.map