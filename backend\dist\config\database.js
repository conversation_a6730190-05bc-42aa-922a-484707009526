"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.config = exports.closeConnection = exports.initializeDatabase = exports.testConnection = exports.sequelize = void 0;
const sequelize_1 = require("sequelize");
const dotenv_1 = require("dotenv");
(0, dotenv_1.config)();
const databaseConfig = {
    development: {
        dialect: 'sqlite',
        storage: process.env.DB_PATH || './database.sqlite',
        logging: console.log,
        define: {
            timestamps: true,
            underscored: true,
            freezeTableName: true,
        },
    },
    test: {
        dialect: 'sqlite',
        storage: ':memory:',
        logging: false,
        define: {
            timestamps: true,
            underscored: true,
            freezeTableName: true,
        },
    },
    production: {
        dialect: 'postgres',
        host: process.env.DB_HOST || 'localhost',
        port: parseInt(process.env.DB_PORT || '5432', 10),
        database: process.env.DB_NAME || 'expense_tracker',
        username: process.env.DB_USER || 'postgres',
        password: process.env.DB_PASSWORD || '',
        logging: false,
        define: {
            timestamps: true,
            underscored: true,
            freezeTableName: true,
        },
        pool: {
            max: 10,
            min: 0,
            acquire: 30000,
            idle: 10000,
        },
        dialectOptions: {
            ssl: process.env.DB_SSL === 'true' ? {
                require: true,
                rejectUnauthorized: false,
            } : false,
        },
    },
};
const env = process.env.NODE_ENV || 'development';
const dbConfig = (env in databaseConfig ? databaseConfig[env] : databaseConfig.development);
exports.config = dbConfig;
let sequelize;
if (env === 'production' && process.env.DATABASE_URL) {
    exports.sequelize = sequelize = new sequelize_1.Sequelize(process.env.DATABASE_URL, {
        dialect: 'postgres',
        logging: false,
        define: dbConfig.define,
        pool: dbConfig.pool,
        dialectOptions: dbConfig.dialectOptions,
    });
}
else {
    if (dbConfig.dialect === 'sqlite') {
        exports.sequelize = sequelize = new sequelize_1.Sequelize({
            dialect: dbConfig.dialect,
            storage: dbConfig.storage,
            logging: dbConfig.logging,
            define: dbConfig.define,
        });
    }
    else {
        exports.sequelize = sequelize = new sequelize_1.Sequelize(dbConfig.database, dbConfig.username, dbConfig.password, {
            host: dbConfig.host,
            port: dbConfig.port,
            dialect: dbConfig.dialect,
            logging: dbConfig.logging,
            define: dbConfig.define,
            pool: dbConfig.pool,
            dialectOptions: dbConfig.dialectOptions,
        });
    }
}
const testConnection = async () => {
    try {
        await sequelize.authenticate();
        console.log(`✅ Database connection established successfully (${env}).`);
        return true;
    }
    catch (error) {
        console.error('❌ Unable to connect to the database:', error);
        return false;
    }
};
exports.testConnection = testConnection;
const initializeDatabase = async () => {
    try {
        const User = require('../models/User');
        const Category = require('../models/Category');
        const Expense = require('../models/Expense');
        User.hasMany(Category, { foreignKey: 'user_id', as: 'categories' });
        Category.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
        User.hasMany(Expense, { foreignKey: 'user_id', as: 'expenses' });
        Expense.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
        Category.hasMany(Expense, { foreignKey: 'category_id', as: 'expenses' });
        Expense.belongsTo(Category, { foreignKey: 'category_id', as: 'category' });
        if (env === 'development') {
            await sequelize.sync({ alter: true });
            console.log('✅ Database synchronized successfully.');
        }
        else if (env === 'test') {
            await sequelize.sync({ force: true });
            console.log('✅ Test database synchronized successfully.');
        }
        return true;
    }
    catch (error) {
        console.error('❌ Database initialization failed:', error);
        return false;
    }
};
exports.initializeDatabase = initializeDatabase;
const closeConnection = async () => {
    try {
        await sequelize.close();
        console.log('✅ Database connection closed successfully.');
    }
    catch (error) {
        console.error('❌ Error closing database connection:', error);
    }
};
exports.closeConnection = closeConnection;
exports.default = sequelize;
//# sourceMappingURL=database.js.map