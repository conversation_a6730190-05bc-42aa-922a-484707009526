# SETUP MEDIU DEZVOLTARE

## 🛠️ PREREQUISITE

### Software necesar
- **Node.js** (versiunea 18+ LTS) - [Download](https://nodejs.org/)
- **Git** - [Download](https://git-scm.com/)
- **VS Code** (recomandat) - [Download](https://code.visualstudio.com/)
- **PostgreSQL** (pentru producție) - [Download](https://www.postgresql.org/)
- **Postman** sau **Insomnia** (pentru testare API)

### VS Code Extensions recomandate
```
- ES7+ React/Redux/React-Native snippets
- Prettier - Code formatter
- ESLint
- Auto Rename Tag
- Bracket Pair Colorizer
- GitLens
- Thunder Client (alternativă la Postman)
- Tailwind CSS IntelliSense
```

---

## 📁 STRUCTURA PROIECTULUI

```
fn/
├── docs/                    # Documentația
├── backend/                 # Server-side application
│   ├── src/
│   │   ├── controllers/     # Route handlers
│   │   ├── middleware/      # Custom middleware
│   │   ├── models/          # Database models
│   │   ├── routes/          # API routes
│   │   ├── services/        # Business logic
│   │   ├── utils/           # Helper functions
│   │   ├── config/          # Configuration files
│   │   └── app.js           # Express app setup
│   ├── tests/               # Backend tests
│   ├── migrations/          # Database migrations
│   ├── package.json
│   └── .env.example
├── frontend/                # Client-side application
│   ├── public/              # Static files
│   ├── src/
│   │   ├── components/      # React components
│   │   ├── pages/           # Page components
│   │   ├── hooks/           # Custom hooks
│   │   ├── services/        # API calls
│   │   ├── utils/           # Helper functions
│   │   ├── styles/          # CSS files
│   │   └── App.jsx          # Main app component
│   ├── tests/               # Frontend tests
│   ├── package.json
│   └── .env.example
├── shared/                  # Shared utilities/types
├── scripts/                 # Build/deployment scripts
├── .gitignore
├── README.md
└── docker-compose.yml       # Pentru development cu Docker
```

---

## 🚀 SETUP RAPID

### 1. Clonare și inițializare
```bash
# Clonează repository-ul
git clone <repository-url>
cd fn

# Creează structura de foldere
mkdir -p backend/src/{controllers,middleware,models,routes,services,utils,config}
mkdir -p backend/{tests,migrations}
mkdir -p frontend/src/{components,pages,hooks,services,utils,styles}
mkdir -p frontend/{public,tests}
mkdir -p shared scripts
```

### 2. Setup Backend
```bash
cd backend

# Inițializează npm project
npm init -y

# Instalează dependențele principale
npm install express cors helmet morgan dotenv
npm install jsonwebtoken bcryptjs
npm install pg sequelize  # Pentru PostgreSQL
npm install sqlite3       # Pentru development
npm install stripe        # Pentru integrarea Stripe
npm install node-cron     # Pentru task-uri programate
npm install winston       # Pentru logging avansat

# Instalează dev dependencies
npm install -D nodemon jest supertest eslint prettier
npm install -D @types/node @types/express  # Dacă folosești TypeScript
```

### 3. Setup Frontend
```bash
cd ../frontend

# Creează React app cu Vite
npm create vite@latest . -- --template react
# SAU pentru TypeScript: npm create vite@latest . -- --template react-ts

# Instalează dependențele
npm install

# Instalează dependențele adiționale
npm install axios react-router-dom
npm install @headlessui/react @heroicons/react  # Pentru UI components
npm install tailwindcss postcss autoprefixer
npm install @stripe/stripe-js @stripe/react-stripe-js  # Pentru Stripe
npm install react-hot-toast  # Pentru notificări
npm install recharts  # Pentru grafice și rapoarte
npm install date-fns  # Pentru manipularea datelor
npm install -D @testing-library/react @testing-library/jest-dom vitest
```

### 4. Configurare Tailwind CSS
```bash
cd frontend
npx tailwindcss init -p
```

**Editează `tailwind.config.js`:**
```javascript
/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
        }
      }
    },
  },
  plugins: [],
}
```

**Adaugă în `src/index.css`:**
```css
@tailwind base;
@tailwind components;
@tailwind utilities;
```

---

## ⚙️ CONFIGURARE ENVIRONMENT

### Backend Environment (.env)
```bash
cd backend
cp .env.example .env
```

**Conținut `.env`:**
```env
# Server Configuration
PORT=3001
NODE_ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=expense_tracker_dev
DB_USER=your_username
DB_PASSWORD=your_password
DB_DIALECT=postgres

# Pentru development cu SQLite
DB_STORAGE=./database.sqlite

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your-refresh-secret-here
JWT_REFRESH_EXPIRES_IN=30d

# Email Configuration (pentru viitor)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# Stripe Configuration (pentru monetizare)
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
```
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_...
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=secure-admin-password

# App Configuration
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:3001
```

### Frontend Environment (.env)
```bash
cd frontend
cp .env.example .env
```

**Conținut `.env`:**
```env
# API Configuration
VITE_API_URL=http://localhost:3001/api
VITE_APP_NAME=Finance Flow
VITE_APP_VERSION=1.0.0

# Stripe Configuration
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_...

# Analytics (pentru producție)
VITE_GA_TRACKING_ID=G-XXXXXXXXXX
VITE_MIXPANEL_TOKEN=your-mixpanel-token

# Feature Flags
VITE_ENABLE_PREMIUM=true
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_STRIPE=true
```

---

## 🗄️ SETUP DATABASE

### Opțiunea 1: SQLite (pentru development rapid)
```bash
cd backend

# SQLite se creează automat la prima rulare
# Nu necesită setup suplimentar
```

### Opțiunea 2: PostgreSQL (recomandat)
```bash
# Instalează PostgreSQL
# Windows: Download de pe postgresql.org
# macOS: brew install postgresql
# Linux: sudo apt-get install postgresql

# Creează database
psql -U postgres
CREATE DATABASE expense_tracker_dev;
CREATE USER expense_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE expense_tracker_dev TO expense_user;
\q
```

### Opțiunea 3: Docker (cel mai simplu)
```bash
# Creează docker-compose.yml în root
version: '3.8'
services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: expense_tracker_dev
      POSTGRES_USER: expense_user
      POSTGRES_PASSWORD: your_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:

# Pornește database
docker-compose up -d postgres
```

---

## 👥 SETUP UTILIZATORI DE TEST

### Generarea automată a utilizatorilor de test
După configurarea bazei de date, creează utilizatorii de test pentru dezvoltare:

```bash
cd backend

# Rulează scriptul de creare utilizatori de test
node scripts/createTestUsers.js
```

### Utilizatori de test disponibili
Scriptul va crea următorii utilizatori pentru testare:

#### 🆓 Utilizator Gratuit
- **Email**: `<EMAIL>`
- **Parola**: `Test123!`
- **Plan**: Free (50 cheltuieli/lună, 5 categorii)

#### 💰 Utilizator Basic
- **Email**: `<EMAIL>`
- **Parola**: `Test123!`
- **Plan**: Basic ($5/lună, 500 cheltuieli/lună)

#### 🌟 Utilizator Premium
- **Email**: `<EMAIL>`
- **Parola**: `Test123!`
- **Plan**: Premium ($15/lună, cheltuieli nelimitate)

#### 🎯 Utilizator Demo
- **Email**: `<EMAIL>`
- **Parola**: `password123`
- **Scop**: Demonstrație pentru vizitatori

#### 🔧 Administrator
- **Email**: `<EMAIL>`
- **Parola**: `admin123`
- **Acces**: Dashboard admin, gestionarea utilizatorilor

### Date incluse pentru fiecare utilizator
- **6 categorii default**: Mâncare, Transport, Utilități, Divertisment, Sănătate, Cumpărături
- **4 cheltuieli sample**: Pentru demonstrarea funcționalității
- **Abonamente configurate**: Pentru utilizatorii Basic și Premium
- **Metadata de test**: Pentru identificarea în sistem

### Verificarea utilizatorilor creați
```bash
# Verifică în baza de date
psql -U expense_user -d expense_tracker_dev
SELECT email, subscription_type FROM users WHERE email LIKE '%test%' OR email LIKE '%demo%' OR email LIKE '%admin%';
```

### Resetarea utilizatorilor de test
```bash
# Pentru a șterge și recrea utilizatorii de test
node scripts/cleanTestUsers.js  # Șterge utilizatorii existenți
node scripts/createTestUsers.js  # Recreează utilizatorii
```

### Utilizare în development
1. **Pentru testarea limitărilor**: Folosește contul Free
2. **Pentru testarea funcționalităților Basic**: Folosește contul Basic
3. **Pentru testarea completă**: Folosește contul Premium
4. **Pentru demonstrații**: Folosește contul Demo
5. **Pentru funcții admin**: Folosește contul Administrator

---

## 📦 PACKAGE.JSON SCRIPTS

### Backend package.json scripts
```json
{
  "scripts": {
    "start": "node src/app.js",
    "dev": "nodemon src/app.js",
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "lint": "eslint src/",
    "lint:fix": "eslint src/ --fix",
    "format": "prettier --write src/",
    "db:migrate": "npx sequelize-cli db:migrate",
    "db:seed": "npx sequelize-cli db:seed:all",
    "db:reset": "npx sequelize-cli db:drop && npx sequelize-cli db:create && npm run db:migrate && npm run db:seed"
  }
}
```

### Frontend package.json scripts
```json
{
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview",
    "test": "vitest",
    "test:ui": "vitest --ui",
    "test:coverage": "vitest --coverage",
    "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0",
    "lint:fix": "eslint . --ext js,jsx --fix",
    "format": "prettier --write src/"
  }
}
```

---

## 🧪 SETUP TESTING

### Backend Testing (Jest)
**Creează `backend/jest.config.js`:**
```javascript
module.exports = {
  testEnvironment: 'node',
  setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],
  testMatch: ['**/__tests__/**/*.js', '**/?(*.)+(spec|test).js'],
  collectCoverageFrom: [
    'src/**/*.js',
    '!src/config/**',
    '!src/migrations/**'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  }
};
```

### Frontend Testing (Vitest)
**Creează `frontend/vitest.config.js`:**
```javascript
import { defineConfig } from 'vitest/config'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  test: {
    environment: 'jsdom',
    setupFiles: ['./src/tests/setup.js'],
    coverage: {
      reporter: ['text', 'json', 'html'],
      threshold: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    }
  }
})
```

---

## 🔧 DEVELOPMENT WORKFLOW

### Pornirea aplicației pentru development
```bash
# Terminal 1: Database (dacă folosești Docker)
docker-compose up postgres

# Terminal 2: Backend
cd backend
npm run dev

# Terminal 3: Frontend
cd frontend
npm run dev

# Terminal 4: Testing (opțional)
cd backend
npm run test:watch
```

### URL-uri pentru development
- **Frontend**: 
  - Local: http://localhost:5173
  - Rețea: http://***********:5173/ (accesibil din orice dispozitiv din rețea)
- **Backend API**: 
  - Local: http://localhost:3000
  - Rețea: http://***********:3000/api (accesibil din orice dispozitiv din rețea)
- **Database**: localhost:5432 (PostgreSQL) sau SQLite local

---

## 🌐 CONFIGURARE ACCESIBILITATE DIN REȚEA

### Backend - Configurare pentru accesul din rețea
**Modifică `backend/src/app.js`:**
```javascript
// Configurează serverul să asculte pe toate interfețele
const PORT = process.env.PORT || 3000;
const HOST = process.env.NODE_ENV === 'production' ? 'localhost' : '0.0.0.0';

app.listen(PORT, HOST, () => {
  console.log(`🚀 Server running on http://${HOST}:${PORT}`);
  console.log(`📡 API available at http://${HOST}:${PORT}/api`);
});

// Configurează CORS pentru dezvoltare
if (process.env.NODE_ENV !== 'production') {
  app.use(cors({
    origin: true, // Permite toate originile în dezvoltare
    credentials: true
  }));
}
```

### Frontend - Configurare Webpack pentru accesul din rețea
**Modifică `frontend/webpack.config.js`:**
```javascript
module.exports = {
  // ... alte configurații
  
  devServer: {
    host: '0.0.0.0', // Permite accesul din rețea
    port: 5173,
    hot: true,
    historyApiFallback: true,
    allowedHosts: 'all', // Permite toate hostname-urile
    proxy: {
      '/api': {
        target: 'http://localhost:3000',
        changeOrigin: true,
        secure: false
      }
    }
  }
};
```

### Beneficii configurare rețea
- ✅ **Testare pe multiple dispozitive**: Telefoane, tablete, alte computere
- ✅ **Dezvoltare colaborativă**: Echipa poate accesa aplicația din rețea
- ✅ **Testare responsive**: Verificare pe dispozitive reale
- ✅ **Demo rapid**: Prezentare aplicației fără deployment

### Securitate în dezvoltare
- 🔒 **CORS configurat**: Permite toate originile doar în modul dezvoltare
- 🔒 **Host binding**: `0.0.0.0` doar pentru dezvoltare, `localhost` în producție
- 🔒 **Environment aware**: Configurații diferite pentru dev/prod

---

## ⚡ OPTIMIZĂRI WEBPACK ȘI PERFORMANȚĂ

### Configurare webpack optimizată
**Actualizează `frontend/webpack.config.js`:**
```javascript
module.exports = {
  mode: process.env.NODE_ENV || 'development',
  
  // Optimizări pentru imagini
  module: {
    rules: [
      {
        test: /\.(png|jpe?g|gif|svg|webp)$/i,
        type: 'asset',
        parser: {
          dataUrlCondition: {
            maxSize: 8 * 1024 // 8KB - imagini mici inline
          }
        },
        generator: {
          filename: 'images/[name].[hash][ext]'
        },
        use: [
          {
            loader: 'image-webpack-loader',
            options: {
              mozjpeg: { progressive: true, quality: 80 },
              optipng: { enabled: true },
              pngquant: { quality: [0.6, 0.8] },
              gifsicle: { interlaced: false },
              webp: { quality: 80 }
            }
          }
        ]
      }
    ]
  },
  
  // Optimizări bundle
  optimization: {
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all'
        }
      }
    }
  },
  
  // Configurări performanță
  performance: {
    hints: process.env.NODE_ENV === 'production' ? 'warning' : false,
    maxAssetSize: 512000, // 512KB
    maxEntrypointSize: 512000 // 512KB
  }
};
```

### Instalare dependențe pentru optimizări
```bash
cd frontend
npm install --save-dev image-webpack-loader
```

### Rezultate optimizări
- ✅ **Imagini comprimate**: hero-bg.jpg de la 396KB la 176KB
- ✅ **Bundle optimizat**: Vendor libraries separate
- ✅ **Fără avertismente**: Build curat în consola webpack
- ✅ **Performanță îmbunătățită**: Încărcare mai rapidă
- ✅ **Imagini mici inline**: Sub 8KB convertite în base64

### Verificare optimizări
```bash
# Verifică dimensiunea bundle-urilor
npm run build

# Analizează bundle-urile (opțional)
npm install --save-dev webpack-bundle-analyzer
# Adaugă în package.json: "analyze": "webpack-bundle-analyzer dist"
npm run analyze
```

---

## 🔍 DEBUGGING

### VS Code Launch Configuration
**Creează `.vscode/launch.json`:**
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Backend",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/backend/src/app.js",
      "env": {
        "NODE_ENV": "development"
      },
      "envFile": "${workspaceFolder}/backend/.env",
      "console": "integratedTerminal",
      "restart": true,
      "runtimeExecutable": "nodemon",
      "skipFiles": ["<node_internals>/**"]
    }
  ]
}
```

### Logging
```javascript
// Backend: Folosește morgan + winston
// Frontend: Folosește console.log cu environment checks

if (import.meta.env.DEV) {
  console.log('Debug info:', data);
}
```

---

## ✅ VERIFICARE SETUP

### Checklist final
- [ ] Node.js instalat și funcțional
- [ ] Git configurat
- [ ] VS Code cu extensiile instalate
- [ ] Backend dependencies instalate (inclusiv Stripe)
- [ ] Frontend dependencies instalate (inclusiv Stripe Elements)
- [ ] Database setup (SQLite/PostgreSQL/Docker)
- [ ] Environment variables configurate
- [ ] **ACCESIBILITATE REȚEA**: Backend configurat pentru `0.0.0.0:3000`
- [ ] **ACCESIBILITATE REȚEA**: Frontend configurat pentru accesul din rețea
- [ ] **OPTIMIZĂRI**: `image-webpack-loader` instalat și configurat
- [ ] **OPTIMIZĂRI**: Webpack configurat fără avertismente
- [ ] **CORS**: Configurat pentru dezvoltare (toate originile)
- [ ] Stripe account creat și configurat
- [ ] Stripe CLI instalat și configurat
- [ ] Webhook-uri Stripe funcționale
- [ ] Aplicația pornește fără erori
- [ ] Hot reload funcționează
- [ ] **TESTARE REȚEA**: Aplicația accesibilă din alte dispozitive
- [ ] **BUILD OPTIMIZAT**: Webpack compilează fără avertismente
- [ ] Testele rulează
- [ ] Integrarea Stripe testată

### Comenzi de verificare
```bash
# Verifică versiunile
node --version    # Ar trebui să fie 18+
npm --version     # Ar trebui să fie 8+
git --version

# Testează backend
cd backend
npm test
curl http://localhost:3000/api/health
# Testează accesul din rețea
curl http://***********:3000/api/health

# Testează frontend
cd frontend
npm test
# Verifică în browser local: http://localhost:5173
# Verifică în browser din rețea: http://***********:5173

# Verifică optimizările webpack
npm run dev  # Ar trebui să compileze fără avertismente

# Testează compresia imaginilor
# Verifică în Network tab că imaginile sunt comprimate
```

---

## 💳 SETUP STRIPE PENTRU DEZVOLTARE

### 1. Crearea contului Stripe
1. **Înregistrează-te** pe [stripe.com](https://stripe.com)
2. **Activează modul test** în dashboard
3. **Obține cheile API** din Developers > API keys

### 2. Configurare webhook-uri locale
```bash
# Instalează Stripe CLI
# Windows: scoop install stripe
# macOS: brew install stripe/stripe-cli/stripe
# Linux: wget + install manual

# Login în Stripe
stripe login

# Forward webhook-uri către aplicația locală
stripe listen --forward-to localhost:3001/api/webhooks/stripe
```

### 3. Testare plăți
**Carduri de test Stripe:**
```
# Card valid
4242 4242 4242 4242
Expiry: orice dată viitoare
CVC: orice 3 cifre

# Card declined
4000 0000 0000 0002

# Card cu autentificare 3D Secure
4000 0025 0000 3155
```

### 4. Verificare integrare
```bash
# Testează webhook endpoint
curl -X POST http://localhost:3001/api/webhooks/stripe \
  -H "Content-Type: application/json" \
  -d '{"type": "customer.subscription.created"}'

# Verifică Stripe dashboard pentru evenimente
```

### 5. Environment variables pentru Stripe
```bash
# Backend .env
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...  # Din stripe listen command

# Frontend .env
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_...
```

---

## 🆘 TROUBLESHOOTING

### Probleme comune

**Port deja în uz:**
```bash
# Windows
netstat -ano | findstr :3001
taskkill /PID <PID> /F

# macOS/Linux
lsof -ti:3001 | xargs kill -9
```

**Probleme cu dependențele:**
```bash
# Șterge node_modules și reinstalează
rm -rf node_modules package-lock.json
npm install
```

**Probleme cu database:**
```bash
# Reset database
npm run db:reset

# Verifică conexiunea
psql -h localhost -U expense_user -d expense_tracker_dev
```

**Probleme cu Stripe:**
```bash
# Verifică dacă Stripe CLI este instalat
stripe --version

# Re-login în Stripe
stripe login

# Testează webhook-ul manual
stripe trigger customer.subscription.created

# Verifică log-urile webhook-urilor
stripe logs tail
```

**Probleme cu environment variables:**
```bash
# Verifică dacă .env este încărcat
echo $STRIPE_SECRET_KEY  # Linux/macOS
echo %STRIPE_SECRET_KEY%  # Windows

# Restart aplicația după modificarea .env
```

---

## 📞 NEXT STEPS

1. **Verifică că totul funcționează** cu checklist-ul de mai sus
2. **Citește** [Ghidul de Testare](./testing-guide.md)
3. **Începe dezvoltarea** cu primul endpoint de autentificare
4. **Configurează** Git hooks pentru linting automat

---

*Pentru probleme sau întrebări, consultă documentația sau contactează echipa.*