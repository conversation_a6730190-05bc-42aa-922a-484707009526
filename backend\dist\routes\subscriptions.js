"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const express_validator_1 = require("express-validator");
const auth_1 = require("../middleware/auth");
const subscriptionController_1 = __importDefault(require("../controllers/subscriptionController"));
const router = express_1.default.Router();
router.use(auth_1.authenticateToken);
router.get('/plans', subscriptionController_1.default.getPlans);
router.get('/current', subscriptionController_1.default.getCurrentSubscription);
router.post('/checkout', [
    (0, express_validator_1.body)('planId')
        .isInt({ min: 1 })
        .withMessage('Plan ID must be a valid integer')
], subscriptionController_1.default.createCheckoutSession);
router.post('/portal', subscriptionController_1.default.createCustomerPortal);
router.post('/cancel', subscriptionController_1.default.cancelSubscription);
router.post('/reactivate', subscriptionController_1.default.reactivateSubscription);
router.get('/checkout/:sessionId', [
    (0, express_validator_1.param)('sessionId')
        .isString()
        .isLength({ min: 1 })
        .withMessage('Session ID is required')
], subscriptionController_1.default.checkCheckoutSession);
router.get('/usage', subscriptionController_1.default.getUsageStats);
router.get('/permission/:action', [
    (0, express_validator_1.param)('action')
        .isString()
        .isLength({ min: 1 })
        .withMessage('Action is required')
], subscriptionController_1.default.checkPermission);
exports.default = router;
//# sourceMappingURL=subscriptions.js.map