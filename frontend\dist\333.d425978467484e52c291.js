"use strict";(self.webpackChunkexpense_tracker_frontend=self.webpackChunkexpense_tracker_frontend||[]).push([[333],{4095:(e,r,a)=>{a.r(r),a.d(r,{default:()=>g});var s=a(4985),t=a(893),l=a(6540),i=a.n(l),n=a(9785),o=a(888),d=a(4976),c=a(8952),m=a(6103),x=a(6215),p=a(4848);const u=c.Ik({email:c.Yj().min(1,"Email-ul este obligatoriu").email("Email-ul nu este valid")}),g=()=>{const[e,r]=i().useState(!1),[a,l]=i().useState(!1),{register:c,handleSubmit:g,formState:{errors:y},getValues:h}=(0,n.mN)({resolver:(0,t.u)(u)});return a?(0,p.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,p.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,p.jsxs)("div",{className:"text-center",children:[(0,p.jsx)("div",{className:"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100",children:(0,p.jsx)("svg",{className:"h-6 w-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,p.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),(0,p.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Email trimis!"}),(0,p.jsxs)("p",{className:"mt-2 text-center text-sm text-gray-600",children:["Am trimis instrucțiunile de resetare a parolei la adresa"," ",(0,p.jsx)("span",{className:"font-medium text-gray-900",children:h("email")})]}),(0,p.jsx)("p",{className:"mt-4 text-center text-sm text-gray-600",children:"Verifică-ți inbox-ul și urmează instrucțiunile din email."})]}),(0,p.jsx)("div",{className:"mt-8",children:(0,p.jsxs)(d.N_,{to:"/login",className:"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-primary-600 bg-primary-50 hover:bg-primary-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500",children:[(0,p.jsx)(s.A,{className:"h-5 w-5 mr-2"}),"Înapoi la conectare"]})})]})}):(0,p.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8",children:(0,p.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,p.jsxs)("div",{children:[(0,p.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"Resetează parola"}),(0,p.jsx)("p",{className:"mt-2 text-center text-sm text-gray-600",children:"Introdu adresa de email asociată contului tău și îți vom trimite un link pentru resetarea parolei."})]}),(0,p.jsxs)("form",{className:"mt-8 space-y-6",onSubmit:g(async e=>{r(!0);try{await new Promise(e=>setTimeout(e,2e3)),l(!0),o.oR.success("Email-ul de resetare a fost trimis!")}catch(e){o.oR.error(e.message||"Eroare la trimiterea email-ului")}finally{r(!1)}}),children:[(0,p.jsx)("div",{children:(0,p.jsx)(x.Ay,{...c("email"),type:"email",label:"Adresa de email",placeholder:"<EMAIL>",error:y.email?.message,autoComplete:"email"})}),(0,p.jsx)("div",{children:(0,p.jsx)(m.Ay,{type:"submit",variant:"primary",size:"lg",className:"w-full",loading:e,disabled:e,children:e?"Se trimite...":"Trimite link de resetare"})}),(0,p.jsx)("div",{className:"text-center",children:(0,p.jsxs)(d.N_,{to:"/login",className:"font-medium text-primary-600 hover:text-primary-500 flex items-center justify-center",children:[(0,p.jsx)(s.A,{className:"h-4 w-4 mr-1"}),"Înapoi la conectare"]})})]})]})})}},6103:(e,r,a)=>{a.d(r,{Ay:()=>i});var s=a(4848),t=(a(6540),a(2392)),l=a(9264);const i=({children:e,variant:r="primary",size:a="md",loading:i=!1,disabled:n=!1,fullWidth:o=!1,leftIcon:d=null,rightIcon:c=null,className:m="",onClick:x,type:p="button",...u})=>{const g={primary:["bg-primary-600 text-white border-primary-600","hover:bg-primary-700 hover:border-primary-700","focus:ring-primary-500","disabled:bg-primary-300 disabled:border-primary-300"].join(" "),secondary:["bg-gray-600 text-white border-gray-600","hover:bg-gray-700 hover:border-gray-700","focus:ring-gray-500","disabled:bg-gray-300 disabled:border-gray-300"].join(" "),outline:["bg-transparent text-primary-600 border-primary-600","hover:bg-primary-50 hover:text-primary-700","focus:ring-primary-500","disabled:text-primary-300 disabled:border-primary-300"].join(" "),ghost:["bg-transparent text-gray-700 border-transparent","hover:bg-gray-100 hover:text-gray-900","focus:ring-gray-500","disabled:text-gray-400"].join(" "),danger:["bg-red-600 text-white border-red-600","hover:bg-red-700 hover:border-red-700","focus:ring-red-500","disabled:bg-red-300 disabled:border-red-300"].join(" "),success:["bg-green-600 text-white border-green-600","hover:bg-green-700 hover:border-green-700","focus:ring-green-500","disabled:bg-green-300 disabled:border-green-300"].join(" "),warning:["bg-yellow-600 text-white border-yellow-600","hover:bg-yellow-700 hover:border-yellow-700","focus:ring-yellow-500","disabled:bg-yellow-300 disabled:border-yellow-300"].join(" "),white:["bg-white text-gray-900 border-white","hover:bg-gray-50 hover:text-gray-900","focus:ring-gray-500","disabled:bg-gray-100 disabled:text-gray-400"].join(" ")},y={xs:"h-3 w-3",sm:"h-4 w-4",md:"h-4 w-4",lg:"h-5 w-5",xl:"h-6 w-6"},h=n||i;return(0,s.jsxs)("button",{type:p,onClick:e=>{h?e.preventDefault():x?.(e)},disabled:h,className:(0,t.cn)("inline-flex items-center justify-center","border font-medium rounded-lg","transition-all duration-200 ease-in-out","focus:outline-none focus:ring-2 focus:ring-offset-2","disabled:cursor-not-allowed disabled:opacity-60",g[r],{xs:"px-2 py-1 text-xs",sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base",xl:"px-8 py-4 text-lg"}[a],o&&"w-full",m),...u,children:[d&&!i&&(0,s.jsx)("span",{className:(0,t.cn)(y[a],e&&"mr-2"),children:d}),i&&(0,s.jsx)("span",{className:(0,t.cn)(e&&"mr-2"),children:(0,s.jsx)(l.Ay,{size:"xs"===a||"sm"===a?"sm":"md",color:"currentColor"})}),e&&(0,s.jsx)("span",{className:i?"opacity-70":"",children:e}),c&&!i&&(0,s.jsx)("span",{className:(0,t.cn)(y[a],e&&"ml-2"),children:c})]})}},6215:(e,r,a)=>{a.d(r,{Ay:()=>x});var s=a(4848),t=a(2509),l=a(72),i=a(3956),n=a(7117),o=a(4015),d=a(6540),c=a(2392);const m=(0,d.forwardRef)(({label:e,type:r="text",placeholder:a,value:m,onChange:x,onBlur:p,onFocus:u,error:g,success:y,hint:h,required:b=!1,disabled:f=!1,readOnly:j=!1,size:w="md",leftIcon:N,rightIcon:v,className:k="",inputClassName:A="",labelClassName:I="",id:C,name:R,...S},E)=>{const[z,B]=(0,d.useState)(!1),[_,T]=(0,d.useState)(!1),D=C||`input-${Math.random().toString(36).substr(2,9)}`,F="password"===r&&z?"text":r,L={sm:"h-4 w-4",md:"h-5 w-5",lg:"h-6 w-6"},P=Boolean(g),q=Boolean(y)&&!P;return Boolean(N||v||"password"===r),(0,s.jsxs)("div",{className:(0,c.cn)("w-full",k),children:[e&&(0,s.jsxs)("label",{htmlFor:D,className:(0,c.cn)("block text-sm font-medium mb-2",P?"text-red-700":"text-gray-700",f&&"text-gray-400",I),children:[e,b&&(0,s.jsx)("span",{className:"text-red-500 ml-1","aria-label":"obligatoriu",children:"*"})]}),(0,s.jsxs)("div",{className:"relative",children:[N&&(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)("span",{className:(0,c.cn)(L[w],P?"text-red-400":"text-gray-400"),children:N})}),(0,s.jsx)("input",{ref:E,id:D,name:R,type:F,value:m,onChange:x,onFocus:e=>{T(!0),u?.(e)},onBlur:e=>{T(!1),p?.(e)},placeholder:a,required:b,disabled:f,readOnly:j,className:(0,c.cn)("block w-full border rounded-lg transition-all duration-200","focus:outline-none focus:ring-2 focus:ring-offset-0","placeholder:text-gray-400",{sm:"px-3 py-2 text-sm",md:"px-4 py-2.5 text-sm",lg:"px-4 py-3 text-base"}[w],N&&"pl-10",(v||"password"===r)&&"pr-10",!f&&!j&&[P?["border-red-300 text-red-900","focus:border-red-500 focus:ring-red-500"]:q?["border-green-300 text-green-900","focus:border-green-500 focus:ring-green-500"]:["border-gray-300 text-gray-900","focus:border-primary-500 focus:ring-primary-500","hover:border-gray-400"]],f&&["bg-gray-50 border-gray-200 text-gray-500","cursor-not-allowed"],j&&["bg-gray-50 border-gray-200","cursor-default"],A),...S}),(0,s.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:"password"===r?(0,s.jsx)("button",{type:"button",onClick:()=>{B(!z)},className:(0,c.cn)("text-gray-400 hover:text-gray-600 focus:outline-none",L[w]),"aria-label":z?"Ascunde parola":"Arată parola",children:z?(0,s.jsx)(t.A,{className:L[w]}):(0,s.jsx)(l.A,{className:L[w]})}):P?(0,s.jsx)(i.A,{className:(0,c.cn)(L[w],"text-red-400")}):q?(0,s.jsx)(n.A,{className:(0,c.cn)(L[w],"text-green-400")}):v?(0,s.jsx)("span",{className:(0,c.cn)(L[w],"text-gray-400"),children:v}):null})]}),(g||y||h)&&(0,s.jsxs)("div",{className:"mt-2 flex items-start space-x-1",children:[(g||y)&&(0,s.jsx)("span",{className:"flex-shrink-0 mt-0.5",children:g?(0,s.jsx)(i.A,{className:"h-4 w-4 text-red-400"}):(0,s.jsx)(n.A,{className:"h-4 w-4 text-green-400"})}),h&&!g&&!y&&(0,s.jsx)(o.A,{className:"h-4 w-4 text-gray-400 flex-shrink-0 mt-0.5"}),(0,s.jsx)("p",{className:(0,c.cn)("text-sm",g?"text-red-600":y?"text-green-600":"text-gray-600"),children:g||y||h})]})]})});m.displayName="Input",(0,d.forwardRef)((e,r)=>(0,s.jsx)(m,{ref:r,type:"email",placeholder:"<EMAIL>",...e})).displayName="EmailInput",(0,d.forwardRef)((e,r)=>(0,s.jsx)(m,{ref:r,type:"password",placeholder:"••••••••",...e})).displayName="PasswordInput",(0,d.forwardRef)(({min:e,max:r,step:a=1,...t},l)=>(0,s.jsx)(m,{ref:l,type:"number",min:e,max:r,step:a,...t})).displayName="NumberInput",(0,d.forwardRef)(({onSearch:e,...r},a)=>(0,s.jsx)(m,{ref:a,type:"search",placeholder:"Caută...",onKeyDown:r=>{"Enter"===r.key&&e&&e(r.currentTarget.value)},...r})).displayName="SearchInput",(0,d.forwardRef)((e,r)=>(0,s.jsx)(m,{ref:r,type:"tel",placeholder:"+40 123 456 789",...e})).displayName="PhoneInput",(0,d.forwardRef)((e,r)=>(0,s.jsx)(m,{ref:r,type:"url",placeholder:"https://exemplu.com",...e})).displayName="UrlInput",(0,d.forwardRef)((e,r)=>(0,s.jsx)(m,{ref:r,type:"date",...e})).displayName="DateInput",(0,d.forwardRef)((e,r)=>(0,s.jsx)(m,{ref:r,type:"time",...e})).displayName="TimeInput",(0,d.forwardRef)((e,r)=>(0,s.jsx)(m,{ref:r,type:"datetime-local",...e})).displayName="DateTimeInput";const x=m}}]);