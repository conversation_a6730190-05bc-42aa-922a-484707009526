"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const prisma_1 = require("../config/prisma");
const logger_1 = __importDefault(require("../utils/logger"));
const usageService_1 = require("../services/usageService");
class AdminController {
    async getDashboardStats(req, res) {
        try {
            const totalUsers = await prisma_1.prisma.user.count();
            const activeUsers = await prisma_1.prisma.user.count({
                where: {
                    is_active: true,
                },
            });
            const newUsersThisMonth = await prisma_1.prisma.user.count({
                where: {
                    created_at: {
                        gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
                    },
                },
            });
            const totalSubscriptions = await prisma_1.prisma.subscription.count();
            const activeSubscriptions = await prisma_1.prisma.subscription.count({
                where: {
                    status: 'active',
                },
            });
            const cancelledSubscriptions = await prisma_1.prisma.subscription.count({
                where: {
                    status: 'canceled',
                },
            });
            const totalExpenses = await prisma_1.prisma.expense.count();
            const expensesThisMonth = await prisma_1.prisma.expense.count({
                where: {
                    date: {
                        gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
                    },
                },
            });
            const monthlyRevenue = activeSubscriptions * 9.99;
            res.json({
                success: true,
                data: {
                    users: {
                        total: totalUsers,
                        active: activeUsers,
                        newThisMonth: newUsersThisMonth,
                    },
                    subscriptions: {
                        total: totalSubscriptions,
                        active: activeSubscriptions,
                        cancelled: cancelledSubscriptions,
                    },
                    expenses: {
                        total: totalExpenses,
                        thisMonth: expensesThisMonth,
                    },
                    revenue: {
                        monthly: monthlyRevenue,
                        annual: monthlyRevenue * 12,
                    },
                },
            });
        }
        catch (error) {
            logger_1.default.error('Error getting dashboard stats:', error);
            res.status(500).json({
                success: false,
                message: 'Internal server error',
            });
        }
    }
    async getSystemAlerts(req, res) {
        try {
            const alerts = [
                {
                    id: 1,
                    type: 'warning',
                    title: 'Server Load High',
                    message: 'Server load is above 80%',
                    timestamp: new Date().toISOString(),
                    read: false,
                },
                {
                    id: 2,
                    type: 'info',
                    title: 'New User Registrations',
                    message: '5 new users registered today',
                    timestamp: new Date(Date.now() - 3600000).toISOString(),
                    read: false,
                },
            ];
            res.json({
                success: true,
                data: alerts,
            });
        }
        catch (error) {
            logger_1.default.error('Error getting system alerts:', error);
            res.status(500).json({
                success: false,
                message: 'Internal server error',
            });
        }
    }
    async markAlertAsRead(req, res) {
        try {
            const { alertId } = req.params;
            logger_1.default.info(`Alert ${alertId} marked as read by admin ${req.user.id}`);
            res.json({
                success: true,
                message: 'Alert marked as read',
            });
        }
        catch (error) {
            logger_1.default.error('Error marking alert as read:', error);
            res.status(500).json({
                success: false,
                message: 'Internal server error',
            });
        }
    }
    async getUsers(req, res) {
        try {
            const { page = '1', limit = '10', search = '', status = 'all', planType } = req.query;
            const offset = (parseInt(page) - 1) * parseInt(limit);
            const whereClause = {};
            if (search && typeof search === 'string') {
                whereClause.OR = [
                    { name: { contains: search, mode: 'insensitive' } },
                    { email: { contains: search, mode: 'insensitive' } },
                ];
            }
            if (status !== 'all') {
                whereClause.is_active = status === 'active';
            }
            const [users, total] = await Promise.all([
                prisma_1.prisma.user.findMany({
                    where: whereClause,
                    select: {
                        id: true,
                        name: true,
                        email: true,
                        role: true,
                        is_active: true,
                        created_at: true,
                        last_login: true,
                        login_count: true,
                        plan_type: true,
                        subscription_status: true,
                        stripe_customer_id: true,
                        subscription_current_period_start: true,
                        subscription_current_period_end: true,
                        monthly_expense_count: true,
                        monthly_expense_limit: true,
                        currency: true,
                        timezone: true,
                        email_verified: true,
                        preferences: true,
                        subscription: {
                            select: {
                                id: true,
                                status: true,
                                current_period_start: true,
                                current_period_end: true,
                                trial_start: true,
                                trial_end: true,
                                plan: {
                                    select: {
                                        id: true,
                                        name: true,
                                        price: true,
                                        currency: true,
                                        interval: true,
                                        features: true,
                                    },
                                },
                            },
                        },
                        _count: {
                            select: {
                                expenses: true,
                                categories: true,
                                usage_logs: true,
                            },
                        },
                    },
                    orderBy: { created_at: 'desc' },
                    skip: offset,
                    take: parseInt(limit),
                }),
                prisma_1.prisma.user.count({ where: whereClause }),
            ]);
            const usersWithStats = await Promise.all(users.map(async (user) => {
                let totalRevenue = 0;
                if (user.subscription && user.subscription.plan) {
                    const revenueResult = await prisma_1.prisma.$queryRaw `
              SELECT 
                CASE 
                  WHEN p.interval = 'month' THEN 
                    p.price * GREATEST(1, EXTRACT(EPOCH FROM (NOW() - s.current_period_start)) / 2592000)
                  WHEN p.interval = 'year' THEN 
                    p.price * GREATEST(1, EXTRACT(EPOCH FROM (NOW() - s.current_period_start)) / 31536000)
                  ELSE 0
                END as revenue
              FROM subscriptions s
              JOIN plans p ON s.plan_id = p.id
              WHERE s.user_id = ${user.id} AND s.status = 'active'
              LIMIT 1
            `;
                    totalRevenue = revenueResult[0]?.revenue || 0;
                }
                return {
                    ...user,
                    createdAt: user.created_at,
                    lastActiveAt: user.last_login,
                    loginCount: user.login_count,
                    totalExpenses: user._count.expenses,
                    totalCategories: user._count.categories,
                    totalUsageLogs: user._count.usage_logs,
                    totalRevenue: parseFloat(totalRevenue) || 0,
                    status: !user.is_active ? 'blocked' :
                        user.subscription_status === 'active' ? 'active' :
                            user.subscription_status === 'trialing' ? 'trial' : 'inactive',
                    planType: user.plan_type,
                };
            }));
            res.json({
                success: true,
                data: {
                    users: usersWithStats,
                    pagination: {
                        page: parseInt(page),
                        limit: parseInt(limit),
                        total,
                        pages: Math.ceil(total / parseInt(limit)),
                    },
                },
            });
        }
        catch (error) {
            logger_1.default.error('Error getting users:', error);
            res.status(500).json({
                success: false,
                message: 'Internal server error',
            });
        }
    }
    async getUserDetails(req, res) {
        try {
            const { userId } = req.params;
            const user = await prisma_1.prisma.user.findUnique({
                where: { id: parseInt(userId) },
                include: {
                    subscription: {
                        include: {
                            plan: true,
                        },
                    },
                    expenses: {
                        orderBy: { created_at: 'desc' },
                        take: 10,
                        include: {
                            category: true,
                        },
                    },
                    categories: {
                        where: { is_active: true },
                        orderBy: { sort_order: 'asc' },
                    },
                    usage_logs: {
                        orderBy: { created_at: 'desc' },
                        take: 20,
                    },
                    _count: {
                        select: {
                            expenses: true,
                            categories: true,
                            usage_logs: true,
                        },
                    },
                },
            });
            if (!user) {
                return res.status(404).json({
                    success: false,
                    message: 'User not found',
                });
            }
            const { password, ...userWithoutPassword } = user;
            let totalRevenue = 0;
            if (user.subscription && user.subscription.plan) {
                const plan = user.subscription.plan;
                const startDate = new Date(user.subscription.current_period_start);
                const now = new Date();
                let periods = 0;
                if (plan.interval === 'month') {
                    periods = Math.floor((now.getTime() - startDate.getTime()) / (30 * 24 * 60 * 60 * 1000)) + 1;
                }
                else if (plan.interval === 'year') {
                    periods = Math.floor((now.getTime() - startDate.getTime()) / (365 * 24 * 60 * 60 * 1000)) + 1;
                }
                totalRevenue = parseFloat(plan.price.toString()) * Math.max(periods, 0);
            }
            const totalExpenseAmount = await prisma_1.prisma.expense.aggregate({
                where: { user_id: parseInt(userId) },
                _sum: { amount: true },
            });
            const enrichedUser = {
                ...userWithoutPassword,
                createdAt: user.created_at,
                lastActiveAt: user.last_login,
                loginCount: user.login_count,
                totalExpenses: user._count.expenses,
                totalCategories: user._count.categories,
                totalUsageLogs: user._count.usage_logs,
                totalRevenue,
                totalExpenseAmount: parseFloat(totalExpenseAmount._sum.amount?.toString() || '0'),
                status: !user.is_active ? 'blocked' :
                    user.subscription_status === 'active' ? 'active' :
                        user.subscription_status === 'trialing' ? 'trial' : 'inactive',
                planType: user.plan_type,
            };
            res.json({
                success: true,
                data: enrichedUser,
            });
        }
        catch (error) {
            logger_1.default.error('Error getting user details:', error);
            res.status(500).json({
                success: false,
                message: 'Internal server error',
            });
        }
    }
    async getUsageStats(req, res) {
        try {
            const { startDate, endDate } = req.query;
            const stats = await usageService_1.usageService.getGlobalUsageStats(startDate, endDate);
            res.json({
                success: true,
                data: stats,
            });
        }
        catch (error) {
            logger_1.default.error('Error getting usage stats:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to get usage statistics',
            });
        }
    }
    async getRevenueData(req, res) {
        try {
            const { period = '12months' } = req.query;
            let startDate = new Date();
            switch (period) {
                case '7days':
                    startDate.setDate(startDate.getDate() - 7);
                    break;
                case '30days':
                    startDate.setDate(startDate.getDate() - 30);
                    break;
                case '3months':
                    startDate.setMonth(startDate.getMonth() - 3);
                    break;
                case '6months':
                    startDate.setMonth(startDate.getMonth() - 6);
                    break;
                case '12months':
                default:
                    startDate.setFullYear(startDate.getFullYear() - 1);
                    break;
            }
            const subscriptions = await prisma_1.prisma.subscription.findMany({
                where: {
                    created_at: {
                        gte: startDate,
                    },
                    status: 'active',
                },
                include: {
                    plan: true,
                },
                orderBy: {
                    created_at: 'asc',
                },
            });
            const revenueByMonth = {};
            let totalRevenue = 0;
            subscriptions.forEach(subscription => {
                const monthKey = subscription.created_at.toISOString().substring(0, 7);
                if (!revenueByMonth[monthKey]) {
                    revenueByMonth[monthKey] = 0;
                }
                const price = parseFloat(subscription.plan.price.toString());
                revenueByMonth[monthKey] += price;
                totalRevenue += price;
            });
            const chartData = Object.entries(revenueByMonth).map(([month, revenue]) => ({
                month,
                revenue,
                date: new Date(month + '-01'),
            })).sort((a, b) => a.date.getTime() - b.date.getTime());
            const currentMonth = new Date().toISOString().substring(0, 7);
            const lastMonth = new Date(new Date().setMonth(new Date().getMonth() - 1)).toISOString().substring(0, 7);
            const currentMonthRevenue = revenueByMonth[currentMonth] || 0;
            const lastMonthRevenue = revenueByMonth[lastMonth] || 0;
            const growthRate = lastMonthRevenue > 0 ? ((currentMonthRevenue - lastMonthRevenue) / lastMonthRevenue) * 100 : 0;
            res.json({
                success: true,
                data: {
                    chartData,
                    totalRevenue,
                    currentMonthRevenue,
                    lastMonthRevenue,
                    growthRate: Math.round(growthRate * 100) / 100,
                    period,
                },
            });
        }
        catch (error) {
            logger_1.default.error('Error getting revenue data:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to get revenue data',
            });
        }
    }
    async getActivity(req, res) {
        try {
            const { page = '1', limit = '50', timeRange = '24h' } = req.query;
            const offset = (parseInt(page) - 1) * parseInt(limit);
            let startDate = new Date();
            switch (timeRange) {
                case '1h':
                    startDate.setHours(startDate.getHours() - 1);
                    break;
                case '24h':
                default:
                    startDate.setDate(startDate.getDate() - 1);
                    break;
                case '7d':
                    startDate.setDate(startDate.getDate() - 7);
                    break;
                case '30d':
                    startDate.setDate(startDate.getDate() - 30);
                    break;
            }
            const [activities, total] = await Promise.all([
                prisma_1.prisma.usageLog.findMany({
                    where: {
                        created_at: {
                            gte: startDate,
                        },
                    },
                    include: {
                        user: {
                            select: {
                                id: true,
                                name: true,
                                email: true,
                            },
                        },
                    },
                    orderBy: { created_at: 'desc' },
                    skip: offset,
                    take: parseInt(limit),
                }),
                prisma_1.prisma.usageLog.count({
                    where: {
                        created_at: {
                            gte: startDate,
                        },
                    },
                }),
            ]);
            res.json({
                success: true,
                data: {
                    activities,
                    pagination: {
                        page: parseInt(page),
                        limit: parseInt(limit),
                        total,
                        pages: Math.ceil(total / parseInt(limit)),
                    },
                    timeRange,
                },
            });
        }
        catch (error) {
            logger_1.default.error('Error getting activity:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to get activity data',
            });
        }
    }
}
exports.default = new AdminController();
//# sourceMappingURL=adminController.js.map