"use strict";(self.webpackChunkexpense_tracker_frontend=self.webpackChunkexpense_tracker_frontend||[]).push([[842],{5842:(e,r,a)=>{a.r(r),a.d(r,{default:()=>g});var s=a(4985),t=a(893),l=(a(6540),a(9785)),n=a(888),i=a(7767),o=a(4976),d=a(8952),c=a(6103),m=a(6215),x=a(7074),p=a(5009),h=a(4848);const u=d.Ik({email:d.Yj().min(1,"Email-ul este obligatoriu").email("Email-ul nu este valid"),password:d.Yj().min(1,"Parola este obligatorie").min(6,"Parola trebuie să aibă cel puțin 6 caractere")}),g=()=>{const{login:e,isLoading:r}=(0,p.nc)(),a=(0,i.Zp)(),{register:d,handleSubmit:g,formState:{errors:y}}=(0,l.mN)({resolver:(0,t.u)(u),defaultValues:{email:"<EMAIL>",password:"admin123"}});return(0,h.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex flex-col",children:[(0,h.jsxs)("div",{className:"flex items-center justify-between p-6",children:[(0,h.jsxs)(o.N_,{to:"/",className:"flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors group",children:[(0,h.jsx)(s.A,{className:"w-5 h-5 group-hover:-translate-x-1 transition-transform"}),(0,h.jsx)("span",{className:"font-medium",children:"Înapoi la pagina principală"})]}),(0,h.jsx)(x.A,{size:"md",to:"/",animated:!0})]}),(0,h.jsx)("div",{className:"flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8",children:(0,h.jsx)("div",{className:"max-w-md w-full",children:(0,h.jsxs)("div",{className:"bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-8",children:[(0,h.jsxs)("div",{className:"text-center mb-8",children:[(0,h.jsx)("div",{className:"mb-6",children:(0,h.jsx)(x.A,{size:"lg",showText:!1,className:"justify-center"})}),(0,h.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Bun venit înapoi!"}),(0,h.jsx)("p",{className:"text-gray-600",children:"Conectează-te pentru a-ți accesa contul"})]}),(0,h.jsxs)("form",{className:"space-y-6",onSubmit:g(async r=>{try{const s=await e(r);if(s.success){const{user:e}=p.nc.getState();a("admin"===e?.role?"/app/admin/dashboard":"/app/dashboard")}else n.oR.error(s.message||"Eroare la autentificare")}catch(e){n.oR.error(e.message||"Eroare la autentificare")}}),children:[(0,h.jsxs)("div",{className:"space-y-5",children:[(0,h.jsx)("div",{children:(0,h.jsx)(m.Ay,{...d("email"),type:"email",label:"Adresa de email",placeholder:"<EMAIL>",error:y.email?.message,autoComplete:"email",className:"rounded-xl"})}),(0,h.jsx)("div",{children:(0,h.jsx)(m.Ay,{...d("password"),type:"password",label:"Parola",placeholder:"Introdu parola",error:y.password?.message,autoComplete:"current-password",className:"rounded-xl"})})]}),(0,h.jsxs)("div",{className:"flex items-center justify-between",children:[(0,h.jsxs)("div",{className:"flex items-center",children:[(0,h.jsx)("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded transition-colors"}),(0,h.jsx)("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-700",children:"Ține-mă minte"})]}),(0,h.jsx)("div",{className:"text-sm",children:(0,h.jsx)(o.N_,{to:"/forgot-password",className:"font-medium text-primary-600 hover:text-primary-700 transition-colors",children:"Ai uitat parola?"})})]}),(0,h.jsxs)("div",{className:"space-y-4",children:[(0,h.jsx)(c.Ay,{type:"submit",variant:"primary",size:"lg",className:"w-full rounded-xl bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 transform hover:scale-[1.02] transition-all duration-200 shadow-lg",loading:r,disabled:r,children:r?"Se conectează...":"Conectează-te"}),(0,h.jsxs)("div",{className:"relative",children:[(0,h.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,h.jsx)("div",{className:"w-full border-t border-gray-200"})}),(0,h.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,h.jsx)("span",{className:"px-4 bg-white text-gray-500",children:"sau"})})]}),(0,h.jsx)("div",{className:"text-center",children:(0,h.jsxs)("p",{className:"text-gray-600",children:["Nu ai cont încă?"," ",(0,h.jsx)(o.N_,{to:"/register",className:"font-semibold text-primary-600 hover:text-primary-700 transition-colors",children:"Creează un cont nou"})]})})]})]})]})})})]})}},6103:(e,r,a)=>{a.d(r,{Ay:()=>n});var s=a(4848),t=(a(6540),a(2392)),l=a(9264);const n=({children:e,variant:r="primary",size:a="md",loading:n=!1,disabled:i=!1,fullWidth:o=!1,leftIcon:d=null,rightIcon:c=null,className:m="",onClick:x,type:p="button",...h})=>{const u={primary:["bg-primary-600 text-white border-primary-600","hover:bg-primary-700 hover:border-primary-700","focus:ring-primary-500","disabled:bg-primary-300 disabled:border-primary-300"].join(" "),secondary:["bg-gray-600 text-white border-gray-600","hover:bg-gray-700 hover:border-gray-700","focus:ring-gray-500","disabled:bg-gray-300 disabled:border-gray-300"].join(" "),outline:["bg-transparent text-primary-600 border-primary-600","hover:bg-primary-50 hover:text-primary-700","focus:ring-primary-500","disabled:text-primary-300 disabled:border-primary-300"].join(" "),ghost:["bg-transparent text-gray-700 border-transparent","hover:bg-gray-100 hover:text-gray-900","focus:ring-gray-500","disabled:text-gray-400"].join(" "),danger:["bg-red-600 text-white border-red-600","hover:bg-red-700 hover:border-red-700","focus:ring-red-500","disabled:bg-red-300 disabled:border-red-300"].join(" "),success:["bg-green-600 text-white border-green-600","hover:bg-green-700 hover:border-green-700","focus:ring-green-500","disabled:bg-green-300 disabled:border-green-300"].join(" "),warning:["bg-yellow-600 text-white border-yellow-600","hover:bg-yellow-700 hover:border-yellow-700","focus:ring-yellow-500","disabled:bg-yellow-300 disabled:border-yellow-300"].join(" "),white:["bg-white text-gray-900 border-white","hover:bg-gray-50 hover:text-gray-900","focus:ring-gray-500","disabled:bg-gray-100 disabled:text-gray-400"].join(" ")},g={xs:"h-3 w-3",sm:"h-4 w-4",md:"h-4 w-4",lg:"h-5 w-5",xl:"h-6 w-6"},y=i||n;return(0,s.jsxs)("button",{type:p,onClick:e=>{y?e.preventDefault():x?.(e)},disabled:y,className:(0,t.cn)("inline-flex items-center justify-center","border font-medium rounded-lg","transition-all duration-200 ease-in-out","focus:outline-none focus:ring-2 focus:ring-offset-2","disabled:cursor-not-allowed disabled:opacity-60",u[r],{xs:"px-2 py-1 text-xs",sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base",xl:"px-8 py-4 text-lg"}[a],o&&"w-full",m),...h,children:[d&&!n&&(0,s.jsx)("span",{className:(0,t.cn)(g[a],e&&"mr-2"),children:d}),n&&(0,s.jsx)("span",{className:(0,t.cn)(e&&"mr-2"),children:(0,s.jsx)(l.Ay,{size:"xs"===a||"sm"===a?"sm":"md",color:"currentColor"})}),e&&(0,s.jsx)("span",{className:n?"opacity-70":"",children:e}),c&&!n&&(0,s.jsx)("span",{className:(0,t.cn)(g[a],e&&"ml-2"),children:c})]})}},6215:(e,r,a)=>{a.d(r,{Ay:()=>x});var s=a(4848),t=a(2509),l=a(72),n=a(3956),i=a(7117),o=a(4015),d=a(6540),c=a(2392);const m=(0,d.forwardRef)(({label:e,type:r="text",placeholder:a,value:m,onChange:x,onBlur:p,onFocus:h,error:u,success:g,hint:y,required:b=!1,disabled:f=!1,readOnly:j=!1,size:w="md",leftIcon:N,rightIcon:v,className:C="",inputClassName:A="",labelClassName:k="",id:I,name:V,...R},z)=>{const[S,H]=(0,d.useState)(!1),[_,B]=(0,d.useState)(!1),E=I||`input-${Math.random().toString(36).substr(2,9)}`,M="password"===r&&S?"text":r,T={sm:"h-4 w-4",md:"h-5 w-5",lg:"h-6 w-6"},Z=Boolean(u),F=Boolean(g)&&!Z;return Boolean(N||v||"password"===r),(0,s.jsxs)("div",{className:(0,c.cn)("w-full",C),children:[e&&(0,s.jsxs)("label",{htmlFor:E,className:(0,c.cn)("block text-sm font-medium mb-2",Z?"text-red-700":"text-gray-700",f&&"text-gray-400",k),children:[e,b&&(0,s.jsx)("span",{className:"text-red-500 ml-1","aria-label":"obligatoriu",children:"*"})]}),(0,s.jsxs)("div",{className:"relative",children:[N&&(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)("span",{className:(0,c.cn)(T[w],Z?"text-red-400":"text-gray-400"),children:N})}),(0,s.jsx)("input",{ref:z,id:E,name:V,type:M,value:m,onChange:x,onFocus:e=>{B(!0),h?.(e)},onBlur:e=>{B(!1),p?.(e)},placeholder:a,required:b,disabled:f,readOnly:j,className:(0,c.cn)("block w-full border rounded-lg transition-all duration-200","focus:outline-none focus:ring-2 focus:ring-offset-0","placeholder:text-gray-400",{sm:"px-3 py-2 text-sm",md:"px-4 py-2.5 text-sm",lg:"px-4 py-3 text-base"}[w],N&&"pl-10",(v||"password"===r)&&"pr-10",!f&&!j&&[Z?["border-red-300 text-red-900","focus:border-red-500 focus:ring-red-500"]:F?["border-green-300 text-green-900","focus:border-green-500 focus:ring-green-500"]:["border-gray-300 text-gray-900","focus:border-primary-500 focus:ring-primary-500","hover:border-gray-400"]],f&&["bg-gray-50 border-gray-200 text-gray-500","cursor-not-allowed"],j&&["bg-gray-50 border-gray-200","cursor-default"],A),...R}),(0,s.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:"password"===r?(0,s.jsx)("button",{type:"button",onClick:()=>{H(!S)},className:(0,c.cn)("text-gray-400 hover:text-gray-600 focus:outline-none",T[w]),"aria-label":S?"Ascunde parola":"Arată parola",children:S?(0,s.jsx)(t.A,{className:T[w]}):(0,s.jsx)(l.A,{className:T[w]})}):Z?(0,s.jsx)(n.A,{className:(0,c.cn)(T[w],"text-red-400")}):F?(0,s.jsx)(i.A,{className:(0,c.cn)(T[w],"text-green-400")}):v?(0,s.jsx)("span",{className:(0,c.cn)(T[w],"text-gray-400"),children:v}):null})]}),(u||g||y)&&(0,s.jsxs)("div",{className:"mt-2 flex items-start space-x-1",children:[(u||g)&&(0,s.jsx)("span",{className:"flex-shrink-0 mt-0.5",children:u?(0,s.jsx)(n.A,{className:"h-4 w-4 text-red-400"}):(0,s.jsx)(i.A,{className:"h-4 w-4 text-green-400"})}),y&&!u&&!g&&(0,s.jsx)(o.A,{className:"h-4 w-4 text-gray-400 flex-shrink-0 mt-0.5"}),(0,s.jsx)("p",{className:(0,c.cn)("text-sm",u?"text-red-600":g?"text-green-600":"text-gray-600"),children:u||g||y})]})]})});m.displayName="Input",(0,d.forwardRef)((e,r)=>(0,s.jsx)(m,{ref:r,type:"email",placeholder:"<EMAIL>",...e})).displayName="EmailInput",(0,d.forwardRef)((e,r)=>(0,s.jsx)(m,{ref:r,type:"password",placeholder:"••••••••",...e})).displayName="PasswordInput",(0,d.forwardRef)(({min:e,max:r,step:a=1,...t},l)=>(0,s.jsx)(m,{ref:l,type:"number",min:e,max:r,step:a,...t})).displayName="NumberInput",(0,d.forwardRef)(({onSearch:e,...r},a)=>(0,s.jsx)(m,{ref:a,type:"search",placeholder:"Caută...",onKeyDown:r=>{"Enter"===r.key&&e&&e(r.currentTarget.value)},...r})).displayName="SearchInput",(0,d.forwardRef)((e,r)=>(0,s.jsx)(m,{ref:r,type:"tel",placeholder:"+40 123 456 789",...e})).displayName="PhoneInput",(0,d.forwardRef)((e,r)=>(0,s.jsx)(m,{ref:r,type:"url",placeholder:"https://exemplu.com",...e})).displayName="UrlInput",(0,d.forwardRef)((e,r)=>(0,s.jsx)(m,{ref:r,type:"date",...e})).displayName="DateInput",(0,d.forwardRef)((e,r)=>(0,s.jsx)(m,{ref:r,type:"time",...e})).displayName="TimeInput",(0,d.forwardRef)((e,r)=>(0,s.jsx)(m,{ref:r,type:"datetime-local",...e})).displayName="DateTimeInput";const x=m},7074:(e,r,a)=>{a.d(r,{A:()=>n});var s=a(4848),t=(a(6540),a(4976)),l=a(2392);const n=({size:e="md",showText:r=!0,className:a="",to:n="/",animated:i=!1})=>{const o={sm:{container:"w-6 h-6",text:"text-sm font-semibold"},md:{container:"w-8 h-8",text:"text-lg font-bold"},lg:{container:"w-12 h-12",text:"text-xl font-bold"},xl:{container:"w-16 h-16",text:"text-2xl font-bold"}},d=(0,s.jsxs)("div",{className:(0,l.cn)("flex items-center space-x-3 group cursor-pointer",a),children:[(0,s.jsx)(()=>(0,s.jsx)("div",{className:(0,l.cn)("bg-gradient-to-br from-primary-500 to-primary-700 rounded-xl flex items-center justify-center shadow-lg",o[e].container,i&&"transition-all duration-300 hover:scale-110 hover:shadow-xl"),children:(0,s.jsxs)("svg",{viewBox:"0 0 24 24",fill:"none",className:(0,l.cn)("text-white","sm"===e?"w-3 h-3":"md"===e?"w-4 h-4":"lg"===e?"w-6 h-6":"w-8 h-8"),children:[(0,s.jsx)("path",{d:"M12 2C13.1 2 14 2.9 14 4V6H16C17.1 6 18 6.9 18 8V18C18 19.1 17.1 20 16 20H8C6.9 20 6 19.1 6 18V8C6 6.9 6.9 6 8 6H10V4C10 2.9 10.9 2 12 2ZM12 4V6H12V4ZM8 8V18H16V8H8ZM10 10H14V12H10V10ZM10 14H14V16H10V14Z",fill:"currentColor"}),(0,s.jsx)("circle",{cx:"12",cy:"13",r:"1.5",fill:"currentColor",opacity:"0.7"})]})}),{}),r&&(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)("span",{className:(0,l.cn)("text-gray-900 group-hover:text-primary-600 transition-colors duration-200",o[e].text),children:"FinanceNinja"}),"lg"===e||"xl"===e?(0,s.jsx)("span",{className:"text-xs text-gray-500 -mt-1",children:"Smart Expense Tracker"}):null]})]});return n?(0,s.jsx)(t.N_,{to:n,className:"inline-block",children:d}):d}}}]);