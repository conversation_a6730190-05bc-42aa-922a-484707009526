"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
class SubscriptionService {
    async getAvailablePlans() {
        try {
            return [
                {
                    id: '1',
                    name: 'Free',
                    description: 'Basic plan with limited features',
                    price: 0,
                    currency: 'RON',
                    interval: 'month',
                    features: {
                        expenses: true,
                        categories: true,
                        reports: false,
                        export: false
                    },
                    limits: {
                        expenses_per_month: 50,
                        categories: 5,
                        exports_per_month: 0
                    },
                    stripe_id: 'free_plan'
                },
                {
                    id: '2',
                    name: 'Premium',
                    description: 'Full access to all features',
                    price: 29.99,
                    currency: 'RON',
                    interval: 'month',
                    features: {
                        expenses: true,
                        categories: true,
                        reports: true,
                        export: true
                    },
                    limits: {
                        expenses_per_month: -1,
                        categories: -1,
                        exports_per_month: -1
                    },
                    stripe_id: 'price_premium_monthly'
                }
            ];
        }
        catch (error) {
            console.error('Error getting available plans:', error);
            throw error;
        }
    }
    async checkUserLimits(userId, action) {
        try {
            return true;
        }
        catch (error) {
            console.error('Error checking user limits:', error);
            return false;
        }
    }
    async updateUsage(userId, action) {
        try {
            console.log(`Usage updated for user ${userId}, action: ${action}`);
        }
        catch (error) {
            console.error('Error updating usage:', error);
        }
    }
    async getSubscriptionStats() {
        try {
            return {
                total_subscriptions: 0,
                active_subscriptions: 0,
                cancelled_subscriptions: 0,
                revenue_this_month: 0,
                revenue_last_month: 0
            };
        }
        catch (error) {
            console.error('Error getting subscription stats:', error);
            throw error;
        }
    }
    async syncPlansFromStripe() {
        try {
            console.log('Syncing plans from Stripe...');
        }
        catch (error) {
            console.error('Error syncing plans from Stripe:', error);
            throw error;
        }
    }
}
exports.default = new SubscriptionService();
//# sourceMappingURL=subscriptionService.js.map