const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

async function checkUsers() {
  try {
    console.log('Conectare la baza de date...');
    
    // Verifică ce tabele există
    const tablesResult = await pool.query("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'");
    console.log('\n=== TABELE DISPONIBILE ===');
    tablesResult.rows.forEach(table => {
      console.log(`Tabelă: ${table.table_name}`);
    });
    
    // Încearcă să găsească tabela utilizatorilor
    const userTableNames = ['User', 'users', 'user', 'Users'];
    let userTableName = null;
    
    for (const tableName of userTableNames) {
      try {
        await pool.query(`SELECT 1 FROM "${tableName}" LIMIT 1`);
        userTableName = tableName;
        break;
      } catch (e) {
        // Continuă să caute
      }
    }
    
    if (!userTableName) {
      console.log('\nNu s-a găsit tabela utilizatorilor!');
      return;
    }
    
    console.log(`\nTabela utilizatorilor găsită: ${userTableName}`);
    
    // Verifică toți utilizatorii
    const allUsersResult = await pool.query(`SELECT * FROM "${userTableName}" ORDER BY id`);
    
    console.log('\n=== TOȚI UTILIZATORII ===');
    console.log(`Total utilizatori: ${allUsersResult.rows.length}`);
    
    allUsersResult.rows.forEach(user => {
      console.log(`Utilizator:`, user);
    });
    
    // Verifică utilizatorii admin (dacă există coloana role)
    try {
      const adminUsersResult = await pool.query(`SELECT * FROM "${userTableName}" WHERE role = $1`, ['admin']);
      
      console.log('\n=== UTILIZATORI ADMIN ===');
      console.log(`Total admini: ${adminUsersResult.rows.length}`);
      
      adminUsersResult.rows.forEach(user => {
        console.log(`Admin:`, user);
      });
    } catch (e) {
      console.log('\nColoana "role" nu există sau altă eroare:', e.message);
    }
    
    // Verifică primul utilizator (ID = 1)
    try {
      const firstUserResult = await pool.query(`SELECT * FROM "${userTableName}" WHERE id = $1`, [1]);
      
      console.log('\n=== PRIMUL UTILIZATOR (ID=1) ===');
      if (firstUserResult.rows.length > 0) {
        const user = firstUserResult.rows[0];
        console.log(`Primul utilizator:`, user);
      } else {
        console.log('Nu există utilizator cu ID=1');
      }
    } catch (e) {
      console.log('\nEroare la căutarea primului utilizator:', e.message);
    }
    
  } catch (error) {
    console.error('Eroare:', error.message);
  } finally {
    await pool.end();
  }
}

checkUsers();