version: 2

updates:
  # Backend dependencies
  - package-ecosystem: "npm"
    directory: "/backend"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 5
    reviewers:
      - "your-username"
    assignees:
      - "your-username"
    labels:
      - "dependencies"
      - "backend"
    commit-message:
      prefix: "chore(backend)"
      include: "scope"
    ignore:
      # Ignore major version updates for critical packages
      - dependency-name: "prisma"
        update-types: ["version-update:semver-major"]
      - dependency-name: "@prisma/client"
        update-types: ["version-update:semver-major"]
      - dependency-name: "express"
        update-types: ["version-update:semver-major"]
      - dependency-name: "typescript"
        update-types: ["version-update:semver-major"]
    groups:
      prisma:
        patterns:
          - "prisma"
          - "@prisma/*"
      testing:
        patterns:
          - "jest"
          - "@types/jest"
          - "supertest"
          - "@types/supertest"
      eslint:
        patterns:
          - "eslint"
          - "@typescript-eslint/*"
          - "eslint-*"

  # Frontend dependencies
  - package-ecosystem: "npm"
    directory: "/frontend"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 5
    reviewers:
      - "your-username"
    assignees:
      - "your-username"
    labels:
      - "dependencies"
      - "frontend"
    commit-message:
      prefix: "chore(frontend)"
      include: "scope"
    ignore:
      # Ignore major version updates for critical packages
      - dependency-name: "react"
        update-types: ["version-update:semver-major"]
      - dependency-name: "react-dom"
        update-types: ["version-update:semver-major"]
      - dependency-name: "vite"
        update-types: ["version-update:semver-major"]
      - dependency-name: "typescript"
        update-types: ["version-update:semver-major"]
    groups:
      react:
        patterns:
          - "react"
          - "react-*"
          - "@types/react*"
      vite:
        patterns:
          - "vite"
          - "@vitejs/*"
          - "vite-*"
      testing:
        patterns:
          - "vitest"
          - "@testing-library/*"
          - "@vitest/*"
      eslint:
        patterns:
          - "eslint"
          - "@typescript-eslint/*"
          - "eslint-*"
      tailwind:
        patterns:
          - "tailwindcss"
          - "@tailwindcss/*"
          - "tailwind-*"

  # GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 3
    reviewers:
      - "your-username"
    assignees:
      - "your-username"
    labels:
      - "dependencies"
      - "github-actions"
    commit-message:
      prefix: "chore(ci)"
      include: "scope"

  # Docker dependencies
  - package-ecosystem: "docker"
    directory: "/backend"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 2
    reviewers:
      - "your-username"
    assignees:
      - "your-username"
    labels:
      - "dependencies"
      - "docker"
      - "backend"
    commit-message:
      prefix: "chore(docker)"
      include: "scope"

  - package-ecosystem: "docker"
    directory: "/frontend"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "UTC"
    open-pull-requests-limit: 2
    reviewers:
      - "your-username"
    assignees:
      - "your-username"
    labels:
      - "dependencies"
      - "docker"
      - "frontend"
    commit-message:
      prefix: "chore(docker)"
      include: "scope"
