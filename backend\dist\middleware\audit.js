"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.dataChangeAuditLogger = exports.adminAuditLogger = exports.auditLogger = void 0;
const uuid_1 = require("uuid");
const logger_1 = __importDefault(require("../utils/logger"));
const auditLogger = (req, res, next) => {
    req.auditId = (0, uuid_1.v4)();
    req.startTime = Date.now();
    const originalSend = res.send;
    const originalJson = res.json;
    res.send = function (data) {
        res.responseData = data;
        return originalSend.call(this, data);
    };
    res.json = function (data) {
        res.responseData = data;
        return originalJson.call(this, data);
    };
    res.on('finish', () => {
        const duration = Date.now() - (req.startTime || 0);
        const action = determineAction(req.method, req.route?.path || req.path);
        const auditData = {
            auditId: req.auditId || '',
            userId: req.user?.id || null,
            userEmail: req.user?.email || null,
            action,
            resource: extractResource(req.route?.path || req.path),
            resourceId: req.params?.id || null,
            method: req.method,
            url: req.originalUrl,
            path: req.path,
            statusCode: res.statusCode,
            duration,
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            referer: req.get('Referer'),
            timestamp: new Date().toISOString(),
            success: res.statusCode < 400,
            requestData: sanitizeRequestData(req),
            responseData: shouldLogResponse(action, res.statusCode)
                ? sanitizeResponseData(res.responseData)
                : null,
            sessionId: req.sessionID || null,
            metadata: {
                contentType: req.get('Content-Type'),
                contentLength: req.get('Content-Length'),
                acceptLanguage: req.get('Accept-Language'),
                origin: req.get('Origin')
            }
        };
        if (isSecuritySensitiveAction(action)) {
            logger_1.default.logSecurity('security_action', auditData);
        }
        else if (isBusinessCriticalAction(action)) {
            logger_1.default.logAudit('business_action', req.user?.id || 'anonymous', auditData);
        }
        else {
            logger_1.default.logRequest(req, res, duration);
        }
        if (res.statusCode >= 400) {
            logger_1.default.logSecurity('error_response', {
                ...auditData,
                errorType: categorizeError(res.statusCode),
                errorDetails: res.responseData
            });
        }
    });
    next();
};
exports.auditLogger = auditLogger;
function determineAction(method, path) {
    const pathSegments = path.split('/').filter(Boolean);
    const resource = pathSegments[1] || 'unknown';
    const actionMap = {
        'GET': {
            default: 'read',
            patterns: {
                '/auth/me': 'profile_view',
                '/auth/logout': 'logout',
                '/expenses/export': 'export_data',
                '/reports': 'generate_report'
            }
        },
        'POST': {
            default: 'create',
            patterns: {
                '/auth/register': 'register',
                '/auth/login': 'login',
                '/auth/forgot-password': 'forgot_password',
                '/auth/reset-password': 'reset_password',
                '/auth/verify-email': 'verify_email',
                '/expenses/bulk': 'bulk_create_expenses',
                '/categories/bulk': 'bulk_create_categories'
            }
        },
        'PUT': {
            default: 'update',
            patterns: {
                '/auth/change-password': 'change_password',
                '/users/profile': 'update_profile'
            }
        },
        'PATCH': {
            default: 'partial_update',
            patterns: {}
        },
        'DELETE': {
            default: 'delete',
            patterns: {
                '/auth/sessions': 'logout_all_sessions'
            }
        }
    };
    const methodActions = actionMap[method] || { default: 'unknown', patterns: {} };
    for (const [pattern, action] of Object.entries(methodActions.patterns)) {
        if (path.includes(pattern)) {
            return action;
        }
    }
    return `${methodActions.default}_${resource}`;
}
function extractResource(path) {
    const pathSegments = path.split('/').filter(Boolean);
    return pathSegments[1] || 'unknown';
}
function sanitizeRequestData(req) {
    const sensitiveFields = [
        'password', 'currentPassword', 'newPassword', 'token',
        'apiKey', 'secret', 'authorization', 'cookie'
    ];
    const sanitized = {
        query: { ...req.query },
        params: { ...req.params },
        body: { ...req.body }
    };
    function removeSensitiveData(obj) {
        if (typeof obj !== 'object' || obj === null)
            return obj;
        const cleaned = {};
        for (const [key, value] of Object.entries(obj)) {
            if (sensitiveFields.some(field => key.toLowerCase().includes(field.toLowerCase()))) {
                cleaned[key] = '[REDACTED]';
            }
            else if (typeof value === 'object') {
                cleaned[key] = removeSensitiveData(value);
            }
            else {
                cleaned[key] = value;
            }
        }
        return cleaned;
    }
    return removeSensitiveData(sanitized);
}
function sanitizeResponseData(data) {
    if (!data)
        return null;
    try {
        const parsed = typeof data === 'string' ? JSON.parse(data) : data;
        const stringified = JSON.stringify(parsed);
        if (stringified.length > 1000) {
            return {
                message: 'Response data truncated due to size',
                size: stringified.length,
                preview: stringified.substring(0, 500) + '...'
            };
        }
        return parsed;
    }
    catch (error) {
        return { error: 'Failed to parse response data' };
    }
}
function shouldLogResponse(action, statusCode) {
    if (statusCode >= 400)
        return true;
    const sensitiveActions = [
        'login', 'register', 'change_password', 'reset_password',
        'delete', 'bulk_create', 'export_data'
    ];
    return sensitiveActions.some(sensitive => action.includes(sensitive));
}
function isSecuritySensitiveAction(action) {
    const securityActions = [
        'login', 'register', 'logout', 'change_password', 'reset_password',
        'forgot_password', 'verify_email', 'delete', 'logout_all_sessions'
    ];
    return securityActions.some(sensitive => action.includes(sensitive));
}
function isBusinessCriticalAction(action) {
    const businessActions = [
        'create_expense', 'update_expense', 'delete_expense',
        'create_category', 'update_category', 'delete_category',
        'bulk_create', 'export_data', 'generate_report'
    ];
    return businessActions.some(critical => action.includes(critical));
}
function categorizeError(statusCode) {
    if (statusCode >= 400 && statusCode < 500) {
        const clientErrors = {
            400: 'bad_request',
            401: 'unauthorized',
            403: 'forbidden',
            404: 'not_found',
            409: 'conflict',
            422: 'validation_error',
            429: 'rate_limit_exceeded'
        };
        return clientErrors[statusCode] || 'client_error';
    }
    if (statusCode >= 500) {
        return 'server_error';
    }
    return 'unknown_error';
}
const adminAuditLogger = (req, res, next) => {
    if (!req.user || req.user.role !== 'admin') {
        return next();
    }
    const originalSend = res.send;
    const originalJson = res.json;
    res.send = function (data) {
        res.responseData = data;
        return originalSend.call(this, data);
    };
    res.json = function (data) {
        res.responseData = data;
        return originalJson.call(this, data);
    };
    res.on('finish', () => {
        const auditData = {
            adminId: req.user.id,
            adminEmail: req.user.email,
            action: determineAction(req.method, req.route?.path || req.path),
            targetResource: extractResource(req.route?.path || req.path),
            targetId: req.params?.id || null,
            method: req.method,
            url: req.originalUrl,
            statusCode: res.statusCode,
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            timestamp: new Date().toISOString(),
            requestData: sanitizeRequestData(req),
            responseData: sanitizeResponseData(res.responseData),
            success: res.statusCode < 400
        };
        logger_1.default.logAudit('admin_action', req.user.id, auditData);
    });
    next();
};
exports.adminAuditLogger = adminAuditLogger;
const dataChangeAuditLogger = (model, operation) => {
    return (req, res, next) => {
        const originalSend = res.send;
        const originalJson = res.json;
        res.send = function (data) {
            res.responseData = data;
            return originalSend.call(this, data);
        };
        res.json = function (data) {
            res.responseData = data;
            return originalJson.call(this, data);
        };
        res.on('finish', () => {
            if (res.statusCode < 400) {
                const auditData = {
                    userId: req.user?.id,
                    userEmail: req.user?.email,
                    model,
                    operation,
                    recordId: req.params?.id || null,
                    changes: operation === 'update' ? req.body : null,
                    timestamp: new Date().toISOString(),
                    ip: req.ip,
                    userAgent: req.get('User-Agent')
                };
                logger_1.default.logAudit('data_change', req.user?.id || 'anonymous', auditData);
            }
        });
        next();
    };
};
exports.dataChangeAuditLogger = dataChangeAuditLogger;
//# sourceMappingURL=audit.js.map