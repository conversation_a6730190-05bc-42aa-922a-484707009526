"use strict";(self.webpackChunkexpense_tracker_frontend=self.webpackChunkexpense_tracker_frontend||[]).push([[314],{1235:(e,r,t)=>{t.d(r,{nw:()=>o});var a=t(4848),s=t(5575),n=t(6304),l=(t(6540),t(2392)),i=t(6103);const o=({currentPage:e=1,totalPages:r=1,onPageChange:t,size:o="md",disabled:c=!1,className:d="",showPageInfo:m=!0,...u})=>{const g=Math.max(1,Math.min(e,r)),x=Math.max(1,r);return(0,a.jsxs)("div",{className:(0,l.cn)("flex items-center justify-between",d),...u,children:[(0,a.jsx)(i.Ay,{variant:"outline",size:o,onClick:()=>{g>1&&t?.(g-1)},disabled:c||1===g,leftIcon:(0,a.jsx)(s.A,{className:"w-4 h-4"}),children:"Anterior"}),m&&(0,a.jsxs)("span",{className:"text-sm text-gray-700",children:["Pagina ",g," din ",x]}),(0,a.jsx)(i.Ay,{variant:"outline",size:o,onClick:()=>{g<x&&t?.(g+1)},disabled:c||g===x,rightIcon:(0,a.jsx)(n.A,{className:"w-4 h-4"}),children:"Următorul"})]})}},2552:(e,r,t)=>{t.d(r,{Ay:()=>o});var a=t(4848),s=t(4576),n=t(6540),l=t(961),i=t(2392);t(6103),t(9264);const o=({isOpen:e=!1,onClose:r,children:t,size:o="md",closeOnOverlayClick:c=!0,closeOnEscape:d=!0,showCloseButton:m=!0,className:u="",overlayClassName:g="",preventBodyScroll:x=!0,...h})=>{const p=(0,n.useRef)(null);if(((e,r)=>{(0,n.useEffect)(()=>{if(!e||!r.current)return;const t=r.current,a=t.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'),s=a[0],n=a[a.length-1],l=e=>{"Tab"===e.key&&(e.shiftKey?document.activeElement===s&&(n?.focus(),e.preventDefault()):document.activeElement===n&&(s?.focus(),e.preventDefault()))};return t.addEventListener("keydown",l),s?.focus(),()=>{t.removeEventListener("keydown",l)}},[e,r])})(e,p),x&&(e=>{(0,n.useEffect)(()=>{if(e){const e=window.getComputedStyle(document.body).overflow;return document.body.style.overflow="hidden",()=>{document.body.style.overflow=e}}},[e])})(e),(0,n.useEffect)(()=>{if(!e||!d)return;const t=e=>{"Escape"===e.key&&r?.()};return document.addEventListener("keydown",t),()=>document.removeEventListener("keydown",t)},[e,d,r]),!e)return null;const y=(0,a.jsx)("div",{className:(0,i.cn)("fixed inset-0 z-50 flex items-center justify-center p-4","bg-black bg-opacity-50 backdrop-blur-sm",g),onClick:e=>{c&&e.target===e.currentTarget&&r?.()},role:"dialog","aria-modal":"true","aria-labelledby":"modal-title",children:(0,a.jsxs)("div",{ref:p,className:(0,i.cn)("relative w-full bg-white rounded-lg shadow-xl","transform transition-all duration-300 ease-out","animate-scale-in",{xs:"max-w-xs",sm:"max-w-sm",md:"max-w-md",lg:"max-w-lg",xl:"max-w-xl","2xl":"max-w-2xl","3xl":"max-w-3xl","4xl":"max-w-4xl","5xl":"max-w-5xl","6xl":"max-w-6xl",full:"max-w-full"}[o],u),...h,children:[m&&(0,a.jsx)("button",{onClick:r,className:(0,i.cn)("absolute top-4 right-4 z-10","p-1 rounded-md text-gray-400 hover:text-gray-600","hover:bg-gray-100 transition-colors duration-200","focus:outline-none focus:ring-2 focus:ring-primary-500"),"aria-label":"Închide modal",children:(0,a.jsx)(s.A,{className:"w-5 h-5"})}),t]})});return(0,l.createPortal)(y,document.body)}},5191:(e,r,t)=>{t.d(r,{bQ:()=>v});var a=t(4848),s=t(1371),n=t(5410),l=t(3638),i=t(5575),o=t(6304),c=t(8331),d=t(6540),m=t(2392),u=t(6103),g=t(9264);const x=({children:e,className:r="",striped:t=!1,bordered:s=!1,hover:n=!1,compact:l=!1,...i})=>(0,a.jsx)("div",{className:"overflow-x-auto",children:(0,a.jsx)("table",{className:(0,m.cn)("min-w-full divide-y divide-gray-200",t&&"divide-y-0",s&&"border border-gray-200",r),...i,children:e})}),h=({children:e,className:r="",...t})=>(0,a.jsx)("thead",{className:(0,m.cn)("bg-gray-50",r),...t,children:e}),p=({children:e,className:r="",striped:t=!1,hover:s=!1,...n})=>(0,a.jsx)("tbody",{className:(0,m.cn)("bg-white divide-y divide-gray-200",t&&"[&>tr:nth-child(even)]:bg-gray-50",s&&"[&>tr]:hover:bg-gray-50 [&>tr]:transition-colors",r),...n,children:e}),y=({children:e,className:r="",clickable:t=!1,onClick:s,selected:n=!1,...l})=>{const i=t||Boolean(s);return(0,a.jsx)("tr",{className:(0,m.cn)(i&&["cursor-pointer","hover:bg-gray-50","focus:bg-gray-50 focus:outline-none"],n&&"bg-primary-50",r),onClick:s,role:i?"button":void 0,tabIndex:i?0:void 0,onKeyDown:i?e=>{"Enter"!==e.key&&" "!==e.key||(e.preventDefault(),s?.(e))}:void 0,...l,children:e})},b=({children:e,className:r="",sortable:t=!1,sortDirection:l=null,onSort:i,align:o="left",width:c,...d})=>(0,a.jsx)("th",{className:(0,m.cn)("px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider",{left:"text-left",center:"text-center",right:"text-right"}[o],t&&"cursor-pointer hover:bg-gray-100 select-none",r),style:{width:c},onClick:()=>{t&&i&&i("asc"===l?"desc":"asc")},...d,children:(0,a.jsxs)("div",{className:(0,m.cn)("flex items-center gap-1","center"===o&&"justify-center","right"===o&&"justify-end"),children:[(0,a.jsx)("span",{children:e}),t&&(0,a.jsxs)("span",{className:"flex flex-col",children:[(0,a.jsx)(s.A,{className:(0,m.cn)("w-3 h-3 -mb-1","asc"===l?"text-gray-900":"text-gray-400")}),(0,a.jsx)(n.A,{className:(0,m.cn)("w-3 h-3","desc"===l?"text-gray-900":"text-gray-400")})]})]})}),f=({children:e,className:r="",align:t="left",width:s,truncate:n=!1,...l})=>(0,a.jsx)("td",{className:(0,m.cn)("px-6 py-4 whitespace-nowrap text-sm text-gray-900",{left:"text-left",center:"text-center",right:"text-right"}[t],n&&"truncate max-w-0",r),style:{width:s},...l,children:e}),v=({data:e=[],columns:r=[],loading:t=!1,emptyMessage:s="Nu există date de afișat",sortable:n=!0,paginated:l=!0,pageSize:i=10,currentPage:o=1,totalItems:c,onPageChange:u,onSort:v,onRowClick:j,selectedRows:N=[],onRowSelect:k,selectable:C=!1,className:A="",...S})=>{const[z,E]=(0,d.useState)({key:null,direction:null}),[I,P]=(0,d.useState)(o),R=(0,d.useMemo)(()=>n&&z.key&&!v?[...e].sort((e,r)=>{const t=z.key?e[z.key]:null,a=z.key?r[z.key]:null;if(t===a)return 0;const s=t<a?-1:1;return"desc"===z.direction?-s:s}):e,[e,z,n,v]),D=(0,d.useMemo)(()=>{if(!l||u)return R;const e=(I-1)*i;return R.slice(e,e+i)},[R,l,I,i,u]),M=(e,r)=>N.some(r=>JSON.stringify(r)===JSON.stringify(e)),O=Math.ceil((c||R.length)/i),L=u?o:I;return(0,a.jsxs)("div",{className:(0,m.cn)("space-y-4",A),children:[(0,a.jsxs)(x,{striped:!0,hover:!0,...S,children:[(0,a.jsx)(h,{children:(0,a.jsxs)(y,{children:[C&&(0,a.jsx)(b,{width:"50px",children:(0,a.jsx)("input",{type:"checkbox",checked:N.length===D.length&&D.length>0,onChange:()=>{k&&(N.length===D.length?k([],"clear"):k(D,"all"))},className:"rounded border-gray-300 text-primary-600 focus:ring-primary-500"})}),r.map(e=>(0,a.jsx)(b,{sortable:n&&!1!==e.sortable,sortDirection:z.key===e.key?z.direction:null,onSort:r=>((e,r)=>{v?v(e,r):E({key:e,direction:r})})(e.key,r),align:e.align,width:e.width,children:e.title},e.key))]})}),(0,a.jsx)(p,{children:t?(0,a.jsx)(y,{children:(0,a.jsx)(f,{colSpan:r.length+(C?1:0),className:"text-center py-8",children:(0,a.jsx)(g.Ay,{size:"md",className:"mx-auto"})})}):0===D.length?(0,a.jsx)(y,{children:(0,a.jsx)(f,{colSpan:r.length+(C?1:0),className:"text-center py-8 text-gray-500",children:s})}):D.map((e,t)=>(0,a.jsxs)(y,{clickable:Boolean(j),onClick:()=>j?.(e,t),selected:M(e),children:[C&&(0,a.jsx)(f,{children:(0,a.jsx)("input",{type:"checkbox",checked:M(e),onChange:()=>((e,r)=>{k&&k(e,r.toString())})(e,t),onClick:e=>e.stopPropagation(),className:"rounded border-gray-300 text-primary-600 focus:ring-primary-500"})}),r.map(r=>(0,a.jsx)(f,{align:r.align,width:r.width,truncate:r.truncate,children:r.render?r.render(e[r.key],e,t):e[r.key]},r.key))]},t))})]}),l&&O>1&&(0,a.jsx)(w,{currentPage:L,totalPages:O,onPageChange:e=>{u?u(e):P(e)},totalItems:c||R.length,pageSize:i})]})},w=({currentPage:e=1,totalPages:r=1,onPageChange:t,totalItems:s=0,pageSize:n=10,showInfo:d=!0,showPageNumbers:g=!0,maxPageNumbers:x=5,className:h=""})=>{const p=(e-1)*n+1,y=Math.min(e*n,s),b=(()=>{const t=[],a=Math.floor(x/2);let s=Math.max(1,e-a);const n=Math.min(r,s+x-1);n-s+1<x&&(s=Math.max(1,n-x+1));for(let e=s;e<=n;e++)t.push(e);return t})();return(0,a.jsxs)("div",{className:(0,m.cn)("flex items-center justify-between px-4 py-3 bg-white border-t border-gray-200",h),children:[d&&(0,a.jsxs)("div",{className:"flex-1 flex justify-between sm:hidden",children:[(0,a.jsx)(u.Ay,{variant:"outline",size:"sm",onClick:()=>t?.(e-1),disabled:e<=1,children:"Anterior"}),(0,a.jsx)(u.Ay,{variant:"outline",size:"sm",onClick:()=>t?.(e+1),disabled:e>=r,children:"Următor"})]}),(0,a.jsxs)("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[d&&(0,a.jsx)("div",{children:(0,a.jsxs)("p",{className:"text-sm text-gray-700",children:["Afișează"," ",(0,a.jsx)("span",{className:"font-medium",children:p})," ","-"," ",(0,a.jsx)("span",{className:"font-medium",children:y})," ","din"," ",(0,a.jsx)("span",{className:"font-medium",children:s})," ","rezultate"]})}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(u.Ay,{variant:"outline",size:"sm",onClick:()=>t?.(1),disabled:e<=1,className:"p-2",children:(0,a.jsx)(l.A,{className:"w-4 h-4"})}),(0,a.jsx)(u.Ay,{variant:"outline",size:"sm",onClick:()=>t?.(e-1),disabled:e<=1,className:"p-2",children:(0,a.jsx)(i.A,{className:"w-4 h-4"})}),g&&b.map(r=>(0,a.jsx)(u.Ay,{variant:r===e?"primary":"outline",size:"sm",onClick:()=>t?.(r),className:"min-w-[2.5rem]",children:r},r)),(0,a.jsx)(u.Ay,{variant:"outline",size:"sm",onClick:()=>t?.(e+1),disabled:e>=r,className:"p-2",children:(0,a.jsx)(o.A,{className:"w-4 h-4"})}),(0,a.jsx)(u.Ay,{variant:"outline",size:"sm",onClick:()=>t?.(r),disabled:e>=r,className:"p-2",children:(0,a.jsx)(c.A,{className:"w-4 h-4"})})]})]})]})}},6103:(e,r,t)=>{t.d(r,{Ay:()=>l});var a=t(4848),s=(t(6540),t(2392)),n=t(9264);const l=({children:e,variant:r="primary",size:t="md",loading:l=!1,disabled:i=!1,fullWidth:o=!1,leftIcon:c=null,rightIcon:d=null,className:m="",onClick:u,type:g="button",...x})=>{const h={primary:["bg-primary-600 text-white border-primary-600","hover:bg-primary-700 hover:border-primary-700","focus:ring-primary-500","disabled:bg-primary-300 disabled:border-primary-300"].join(" "),secondary:["bg-gray-600 text-white border-gray-600","hover:bg-gray-700 hover:border-gray-700","focus:ring-gray-500","disabled:bg-gray-300 disabled:border-gray-300"].join(" "),outline:["bg-transparent text-primary-600 border-primary-600","hover:bg-primary-50 hover:text-primary-700","focus:ring-primary-500","disabled:text-primary-300 disabled:border-primary-300"].join(" "),ghost:["bg-transparent text-gray-700 border-transparent","hover:bg-gray-100 hover:text-gray-900","focus:ring-gray-500","disabled:text-gray-400"].join(" "),danger:["bg-red-600 text-white border-red-600","hover:bg-red-700 hover:border-red-700","focus:ring-red-500","disabled:bg-red-300 disabled:border-red-300"].join(" "),success:["bg-green-600 text-white border-green-600","hover:bg-green-700 hover:border-green-700","focus:ring-green-500","disabled:bg-green-300 disabled:border-green-300"].join(" "),warning:["bg-yellow-600 text-white border-yellow-600","hover:bg-yellow-700 hover:border-yellow-700","focus:ring-yellow-500","disabled:bg-yellow-300 disabled:border-yellow-300"].join(" "),white:["bg-white text-gray-900 border-white","hover:bg-gray-50 hover:text-gray-900","focus:ring-gray-500","disabled:bg-gray-100 disabled:text-gray-400"].join(" ")},p={xs:"h-3 w-3",sm:"h-4 w-4",md:"h-4 w-4",lg:"h-5 w-5",xl:"h-6 w-6"},y=i||l;return(0,a.jsxs)("button",{type:g,onClick:e=>{y?e.preventDefault():u?.(e)},disabled:y,className:(0,s.cn)("inline-flex items-center justify-center","border font-medium rounded-lg","transition-all duration-200 ease-in-out","focus:outline-none focus:ring-2 focus:ring-offset-2","disabled:cursor-not-allowed disabled:opacity-60",h[r],{xs:"px-2 py-1 text-xs",sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base",xl:"px-8 py-4 text-lg"}[t],o&&"w-full",m),...x,children:[c&&!l&&(0,a.jsx)("span",{className:(0,s.cn)(p[t],e&&"mr-2"),children:c}),l&&(0,a.jsx)("span",{className:(0,s.cn)(e&&"mr-2"),children:(0,a.jsx)(n.Ay,{size:"xs"===t||"sm"===t?"sm":"md",color:"currentColor"})}),e&&(0,a.jsx)("span",{className:l?"opacity-70":"",children:e}),d&&!l&&(0,a.jsx)("span",{className:(0,s.cn)(p[t],e&&"ml-2"),children:d})]})}},6215:(e,r,t)=>{t.d(r,{Ay:()=>u});var a=t(4848),s=t(2509),n=t(72),l=t(3956),i=t(7117),o=t(4015),c=t(6540),d=t(2392);const m=(0,c.forwardRef)(({label:e,type:r="text",placeholder:t,value:m,onChange:u,onBlur:g,onFocus:x,error:h,success:p,hint:y,required:b=!1,disabled:f=!1,readOnly:v=!1,size:w="md",leftIcon:j,rightIcon:N,className:k="",inputClassName:C="",labelClassName:A="",id:S,name:z,...E},I)=>{const[P,R]=(0,c.useState)(!1),[D,M]=(0,c.useState)(!1),O=S||`input-${Math.random().toString(36).substr(2,9)}`,L="password"===r&&P?"text":r,B={sm:"h-4 w-4",md:"h-5 w-5",lg:"h-6 w-6"},T=Boolean(h),F=Boolean(p)&&!T;return Boolean(j||N||"password"===r),(0,a.jsxs)("div",{className:(0,d.cn)("w-full",k),children:[e&&(0,a.jsxs)("label",{htmlFor:O,className:(0,d.cn)("block text-sm font-medium mb-2",T?"text-red-700":"text-gray-700",f&&"text-gray-400",A),children:[e,b&&(0,a.jsx)("span",{className:"text-red-500 ml-1","aria-label":"obligatoriu",children:"*"})]}),(0,a.jsxs)("div",{className:"relative",children:[j&&(0,a.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,a.jsx)("span",{className:(0,d.cn)(B[w],T?"text-red-400":"text-gray-400"),children:j})}),(0,a.jsx)("input",{ref:I,id:O,name:z,type:L,value:m,onChange:u,onFocus:e=>{M(!0),x?.(e)},onBlur:e=>{M(!1),g?.(e)},placeholder:t,required:b,disabled:f,readOnly:v,className:(0,d.cn)("block w-full border rounded-lg transition-all duration-200","focus:outline-none focus:ring-2 focus:ring-offset-0","placeholder:text-gray-400",{sm:"px-3 py-2 text-sm",md:"px-4 py-2.5 text-sm",lg:"px-4 py-3 text-base"}[w],j&&"pl-10",(N||"password"===r)&&"pr-10",!f&&!v&&[T?["border-red-300 text-red-900","focus:border-red-500 focus:ring-red-500"]:F?["border-green-300 text-green-900","focus:border-green-500 focus:ring-green-500"]:["border-gray-300 text-gray-900","focus:border-primary-500 focus:ring-primary-500","hover:border-gray-400"]],f&&["bg-gray-50 border-gray-200 text-gray-500","cursor-not-allowed"],v&&["bg-gray-50 border-gray-200","cursor-default"],C),...E}),(0,a.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center",children:"password"===r?(0,a.jsx)("button",{type:"button",onClick:()=>{R(!P)},className:(0,d.cn)("text-gray-400 hover:text-gray-600 focus:outline-none",B[w]),"aria-label":P?"Ascunde parola":"Arată parola",children:P?(0,a.jsx)(s.A,{className:B[w]}):(0,a.jsx)(n.A,{className:B[w]})}):T?(0,a.jsx)(l.A,{className:(0,d.cn)(B[w],"text-red-400")}):F?(0,a.jsx)(i.A,{className:(0,d.cn)(B[w],"text-green-400")}):N?(0,a.jsx)("span",{className:(0,d.cn)(B[w],"text-gray-400"),children:N}):null})]}),(h||p||y)&&(0,a.jsxs)("div",{className:"mt-2 flex items-start space-x-1",children:[(h||p)&&(0,a.jsx)("span",{className:"flex-shrink-0 mt-0.5",children:h?(0,a.jsx)(l.A,{className:"h-4 w-4 text-red-400"}):(0,a.jsx)(i.A,{className:"h-4 w-4 text-green-400"})}),y&&!h&&!p&&(0,a.jsx)(o.A,{className:"h-4 w-4 text-gray-400 flex-shrink-0 mt-0.5"}),(0,a.jsx)("p",{className:(0,d.cn)("text-sm",h?"text-red-600":p?"text-green-600":"text-gray-600"),children:h||p||y})]})]})});m.displayName="Input",(0,c.forwardRef)((e,r)=>(0,a.jsx)(m,{ref:r,type:"email",placeholder:"<EMAIL>",...e})).displayName="EmailInput",(0,c.forwardRef)((e,r)=>(0,a.jsx)(m,{ref:r,type:"password",placeholder:"••••••••",...e})).displayName="PasswordInput",(0,c.forwardRef)(({min:e,max:r,step:t=1,...s},n)=>(0,a.jsx)(m,{ref:n,type:"number",min:e,max:r,step:t,...s})).displayName="NumberInput",(0,c.forwardRef)(({onSearch:e,...r},t)=>(0,a.jsx)(m,{ref:t,type:"search",placeholder:"Caută...",onKeyDown:r=>{"Enter"===r.key&&e&&e(r.currentTarget.value)},...r})).displayName="SearchInput",(0,c.forwardRef)((e,r)=>(0,a.jsx)(m,{ref:r,type:"tel",placeholder:"+40 123 456 789",...e})).displayName="PhoneInput",(0,c.forwardRef)((e,r)=>(0,a.jsx)(m,{ref:r,type:"url",placeholder:"https://exemplu.com",...e})).displayName="UrlInput",(0,c.forwardRef)((e,r)=>(0,a.jsx)(m,{ref:r,type:"date",...e})).displayName="DateInput",(0,c.forwardRef)((e,r)=>(0,a.jsx)(m,{ref:r,type:"time",...e})).displayName="TimeInput",(0,c.forwardRef)((e,r)=>(0,a.jsx)(m,{ref:r,type:"datetime-local",...e})).displayName="DateTimeInput";const u=m},8724:(e,r,t)=>{t.d(r,{Wy:()=>h,l6:()=>x});var a=t(4848),s=t(5410),n=t(2933),l=t(6540),i=t.n(l),o=t(2392),c=t(9264);const d=({trigger:e,children:r,isOpen:t,onToggle:s,position:n="bottom-left",offset:c=8,className:d="",menuClassName:m="",disabled:u=!1,closeOnSelect:g=!0,...x})=>{const[h,p]=(0,l.useState)(!1),y=(0,l.useRef)(null),b=void 0!==t?t:h,f=()=>{s?s(!1):p(!1)};var v,w;return v=y,w=f,(0,l.useEffect)(()=>{const e=e=>{v.current&&!v.current.contains(e.target)&&w()};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[v,w]),(0,a.jsxs)("div",{ref:y,className:(0,o.cn)("relative inline-block",d),...x,children:[(0,a.jsx)("div",{onClick:()=>{u||(s?s(!b):p(!b))},children:e}),b&&(0,a.jsx)("div",{className:(0,o.cn)("absolute z-50 min-w-full","bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5","transform transition-all duration-200 ease-out","animate-scale-in origin-top",{"top-left":"bottom-full left-0 mb-2","top-right":"bottom-full right-0 mb-2","bottom-left":"top-full left-0 mt-2","bottom-right":"top-full right-0 mt-2",left:"right-full top-0 mr-2",right:"left-full top-0 ml-2"}[n],m),style:{marginTop:n.includes("bottom")?c:void 0},children:(0,a.jsx)("div",{className:"py-1",children:i().Children.map(r,e=>i().isValidElement(e)?i().cloneElement(e,{onSelect:g?()=>{e.props.onSelect?.(),f()}:e.props.onSelect}):e)})})]})},m=({children:e,onClick:r,onSelect:t,disabled:s=!1,active:n=!1,className:l="",icon:i,shortcut:c,...d})=>(0,a.jsxs)("button",{type:"button",className:(0,o.cn)("w-full text-left px-4 py-2 text-sm","flex items-center justify-between","transition-colors duration-150",s?"text-gray-400 cursor-not-allowed":["text-gray-700 hover:bg-gray-100 hover:text-gray-900","focus:bg-gray-100 focus:text-gray-900 focus:outline-none"],n&&"bg-primary-50 text-primary-700",l),onClick:e=>{s||(r?.(e),t?.())},disabled:s,...d,children:[(0,a.jsxs)("div",{className:"flex items-center gap-3 min-w-0 flex-1",children:[i&&(0,a.jsx)("span",{className:"flex-shrink-0",children:i}),(0,a.jsx)("span",{className:"truncate",children:e})]}),c&&(0,a.jsx)("span",{className:"text-xs text-gray-400 ml-2",children:c})]}),u=({className:e=""})=>(0,a.jsx)("div",{className:(0,o.cn)("border-t border-gray-100 my-1",e)}),g=({children:e,className:r="",...t})=>(0,a.jsx)("div",{className:(0,o.cn)("px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider",r),...t,children:e}),x=({value:e,onChange:r,options:t=[],placeholder:i="Selectează o opțiune",disabled:u=!1,loading:g=!1,error:x=!1,searchable:h=!1,clearable:p=!1,multiple:y=!1,className:b="",buttonClassName:f="",optionClassName:v="",menuClassName:w="",renderOption:j,renderValue:N,getOptionLabel:k=e=>e?.label||e,getOptionValue:C=e=>e?.value||e,offset:A=8,...S})=>{const[z,E]=(0,l.useState)(!1),[I,P]=(0,l.useState)(""),R=(0,l.useRef)(null),D=y?Array.isArray(e)?e:[]:e?[e]:[],M=h&&I?t.filter(e=>k(e).toLowerCase().includes(I.toLowerCase())):t,O=((e,r,t,a)=>{const[s,n]=(0,l.useState)(-1);return(0,l.useEffect)(()=>{if(!e)return void n(-1);const l=e=>{switch(e.key){case"ArrowDown":e.preventDefault(),n(e=>e<r.length-1?e+1:0);break;case"ArrowUp":e.preventDefault(),n(e=>e>0?e-1:r.length-1);break;case"Enter":case" ":e.preventDefault(),s>=0&&r[s]&&t(r[s]);break;case"Escape":e.preventDefault(),a()}};return document.addEventListener("keydown",l),()=>document.removeEventListener("keydown",l)},[e,r,s,t,a]),s})(z,M,L,()=>E(!1));function L(e){if(y){const t=C(e);let a;a=D.some(e=>C(e)===t)?D.filter(e=>C(e)!==t):[...D,e],r?.(a)}else r?.(e),E(!1);P("")}return(0,a.jsxs)(d,{isOpen:z,onToggle:E,className:b,menuClassName:w,offset:A,trigger:(0,a.jsxs)("button",{type:"button",className:(0,o.cn)("relative w-full bg-white border rounded-md shadow-sm pl-3 pr-10 py-2 text-left","focus:outline-none focus:ring-1 focus:border-primary-500",u?"bg-gray-50 text-gray-500 cursor-not-allowed border-gray-200":"cursor-pointer border-gray-300 hover:border-gray-400",x&&"border-red-300 focus:border-red-500 focus:ring-red-500",f),disabled:u||g,children:[(0,a.jsx)("span",{className:(0,o.cn)("block truncate",!e&&"text-gray-500"),children:N?N(e):y?0===D.length?i:1===D.length?k(D[0]):`${D.length} opțiuni selectate`:e?k(e):i}),(0,a.jsx)("span",{className:"absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none",children:g?(0,a.jsx)(c.Ay,{size:"sm"}):p&&e&&!u?(0,a.jsx)("button",{type:"button",className:"p-1 hover:bg-gray-100 rounded pointer-events-auto",onClick:e=>{e.stopPropagation(),r?.(y?[]:null)},children:(0,a.jsx)("svg",{className:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}):(0,a.jsx)(s.A,{className:(0,o.cn)("w-5 h-5 text-gray-400 transition-transform duration-200",z&&"transform rotate-180")})})]}),closeOnSelect:!y,children:[h&&(0,a.jsx)("div",{className:"p-2 border-b border-gray-100",children:(0,a.jsx)("input",{ref:R,type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500",placeholder:"Caută...",value:I,onChange:e=>P(e.target.value),onClick:e=>e.stopPropagation()})}),(0,a.jsx)("div",{className:"max-h-60 overflow-auto",children:0===M.length?(0,a.jsx)("div",{className:"px-4 py-2 text-sm text-gray-500",children:I?"Nu s-au găsit rezultate":"Nu există opțiuni"}):M.map((e,r)=>{const t=(e=>{const r=C(e);return D.some(e=>C(e)===r)})(e),s=r===O;return(0,a.jsx)(m,{onClick:()=>L(e),active:s,className:(0,o.cn)(t&&"bg-primary-50 text-primary-700",v),children:(0,a.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,a.jsx)("span",{children:j?(0,a.jsx)(a.Fragment,{children:j(e)}):k(e)}),t&&(0,a.jsx)(n.A,{className:"w-4 h-4 text-primary-600"})]})},C(e))})})]})},h=({trigger:e,actions:r=[],position:t="bottom-right",className:s="",...n})=>(0,a.jsx)(d,{trigger:e,position:t,className:s,...n,children:r.map((e,r)=>"separator"===e.type?(0,a.jsx)(u,{},r):"header"===e.type?(0,a.jsx)(g,{children:e.label},r):(0,a.jsx)(m,{onClick:e.onClick,disabled:e.disabled,icon:e.icon,shortcut:e.shortcut,children:e.label},r))})},9582:(e,r,t)=>{t.d(r,{Ay:()=>o,Wh:()=>i});var a=t(4848),s=t(4576),n=(t(6540),t(2392));const l=({children:e,variant:r="primary",size:t="md",rounded:l=!1,outline:i=!1,removable:o=!1,onRemove:c,icon:d,rightIcon:m,className:u="",...g})=>{const x={xs:l?"rounded-full":"rounded",sm:l?"rounded-full":"rounded",md:l?"rounded-full":"rounded-md",lg:l?"rounded-full":"rounded-lg"},h={primary:i?"text-primary-700 bg-primary-50 ring-1 ring-inset ring-primary-600/20":"text-primary-50 bg-primary-600",secondary:i?"text-gray-700 bg-gray-50 ring-1 ring-inset ring-gray-600/20":"text-gray-50 bg-gray-600",success:i?"text-green-700 bg-green-50 ring-1 ring-inset ring-green-600/20":"text-green-50 bg-green-600",warning:i?"text-yellow-700 bg-yellow-50 ring-1 ring-inset ring-yellow-600/20":"text-yellow-50 bg-yellow-600",danger:i?"text-red-700 bg-red-50 ring-1 ring-inset ring-red-600/20":"text-red-50 bg-red-600",info:i?"text-blue-700 bg-blue-50 ring-1 ring-inset ring-blue-600/20":"text-blue-50 bg-blue-600"},p={xs:"w-3 h-3",sm:"w-3 h-3",md:"w-4 h-4",lg:"w-5 h-5"};return(0,a.jsxs)("span",{className:(0,n.cn)(["inline-flex items-center font-medium","transition-colors duration-200"],{xs:"px-2 py-0.5 text-xs gap-1",sm:"px-2.5 py-0.5 text-xs gap-1",md:"px-3 py-1 text-sm gap-1.5",lg:"px-4 py-1.5 text-base gap-2"}[t],x[t],h[r],u),...g,children:[d&&(0,a.jsx)("span",{className:(0,n.cn)("flex-shrink-0",p[t]),children:d}),(0,a.jsx)("span",{className:"truncate",children:e}),m&&(0,a.jsx)("span",{className:(0,n.cn)("flex-shrink-0",p[t]),children:m}),o&&(0,a.jsx)("button",{type:"button",className:(0,n.cn)("flex-shrink-0 ml-1 rounded-full p-0.5","hover:bg-black hover:bg-opacity-10","focus:outline-none focus:bg-black focus:bg-opacity-10",p[t]),onClick:e=>{e.stopPropagation(),c?.()},"aria-label":"Șterge",children:(0,a.jsx)(s.A,{className:"w-full h-full"})})]})},i=({status:e,children:r,className:t="",...s})=>{const n={active:{variant:"success",children:r?.toString()||"Activ"},inactive:{variant:"secondary",children:r?.toString()||"Inactiv"},pending:{variant:"warning",children:r?.toString()||"În așteptare"},completed:{variant:"success",children:r?.toString()||"Finalizat"},cancelled:{variant:"danger",children:r?.toString()||"Anulat"},draft:{variant:"secondary",children:r?.toString()||"Ciornă"},published:{variant:"primary",children:r?.toString()||"Publicat"},archived:{variant:"secondary",children:r?.toString()||"Arhivat"}}[e]||{variant:"secondary",children:e};return(0,a.jsx)(l,{variant:n.variant,className:t,...s,children:n.children})},o=l}}]);