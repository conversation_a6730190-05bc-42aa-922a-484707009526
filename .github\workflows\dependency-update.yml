name: Dependency Updates

on:
  schedule:
    # Run every Monday at 9 AM UTC
    - cron: '0 9 * * 1'
  workflow_dispatch:

env:
  NODE_VERSION: '18'

jobs:
  # Job pentru actualizarea dependențelor
  update-dependencies:
    name: Update Dependencies
    runs-on: ubuntu-latest

    strategy:
      matrix:
        workspace: [backend, frontend]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: ${{ matrix.workspace }}/package-lock.json

      - name: Install npm-check-updates
        run: npm install -g npm-check-updates

      - name: Check for outdated packages
        working-directory: ./${{ matrix.workspace }}
        run: |
          echo "Checking for outdated packages in ${{ matrix.workspace }}..."
          ncu --format group --target minor > outdated-packages.txt
          cat outdated-packages.txt

      - name: Update minor and patch versions
        working-directory: ./${{ matrix.workspace }}
        run: |
          echo "Updating minor and patch versions..."
          ncu --target minor --upgrade
          npm install

      - name: Run tests after updates
        working-directory: ./${{ matrix.workspace }}
        run: |
          npm run lint
          npm run type-check
          npm run test
        continue-on-error: true

      - name: Check for major version updates
        working-directory: ./${{ matrix.workspace }}
        run: |
          echo "Checking for major version updates..."
          ncu --target major > major-updates.txt
          cat major-updates.txt

      - name: Create Pull Request for minor/patch updates
        uses: peter-evans/create-pull-request@v5
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: 'chore(${{ matrix.workspace }}): update dependencies'
          title: 'chore(${{ matrix.workspace }}): Update dependencies'
          body: |
            ## Dependency Updates for ${{ matrix.workspace }}

            This PR contains automatic dependency updates for minor and patch versions.

            ### Changes
            - Updated dependencies to latest minor/patch versions
            - All tests are passing ✅

            ### Major Updates Available
            The following major version updates are available but require manual review:
            ```
            $(cat ${{ matrix.workspace }}/major-updates.txt || echo "No major updates available")
            ```

            ### Testing
            - [x] Linting passed
            - [x] Type checking passed
            - [x] Unit tests passed

            ---
            *This PR was created automatically by the dependency update workflow.*
          branch: dependency-updates/${{ matrix.workspace }}
          delete-branch: true
          labels: |
            dependencies
            ${{ matrix.workspace }}
            automated

  # Job pentru security audit
  security-audit:
    name: Security Audit
    runs-on: ubuntu-latest
    needs: [update-dependencies]

    strategy:
      matrix:
        workspace: [backend, frontend]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: ${{ matrix.workspace }}/package-lock.json

      - name: Install dependencies
        working-directory: ./${{ matrix.workspace }}
        run: npm ci

      - name: Run npm audit
        working-directory: ./${{ matrix.workspace }}
        run: |
          echo "Running security audit for ${{ matrix.workspace }}..."
          npm audit --audit-level moderate --json > audit-results.json || true
          npm audit --audit-level moderate

      - name: Check for high/critical vulnerabilities
        working-directory: ./${{ matrix.workspace }}
        run: |
          HIGH_VULNS=$(cat audit-results.json | jq '.metadata.vulnerabilities.high // 0')
          CRITICAL_VULNS=$(cat audit-results.json | jq '.metadata.vulnerabilities.critical // 0')
          
          echo "High vulnerabilities: $HIGH_VULNS"
          echo "Critical vulnerabilities: $CRITICAL_VULNS"
          
          if [ "$HIGH_VULNS" -gt 0 ] || [ "$CRITICAL_VULNS" -gt 0 ]; then
            echo "::warning::Found $HIGH_VULNS high and $CRITICAL_VULNS critical vulnerabilities in ${{ matrix.workspace }}"
          fi

      - name: Upload audit results
        uses: actions/upload-artifact@v3
        with:
          name: security-audit-${{ matrix.workspace }}
          path: ${{ matrix.workspace }}/audit-results.json
          retention-days: 30

  # Job pentru verificarea licențelor
  license-check:
    name: License Compliance Check
    runs-on: ubuntu-latest

    strategy:
      matrix:
        workspace: [backend, frontend]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: ${{ matrix.workspace }}/package-lock.json

      - name: Install license checker
        run: npm install -g license-checker

      - name: Install dependencies
        working-directory: ./${{ matrix.workspace }}
        run: npm ci

      - name: Check licenses
        working-directory: ./${{ matrix.workspace }}
        run: |
          echo "Checking licenses for ${{ matrix.workspace }}..."
          license-checker --json > licenses.json
          
          # Check for problematic licenses
          PROBLEMATIC_LICENSES="GPL-2.0,GPL-3.0,AGPL-1.0,AGPL-3.0"
          
          if license-checker --excludePrivatePackages --onlyAllow "MIT;ISC;BSD;Apache-2.0;Unlicense;WTFPL" --summary; then
            echo "✅ All licenses are compliant"
          else
            echo "❌ Found potentially problematic licenses"
            license-checker --excludePrivatePackages --json | jq 'to_entries[] | select(.value.licenses | test("GPL|AGPL")) | {package: .key, license: .value.licenses}'
          fi

      - name: Upload license report
        uses: actions/upload-artifact@v3
        with:
          name: license-report-${{ matrix.workspace }}
          path: ${{ matrix.workspace }}/licenses.json
          retention-days: 30

  # Job pentru notificări
  notify-results:
    name: Notify Results
    runs-on: ubuntu-latest
    needs: [update-dependencies, security-audit, license-check]
    if: always()

    steps:
      - name: Download audit results
        uses: actions/download-artifact@v3
        with:
          pattern: security-audit-*
          merge-multiple: true

      - name: Analyze security results
        run: |
          TOTAL_HIGH=0
          TOTAL_CRITICAL=0
          
          for file in security-audit-*/audit-results.json; do
            if [ -f "$file" ]; then
              HIGH=$(cat "$file" | jq '.metadata.vulnerabilities.high // 0')
              CRITICAL=$(cat "$file" | jq '.metadata.vulnerabilities.critical // 0')
              TOTAL_HIGH=$((TOTAL_HIGH + HIGH))
              TOTAL_CRITICAL=$((TOTAL_CRITICAL + CRITICAL))
            fi
          done
          
          echo "TOTAL_HIGH=$TOTAL_HIGH" >> $GITHUB_ENV
          echo "TOTAL_CRITICAL=$TOTAL_CRITICAL" >> $GITHUB_ENV

      - name: Notify security issues
        if: env.TOTAL_HIGH > 0 || env.TOTAL_CRITICAL > 0
        uses: 8398a7/action-slack@v3
        with:
          status: custom
          custom_payload: |
            {
              "text": "🚨 Security vulnerabilities found in dependencies",
              "attachments": [
                {
                  "color": "danger",
                  "fields": [
                    {
                      "title": "High Vulnerabilities",
                      "value": "${{ env.TOTAL_HIGH }}",
                      "short": true
                    },
                    {
                      "title": "Critical Vulnerabilities", 
                      "value": "${{ env.TOTAL_CRITICAL }}",
                      "short": true
                    },
                    {
                      "title": "Action Required",
                      "value": "Please review and update vulnerable packages",
                      "short": false
                    }
                  ]
                }
              ]
            }
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}

      - name: Notify successful updates
        if: needs.update-dependencies.result == 'success' && (env.TOTAL_HIGH == 0 && env.TOTAL_CRITICAL == 0)
        uses: 8398a7/action-slack@v3
        with:
          status: success
          channel: '#development'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
          message: |
            ✅ Weekly dependency update completed successfully!
            - All dependencies updated to latest minor/patch versions
            - No security vulnerabilities found
            - All tests passing

  # Job pentru cleanup
  cleanup:
    name: Cleanup Old Branches
    runs-on: ubuntu-latest
    needs: [notify-results]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Delete old dependency update branches
        run: |
          echo "Cleaning up old dependency update branches..."
          
          # Delete merged dependency update branches older than 7 days
          git for-each-ref --format='%(refname:short) %(committerdate)' refs/remotes/origin/dependency-updates/ | \
          while read branch date; do
            if [[ $(date -d "$date" +%s) -lt $(date -d "7 days ago" +%s) ]]; then
              echo "Deleting old branch: $branch"
              git push origin --delete "${branch#origin/}" || true
            fi
          done
