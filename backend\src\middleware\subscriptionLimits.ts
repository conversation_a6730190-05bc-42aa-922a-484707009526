import { Request, Response, NextFunction } from 'express';
import { prisma } from '../config/prisma';

interface AuthenticatedRequest extends Request {
  user?: any;
}

// Check category limit based on subscription plan
export const checkCategoryLimit = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const { user } = req;

    if (!user) {
      res.status(401).json({
        success: false,
        message: 'Authentication required',
      });
      return;
    }

    // Get current category count for user
    const categoryCount = await prisma.category.count({
      where: {
        user_id: user.id,
        is_active: true,
      },
    });

    // Define limits based on plan type
    const limits = {
      free: 10,
      basic: 25,
      premium: 100,
      enterprise: 1000,
    };

    const userPlan = user.plan_type || 'free';
    const limit = limits[userPlan as keyof typeof limits] || limits.free;

    if (categoryCount >= limit) {
      res.status(403).json({
        success: false,
        message: `Category limit reached. Your ${userPlan} plan allows up to ${limit} categories.`,
        data: {
          currentCount: categoryCount,
          limit: limit,
          planType: userPlan,
        },
      });
      return;
    }

    next();
  } catch (error: any) {
    console.error('Category limit check error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while checking category limits',
    });
  }
};

// Check expense limit based on subscription plan
export const checkExpenseLimit = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    const { user } = req;

    if (!user) {
      res.status(401).json({
        success: false,
        message: 'Authentication required',
      });
      return;
    }

    // Get current month's expense count for user
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);

    const endOfMonth = new Date(startOfMonth);
    endOfMonth.setMonth(endOfMonth.getMonth() + 1);
    endOfMonth.setDate(0);
    endOfMonth.setHours(23, 59, 59, 999);

    const expenseCount = await prisma.expense.count({
      where: {
        user_id: user.id,
        date: {
          gte: startOfMonth,
          lte: endOfMonth,
        },
        // No deleted_at field in expense model
      },
    });

    // Define limits based on plan type
    const limits = {
      free: 50,
      basic: 200,
      premium: 1000,
      enterprise: 10000,
    };

    const userPlan = user.plan_type || 'free';
    const limit = limits[userPlan as keyof typeof limits] || limits.free;

    if (expenseCount >= limit) {
      res.status(403).json({
        success: false,
        message: `Monthly expense limit reached. Your ${userPlan} plan allows up to ${limit} expenses per month.`,
        data: {
          currentCount: expenseCount,
          limit: limit,
          planType: userPlan,
          period: 'monthly',
        },
      });
      return;
    }

    next();
  } catch (error: any) {
    console.error('Expense limit check error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while checking expense limits',
    });
  }
};

// Check export limit based on subscription plan
export const checkExportLimit = async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { user } = req;

    if (!user) {
      res.status(401).json({
        success: false,
        message: 'Authentication required',
      });
      return;
    }

    // For free users, limit exports
    const userPlan = user.plan_type || 'free';

    if (userPlan === 'free') {
      // Get current month's export count
      const startOfMonth = new Date();
      startOfMonth.setDate(1);
      startOfMonth.setHours(0, 0, 0, 0);

      const endOfMonth = new Date(startOfMonth);
      endOfMonth.setMonth(endOfMonth.getMonth() + 1);
      endOfMonth.setDate(0);
      endOfMonth.setHours(23, 59, 59, 999);

      // This would require an export tracking table in a real implementation
      // For now, we'll allow exports but could implement tracking later

      const exportLimit = 3; // Free users get 3 exports per month

      // TODO: Implement export tracking table and check against it
      // For now, just proceed
    }

    next();
  } catch (error: any) {
    console.error('Export limit check error:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error while checking export limits',
    });
  }
};

// Check export permission based on format and subscription plan
export const checkExportPermission = (format: string) => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { user } = req;

      if (!user) {
        res.status(401).json({
          success: false,
          message: 'Authentication required',
        });
        return;
      }

      const userPlan = user.plan_type || 'free';

      // Define format permissions by plan
      const permissions = {
        free: [],
        basic: ['csv'],
        premium: ['csv', 'pdf', 'excel'],
        enterprise: ['csv', 'pdf', 'excel'],
      };

      const allowedFormats = permissions[userPlan as keyof typeof permissions] || [];

      if (!allowedFormats.includes(format)) {
        res.status(403).json({
          success: false,
          message: `${format.toUpperCase()} export is not available for your ${userPlan} plan. Please upgrade to access this feature.`,
          data: {
            currentPlan: userPlan,
            requiredPlan: format === 'csv' ? 'basic' : 'premium',
            availableFormats: allowedFormats,
          },
        });
        return;
      }

      next();
    } catch (error: any) {
      console.error('Export permission check error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error while checking export permissions',
      });
    }
  };
};
