"use strict";(self.webpackChunkexpense_tracker_frontend=self.webpackChunkexpense_tracker_frontend||[]).push([[605],{5605:(e,s,a)=>{a.r(s),a.d(s,{default:()=>u});var t=a(8835),l=a(4175),i=a(2957),r=a(3930),n=a(6540),c=(a(888),a(6103)),m=a(125),d=a(9264),x=a(6821),o=a(2392),p=a(4848);const h={totalExpenses:1250.75,totalIncome:2500,savings:1249.25,monthlyData:[{month:"Ian",expenses:800,income:2500},{month:"Feb",expenses:950,income:2500},{month:"Mar",expenses:1100,income:2500},{month:"Apr",expenses:750,income:2500},{month:"Mai",expenses:1250,income:2500},{month:"Iun",expenses:900,income:2500}],categoryBreakdown:[{name:"Mâncare",amount:450.25,percentage:36},{name:"Transport",amount:200,percentage:16},{name:"U<PERSON><PERSON><PERSON><PERSON><PERSON>",amount:150,percentage:12},{name:"Divertisment",amount:100,percentage:8},{name:"Altele",amount:350.5,percentage:28}],topExpenses:[{id:1,description:"Cumpărături supermarket",amount:85.5,date:"2024-01-15",category:"Mâncare"},{id:2,description:"Combustibil",amount:75,date:"2024-01-14",category:"Transport"},{id:3,description:"Factură electricitate",amount:65,date:"2024-01-13",category:"Utilități"},{id:4,description:"Cinema",amount:45,date:"2024-01-12",category:"Divertisment"},{id:5,description:"Restaurant",amount:120,date:"2024-01-11",category:"Mâncare"}]},u=()=>{const[e,s]=(0,n.useState)("thisMonth"),[a,u]=(0,n.useState)("all"),j=(0,x.Rj)(),{data:g,isLoading:v,error:y}=(0,r.I)({queryKey:["reports",e,a],queryFn:async()=>(await new Promise(e=>setTimeout(e,800)),h)}),N=async s=>{try{const t={period:e,category:"all"!==a?a:void 0};await j.mutateAsync({format:s,params:t})}catch(e){console.error("Export failed:",e)}};return y?(0,p.jsx)("div",{className:"text-center py-12",children:(0,p.jsx)("p",{className:"text-red-600",children:"Eroare la încărcarea raportului"})}):(0,p.jsxs)("div",{className:"space-y-6",children:[(0,p.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between",children:[(0,p.jsxs)("div",{children:[(0,p.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Rapoarte"}),(0,p.jsx)("p",{className:"text-gray-600",children:"Analizează cheltuielile și tendințele financiare"})]}),(0,p.jsxs)("div",{className:"mt-4 sm:mt-0 flex space-x-3",children:[(0,p.jsxs)(c.Ay,{variant:"outline",onClick:()=>N("csv"),disabled:j.isPending,className:"flex items-center",children:[(0,p.jsx)(t.A,{className:"h-5 w-5 mr-2"}),j.isPending?"Se exportă...":"Export CSV"]}),(0,p.jsxs)(c.Ay,{variant:"outline",onClick:()=>N("pdf"),disabled:j.isPending,className:"flex items-center",children:[(0,p.jsx)(t.A,{className:"h-5 w-5 mr-2"}),j.isPending?"Se exportă...":"Export PDF"]}),(0,p.jsxs)(c.Ay,{variant:"outline",onClick:()=>N("excel"),disabled:j.isPending,className:"flex items-center",children:[(0,p.jsx)(t.A,{className:"h-5 w-5 mr-2"}),j.isPending?"Se exportă...":"Export Excel"]})]})]}),(0,p.jsx)(m.Ay,{className:"p-6",children:(0,p.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-4 sm:space-y-0",children:[(0,p.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,p.jsx)(l.A,{className:"h-5 w-5 text-gray-400"}),(0,p.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Filtrează:"})]}),(0,p.jsxs)("div",{className:"flex flex-col sm:flex-row sm:space-x-4 space-y-4 sm:space-y-0",children:[(0,p.jsxs)("div",{children:[(0,p.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Perioada"}),(0,p.jsx)("select",{value:e,onChange:e=>s(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500",children:[{value:"thisWeek",label:"Această săptămână"},{value:"thisMonth",label:"Această lună"},{value:"lastMonth",label:"Luna trecută"},{value:"thisYear",label:"Acest an"},{value:"custom",label:"Perioadă personalizată"}].map(e=>(0,p.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,p.jsxs)("div",{children:[(0,p.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Categoria"}),(0,p.jsx)("select",{value:a,onChange:e=>u(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500",children:[{value:"all",label:"Toate categoriile"},{value:"food",label:"Mâncare"},{value:"transport",label:"Transport"},{value:"utilities",label:"Utilități"},{value:"entertainment",label:"Divertisment"}].map(e=>(0,p.jsx)("option",{value:e.value,children:e.label},e.value))})]})]})]})}),v?(0,p.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,p.jsx)(d.Ay,{size:"lg"})}):g?(0,p.jsxs)(p.Fragment,{children:[(0,p.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,p.jsx)(m.Ay,{className:"p-6",children:(0,p.jsxs)("div",{className:"flex items-center",children:[(0,p.jsx)("div",{className:"p-2 bg-red-100 rounded-lg",children:(0,p.jsx)(i.A,{className:"h-6 w-6 text-red-600"})}),(0,p.jsxs)("div",{className:"ml-4",children:[(0,p.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total cheltuieli"}),(0,p.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(0,o.vv)(g.totalExpenses)})]})]})}),(0,p.jsx)(m.Ay,{className:"p-6",children:(0,p.jsxs)("div",{className:"flex items-center",children:[(0,p.jsx)("div",{className:"p-2 bg-green-100 rounded-lg",children:(0,p.jsx)(i.A,{className:"h-6 w-6 text-green-600"})}),(0,p.jsxs)("div",{className:"ml-4",children:[(0,p.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total venituri"}),(0,p.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(0,o.vv)(g.totalIncome)})]})]})}),(0,p.jsx)(m.Ay,{className:"p-6",children:(0,p.jsxs)("div",{className:"flex items-center",children:[(0,p.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:(0,p.jsx)(i.A,{className:"h-6 w-6 text-blue-600"})}),(0,p.jsxs)("div",{className:"ml-4",children:[(0,p.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Economii"}),(0,p.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:(0,o.vv)(g.savings)})]})]})})]}),(0,p.jsxs)(m.Ay,{className:"p-6",children:[(0,p.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Tendința lunară"}),(0,p.jsx)("div",{className:"space-y-4",children:g.monthlyData.map((e,s)=>(0,p.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,p.jsx)("div",{className:"w-12 text-sm font-medium text-gray-600",children:e.month}),(0,p.jsx)("div",{className:"flex-1",children:(0,p.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,p.jsx)("div",{className:"flex-1 bg-gray-200 rounded-full h-4 relative",children:(0,p.jsx)("div",{className:"bg-red-500 h-4 rounded-full",style:{width:e.expenses/e.income*100+"%"}})}),(0,p.jsx)("div",{className:"text-sm text-gray-600 w-20",children:(0,o.vv)(e.expenses)})]})})]},s))})]}),(0,p.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,p.jsxs)(m.Ay,{className:"p-6",children:[(0,p.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Cheltuieli pe categorii"}),(0,p.jsx)("div",{className:"space-y-4",children:g.categoryBreakdown.map((e,s)=>(0,p.jsxs)("div",{className:"flex items-center justify-between",children:[(0,p.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,p.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded-full"}),(0,p.jsx)("span",{className:"text-sm font-medium text-gray-900",children:e.name})]}),(0,p.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,p.jsxs)("span",{className:"text-sm text-gray-600",children:[e.percentage,"%"]}),(0,p.jsx)("span",{className:"text-sm font-medium text-gray-900",children:(0,o.vv)(e.amount)})]})]},s))})]}),(0,p.jsxs)(m.Ay,{className:"p-6",children:[(0,p.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Top cheltuieli"}),(0,p.jsx)("div",{className:"space-y-4",children:g.topExpenses.map(e=>(0,p.jsxs)("div",{className:"flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0",children:[(0,p.jsxs)("div",{children:[(0,p.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.description}),(0,p.jsxs)("p",{className:"text-xs text-gray-500",children:[e.category," • ",e.date]})]}),(0,p.jsx)("span",{className:"text-sm font-medium text-gray-900",children:(0,o.vv)(e.amount)})]},e.id))})]})]})]}):(0,p.jsx)(m.Ay,{className:"p-12 text-center",children:(0,p.jsx)("p",{className:"text-gray-500",children:"Nu sunt date disponibile pentru perioada selectată"})})]})}}}]);