import { PrismaClient } from '@prisma/client';
import logger from '../utils/logger';

const prisma = new PrismaClient();

interface UsageStats {
  current_period: {
    expenses: number;
    categories: number;
    exports: number;
  };
  limits: {
    expenses_per_month: number;
    categories: number;
    exports_per_month: number;
  };
  usage_percentage: {
    expenses: number;
    categories: number;
    exports: number;
  };
  plan_type: string;
}

interface PlanLimits {
  expenses_per_month: number;
  categories: number;
  exports_per_month: number;
}

const PLAN_LIMITS: Record<string, PlanLimits> = {
  free: {
    expenses_per_month: 50,
    categories: 5,
    exports_per_month: 3,
  },
  basic: {
    expenses_per_month: 500,
    categories: -1, // unlimited
    exports_per_month: 20,
  },
  premium: {
    expenses_per_month: -1, // unlimited
    categories: -1, // unlimited
    exports_per_month: -1, // unlimited
  },
};

class UsageService {
  /**
   * Obține statisticile de utilizare pentru un utilizator
   */
  async getUserUsageStats(userId: string): Promise<UsageStats> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: parseInt(userId) },
        select: {
          subscription_plan: true,
          subscription_status: true,
        },
      });

      if (!user) {
        throw new Error('User not found');
      }

      const planType = user.subscription_plan || 'free';
      const limits = PLAN_LIMITS[planType];

      // Calculează perioada curentă (luna curentă)
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

      // Obține utilizarea curentă
      const [expenseCount, categoryCount, exportCount] = await Promise.all([
        this.getExpenseCount(userId, startOfMonth, endOfMonth),
        this.getCategoryCount(userId),
        this.getExportCount(userId, startOfMonth, endOfMonth),
      ]);

      // Calculează procentajele de utilizare
      const usage_percentage = {
        expenses: limits.expenses_per_month === -1 ? 0 : Math.round((expenseCount / limits.expenses_per_month) * 100),
        categories: limits.categories === -1 ? 0 : Math.round((categoryCount / limits.categories) * 100),
        exports: limits.exports_per_month === -1 ? 0 : Math.round((exportCount / limits.exports_per_month) * 100),
      };

      return {
        current_period: {
          expenses: expenseCount,
          categories: categoryCount,
          exports: exportCount,
        },
        limits: {
          expenses_per_month: limits.expenses_per_month,
          categories: limits.categories,
          exports_per_month: limits.exports_per_month,
        },
        usage_percentage,
        plan_type: planType,
      };
    } catch (error) {
      logger.error('Error getting user usage stats:', error);
      throw error;
    }
  }

  /**
   * Incrementează utilizarea pentru o acțiune specifică
   */
  async incrementUsage(userId: string, action: string): Promise<void> {
    try {
      // Înregistrează acțiunea în usage_logs
      await prisma.usageLog.create({
        data: {
          user_id: parseInt(userId),
          action,
          resource: this.getResourceFromAction(action),
          created_at: new Date(),
        },
      });

      logger.info(`Usage incremented for user ${userId}, action: ${action}`);
    } catch (error) {
      logger.error('Error incrementing usage:', error);
      throw error;
    }
  }

  /**
   * Helper method pentru a obține resursa din acțiune
   */
  private getResourceFromAction(action: string): string {
    switch (action) {
      case 'create_expense':
      case 'update_expense':
      case 'delete_expense':
        return 'expense';
      case 'create_category':
      case 'update_category':
      case 'delete_category':
        return 'category';
      case 'export_data':
        return 'export';
      default:
        return 'unknown';
    }
  }

  /**
   * Obține numărul de cheltuieli pentru o perioadă
   */
  private async getExpenseCount(userId: string, startDate: Date, endDate: Date): Promise<number> {
    return await prisma.expense.count({
      where: {
        user_id: parseInt(userId),
        created_at: {
          gte: startDate,
          lte: endDate,
        },
      },
    });
  }

  /**
   * Obține numărul de categorii personalizate
   */
  private async getCategoryCount(userId: string): Promise<number> {
    return await prisma.category.count({
      where: {
        user_id: parseInt(userId),
        is_default: false,
      },
    });
  }

  /**
   * Obține numărul de export-uri pentru o perioadă
   */
  private async getExportCount(userId: string, startDate: Date, endDate: Date): Promise<number> {
    return await prisma.usageLog.count({
      where: {
        user_id: parseInt(userId),
        action: 'export_data',
        created_at: {
          gte: startDate,
          lte: endDate,
        },
      },
    });
  }

  /**
   * Verifică dacă utilizatorul poate efectua o acțiune
   */
  async canPerformAction(userId: string, action: string): Promise<boolean> {
    try {
      const stats = await this.getUserUsageStats(userId);

      switch (action) {
        case 'create_expense':
          if (stats.limits.expenses_per_month === -1) return true;
          return stats.current_period.expenses < stats.limits.expenses_per_month;

        case 'create_category':
          if (stats.limits.categories === -1) return true;
          return stats.current_period.categories < stats.limits.categories;

        case 'export_data':
          if (stats.limits.exports_per_month === -1) return true;
          return stats.current_period.exports < stats.limits.exports_per_month;

        default:
          return true;
      }
    } catch (error) {
      logger.error('Error checking if user can perform action:', error);
      return false;
    }
  }

  /**
   * Resetează utilizarea pentru o perioadă
   */
  async resetUsageForPeriod(userId: string, period: string): Promise<void> {
    try {
      const startDate = new Date();
      const endDate = new Date();

      if (period === 'month') {
        startDate.setDate(1);
        startDate.setHours(0, 0, 0, 0);
        endDate.setMonth(endDate.getMonth() + 1);
        endDate.setDate(0);
        endDate.setHours(23, 59, 59, 999);
      }

      // Șterge log-urile de utilizare pentru perioada specificată
      await prisma.usageLog.deleteMany({
        where: {
          user_id: parseInt(userId),
          created_at: {
            gte: startDate,
            lte: endDate,
          },
        },
      });

      logger.info(`Usage reset for user ${userId}, period: ${period}`);
    } catch (error) {
      logger.error('Error resetting usage:', error);
      throw error;
    }
  }

  /**
   * Obține statistici globale de utilizare
   */
  async getGlobalUsageStats(startDate?: string, endDate?: string): Promise<any> {
    try {
      const start = startDate ? new Date(startDate) : new Date(new Date().getFullYear(), new Date().getMonth(), 1);
      const end = endDate ? new Date(endDate) : new Date();

      const [totalUsers, activeUsers, totalExpenses, totalCategories, totalExports] = await Promise.all([
        prisma.user.count(),
        prisma.user.count({
          where: {
            last_login: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
            },
          },
        }),
        prisma.expense.count({
          where: {
            created_at: {
              gte: start,
              lte: end,
            },
          },
        }),
        prisma.category.count({
          where: {
            created_at: {
              gte: start,
              lte: end,
            },
          },
        }),
        prisma.usageLog.count({
          where: {
            action: 'export_data',
            created_at: {
              gte: start,
              lte: end,
            },
          },
        }),
      ]);

      return {
        total_users: totalUsers,
        active_users: activeUsers,
        total_expenses: totalExpenses,
        total_categories: totalCategories,
        total_exports: totalExports,
        period: {
          start: start.toISOString(),
          end: end.toISOString(),
        },
      };
    } catch (error) {
      logger.error('Error getting global usage stats:', error);
      throw error;
    }
  }

  /**
   * Obține progresul utilizării pentru un utilizator
   */
  async getUsageProgress(userId: string): Promise<any> {
    try {
      const stats = await this.getUserUsageStats(userId);

      return {
        expenses: {
          current: stats.current_period.expenses,
          limit: stats.limits.expenses_per_month,
          percentage: stats.usage_percentage.expenses,
          unlimited: stats.limits.expenses_per_month === -1,
        },
        categories: {
          current: stats.current_period.categories,
          limit: stats.limits.categories,
          percentage: stats.usage_percentage.categories,
          unlimited: stats.limits.categories === -1,
        },
        exports: {
          current: stats.current_period.exports,
          limit: stats.limits.exports_per_month,
          percentage: stats.usage_percentage.exports,
          unlimited: stats.limits.exports_per_month === -1,
        },
        plan_type: stats.plan_type,
      };
    } catch (error) {
      logger.error('Error getting usage progress:', error);
      throw error;
    }
  }
}

const usageService = new UsageService();
export default usageService;
