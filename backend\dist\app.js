"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
console.log('🚀 Starting application...');
const express_1 = __importDefault(require("express"));
const compression_1 = __importDefault(require("compression"));
const morgan_1 = __importDefault(require("morgan"));
const dotenv_1 = require("dotenv");
console.log('📦 Imports loaded successfully');
(0, dotenv_1.config)();
console.log('🔧 Environment variables loaded');
console.log('🔧 Starting middleware imports...');
const security_1 = require("./middleware/security");
const attackDetection_1 = require("./middleware/attackDetection");
const headerSecurity_1 = require("./middleware/headerSecurity");
const botDetection_1 = require("./middleware/botDetection");
const audit_1 = require("./middleware/audit");
const errorHandler_1 = __importDefault(require("./middleware/errorHandler"));
const notFound_1 = __importDefault(require("./middleware/notFound"));
const cache_1 = require("./middleware/cache");
console.log('🛡️ Middleware imported successfully');
console.log('🛣️ Starting routes import...');
const auth_1 = __importDefault(require("./routes/auth"));
console.log('✅ Auth routes imported');
const users_1 = __importDefault(require("./routes/users"));
console.log('✅ User routes imported');
const categories_1 = __importDefault(require("./routes/categories"));
console.log('✅ Category routes imported');
const expenses_1 = __importDefault(require("./routes/expenses"));
console.log('✅ Expense routes imported');
const export_1 = __importDefault(require("./routes/export"));
console.log('✅ Export routes imported');
const subscription_1 = __importDefault(require("./routes/subscription"));
console.log('✅ Subscription routes imported');
const admin_1 = __importDefault(require("./routes/admin"));
console.log('🛣️ All routes imported successfully');
const prisma_1 = require("./config/prisma");
const logger_1 = __importDefault(require("./utils/logger"));
console.log('🗄️ Database utilities imported');
const app = (0, express_1.default)();
const PORT = parseInt(process.env.PORT || '3001', 10);
(0, security_1.setupSecurity)(app);
app.use(attackDetection_1.attackDetection);
app.use(headerSecurity_1.headerSecurity);
app.use(botDetection_1.botDetection);
app.use((0, compression_1.default)());
app.use(audit_1.auditLogger);
if (process.env.NODE_ENV !== 'test') {
    app.use((0, morgan_1.default)('combined'));
}
app.use('/api/webhooks/stripe', express_1.default.raw({ type: 'application/json' }));
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
app.get('/health', (req, res) => {
    res.status(200).json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || 'development',
        version: process.env.npm_package_version || '1.0.0',
    });
});
app.use('/api/auth', auth_1.default);
app.use('/api/users', cache_1.userCache, cache_1.invalidateUserCache, users_1.default);
app.use('/api/expenses', cache_1.expenseCache, cache_1.invalidateExpenseCache, expenses_1.default);
app.use('/api/categories', cache_1.categoryCache, cache_1.invalidateCategoryCache, categories_1.default);
app.use('/api/export', cache_1.reportCache, export_1.default);
app.use('/api/subscriptions', subscription_1.default);
app.use('/api/admin', audit_1.adminAuditLogger, admin_1.default);
app.get('/api', (req, res) => {
    res.json({
        message: 'Expense Tracker API',
        version: '1.0.0',
        endpoints: {
            auth: {
                'POST /api/auth/register': 'Register new user',
                'POST /api/auth/login': 'Login user',
                'POST /api/auth/refresh': 'Refresh access token',
                'POST /api/auth/logout': 'Logout user',
                'GET /api/auth/profile': 'Get user profile',
            },
            users: {
                'GET /api/users/profile': 'Get current user profile',
                'PUT /api/users/profile': 'Update user profile',
                'DELETE /api/users/account': 'Delete user account',
            },
            expenses: {
                'GET /api/expenses': 'Get user expenses',
                'POST /api/expenses': 'Create new expense',
                'GET /api/expenses/:id': 'Get expense by ID',
                'PUT /api/expenses/:id': 'Update expense',
                'DELETE /api/expenses/:id': 'Delete expense',
                'GET /api/expenses/stats/monthly': 'Get monthly statistics',
                'GET /api/expenses/stats/category': 'Get category statistics',
            },
            categories: {
                'GET /api/categories': 'Get user categories',
                'POST /api/categories': 'Create new category',
                'PUT /api/categories/:id': 'Update category',
                'DELETE /api/categories/:id': 'Delete category',
            },
            subscriptions: {
                'GET /api/subscriptions/plans': 'Get available plans',
                'GET /api/subscriptions/current': 'Get current subscription',
                'POST /api/subscriptions/checkout': 'Create checkout session',
                'POST /api/subscriptions/portal': 'Create customer portal',
                'POST /api/subscriptions/cancel': 'Cancel subscription',
                'POST /api/subscriptions/reactivate': 'Reactivate subscription',
                'GET /api/subscriptions/usage': 'Get usage statistics',
                'POST /api/webhooks/stripe': 'Stripe webhook endpoint',
            },
            admin: {
                'GET /api/admin/dashboard/stats': 'Get admin dashboard statistics',
                'GET /api/admin/alerts': 'Get system alerts',
                'POST /api/admin/alerts/:alertId/read': 'Mark alert as read',
                'GET /api/admin/users': 'Get users list with pagination',
                'GET /api/admin/users/:userId': 'Get user details',
            },
        },
        documentation: 'https://github.com/your-repo/expense-tracker/wiki/API-Documentation',
    });
});
app.use(notFound_1.default);
app.use(errorHandler_1.default);
const startServer = async () => {
    try {
        await (0, prisma_1.initializeDatabase)();
        console.log('✅ Database connection established successfully.');
        if (process.env.NODE_ENV !== 'test') {
            app.listen(PORT, '0.0.0.0', () => {
                console.log(`🚀 Server running on port ${PORT}`);
                console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
                console.log(`🔗 API URL: http://0.0.0.0:${PORT}/api`);
                console.log(`❤️  Health check: http://0.0.0.0:${PORT}/health`);
            });
        }
    }
    catch (error) {
        console.error('❌ Unable to start server:', error);
        process.exit(1);
    }
};
process.on('unhandledRejection', (err) => {
    logger_1.default.error('❌ Unhandled Promise Rejection:', {
        error: err.message,
        stack: err.stack,
        timestamp: new Date().toISOString()
    });
    console.error('❌ Unhandled Promise Rejection:', err);
    process.exit(1);
});
process.on('uncaughtException', (err) => {
    logger_1.default.error('❌ Uncaught Exception:', {
        error: err.message,
        stack: err.stack,
        timestamp: new Date().toISOString()
    });
    console.error('❌ Uncaught Exception:', err);
    process.exit(1);
});
process.on('SIGTERM', async () => {
    console.log('🔄 SIGTERM received, shutting down gracefully...');
    try {
        const { closeCache } = require('./middleware/cache');
        await closeCache();
        logger_1.default.info('Cache connections closed successfully');
    }
    catch (error) {
        logger_1.default.error('Error closing cache connections:', error);
    }
    await (0, prisma_1.closeConnection)();
    logger_1.default.end();
    process.exit(0);
});
process.on('SIGINT', async () => {
    console.log('🔄 SIGINT received, shutting down gracefully...');
    try {
        const { closeCache } = require('./middleware/cache');
        await closeCache();
        logger_1.default.info('Cache connections closed successfully');
    }
    catch (error) {
        logger_1.default.error('Error closing cache connections:', error);
    }
    await (0, prisma_1.closeConnection)();
    logger_1.default.end();
    process.exit(0);
});
if (require.main === module) {
    startServer();
}
exports.default = app;
//# sourceMappingURL=app.js.map