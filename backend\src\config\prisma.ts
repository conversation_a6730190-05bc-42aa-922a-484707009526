import { PrismaClient } from '@prisma/client';

// Create a single instance of PrismaClient
const prisma = new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
  errorFormat: 'pretty',
});

// Handle graceful shutdown
process.on('beforeExit', async () => {
  await prisma.$disconnect();
});

process.on('SIGINT', async () => {
  await prisma.$disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  await prisma.$disconnect();
  process.exit(0);
});

// Test database connection
const testConnection = async (): Promise<boolean> => {
  try {
    await prisma.$connect();
    console.log('✅ Database connected successfully');
    return true;
  } catch (error: any) {
    console.error('❌ Database connection failed:', error.message);
    return false;
  }
};

// Initialize database (equivalent to Sequelize sync)
const initializeDatabase = async (): Promise<boolean> => {
  try {
    await testConnection();
    console.log('📊 Database initialized successfully');
    return true;
  } catch (error: any) {
    console.error('❌ Database initialization failed:', error.message);
    throw error;
  }
};

// Close database connection
const closeConnection = async (): Promise<void> => {
  try {
    await prisma.$disconnect();
    console.log('🔌 Database connection closed');
  } catch (error: any) {
    console.error('❌ Error closing database connection:', error.message);
  }
};

export {
  prisma,
  testConnection,
  initializeDatabase,
  closeConnection,
};