"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.attackDetection = void 0;
const logger_1 = __importDefault(require("../utils/logger"));
const attackDetection = (req, res, next) => {
    const suspiciousPatterns = [
        /('|(\-\-)|(;)|(\||\|)|(\*|\*))/i,
        /(<script[^>]*>.*?<\/script>)|(<iframe[^>]*>.*?<\/iframe>)|(<object[^>]*>.*?<\/object>)/i,
        /(\.\.[\/\\])|(\.\.\.)/,
        /(;\s*(rm|del|format|shutdown|reboot))/i
    ];
    const checkForAttacks = (data, source) => {
        if (typeof data === 'string') {
            for (const pattern of suspiciousPatterns) {
                if (pattern.test(data)) {
                    logger_1.default.error('Potential attack detected', {
                        ip: req.ip,
                        url: req.originalUrl,
                        method: req.method,
                        userAgent: req.get('User-Agent'),
                        userId: req.user?.id,
                        source,
                        pattern: pattern.toString(),
                        data: data.substring(0, 100)
                    });
                    return true;
                }
            }
        }
        else if (typeof data === 'object' && data !== null) {
            for (const key in data) {
                if (checkForAttacks(data[key], `${source}.${key}`)) {
                    return true;
                }
            }
        }
        return false;
    };
    checkForAttacks(req.query, 'query');
    checkForAttacks(req.body, 'body');
    checkForAttacks(req.params, 'params');
    next();
};
exports.attackDetection = attackDetection;
exports.default = exports.attackDetection;
//# sourceMappingURL=attackDetection.js.map