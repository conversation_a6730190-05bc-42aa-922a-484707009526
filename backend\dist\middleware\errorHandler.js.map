{"version": 3, "file": "errorHandler.js", "sourceRoot": "", "sources": ["../../src/middleware/errorHandler.ts"], "names": [], "mappings": ";;;;;AACA,6DAAqC;AAuBrC,MAAM,YAAY,GAAG,CACnB,GAAgB,EAChB,GAAyB,EACzB,GAAa,EACb,IAAkB,EACZ,EAAE;IACR,IAAI,KAAK,GAA4C;QACnD,OAAO,EAAE,GAAG,CAAC,OAAO;QACpB,UAAU,EAAE,GAAG,CAAC,UAAU,IAAI,GAAG;KAClC,CAAC;IAGF,gBAAM,CAAC,KAAK,CAAC;QACX,KAAK,EAAE;YACL,OAAO,EAAE,GAAG,CAAC,OAAO;YACpB,KAAK,EAAE,GAAG,CAAC,KAAK;YAChB,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,IAAI,EAAE,GAAG,CAAC,IAAI;SACf;QACD,OAAO,EAAE;YACP,MAAM,EAAE,GAAG,CAAC,MAAM;YAClB,GAAG,EAAE,GAAG,CAAC,WAAW;YACpB,EAAE,EAAE,GAAG,CAAC,EAAE;YACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;YAChC,MAAM,EAAE,GAAG,CAAC,IAAI,EAAE,EAAE;SACrB;KACF,CAAC,CAAC;IAGH,IAAI,GAAG,CAAC,IAAI,IAAI,OAAO,GAAG,CAAC,IAAI,KAAK,QAAQ,IAAI,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;QACzE,QAAQ,GAAG,CAAC,IAAI,EAAE,CAAC;YACjB,KAAK,OAAO;gBACV,KAAK,GAAG,EAAE,OAAO,EAAE,wCAAwC,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;gBAC/E,MAAM;YACR,KAAK,OAAO;gBACV,KAAK,GAAG,EAAE,OAAO,EAAE,mDAAmD,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;gBAC1F,MAAM;YACR,KAAK,OAAO;gBACV,KAAK,GAAG,EAAE,OAAO,EAAE,4CAA4C,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;gBACnF,MAAM;YACR,KAAK,OAAO;gBACV,KAAK,GAAG,EAAE,OAAO,EAAE,gCAAgC,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;gBACvE,MAAM;YACR,KAAK,OAAO;gBACV,KAAK,GAAG,EAAE,OAAO,EAAE,qCAAqC,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;gBAC5E,MAAM;YACR;gBACE,KAAK,GAAG,EAAE,OAAO,EAAE,wBAAwB,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;QACnE,CAAC;IACH,CAAC;IAGD,IAAI,GAAG,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;QAC7B,MAAM,OAAO,GAAG,oBAAoB,CAAC;QACrC,KAAK,GAAG,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;IACvC,CAAC;IAGD,IAAI,GAAG,CAAC,IAAI,KAAK,KAAK,EAAE,CAAC;QACvB,MAAM,OAAO,GAAG,+BAA+B,CAAC;QAChD,KAAK,GAAG,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;IACvC,CAAC;IAGD,IAAI,GAAG,CAAC,IAAI,KAAK,iBAAiB,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;QACjD,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAQ,EAAE,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACpF,KAAK,GAAG,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;IACvC,CAAC;IAGD,IAAI,GAAG,CAAC,IAAI,KAAK,0BAA0B,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;QAC1D,MAAM,OAAO,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjE,KAAK,GAAG,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;IACvC,CAAC;IAGD,IAAI,GAAG,CAAC,IAAI,KAAK,gCAAgC,EAAE,CAAC;QAClD,MAAM,OAAO,GAAG,+BAA+B,CAAC;QAChD,KAAK,GAAG,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;IACvC,CAAC;IAGD,IAAI,GAAG,CAAC,IAAI,KAAK,oCAAoC,EAAE,CAAC;QACtD,MAAM,OAAO,GAAG,uCAAuC,CAAC;QACxD,KAAK,GAAG,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;IACvC,CAAC;IAGD,IAAI,GAAG,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;QACrC,MAAM,OAAO,GAAG,sDAAsD,CAAC;QACvE,KAAK,GAAG,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;IACvC,CAAC;IAED,IAAI,GAAG,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;QACrC,MAAM,OAAO,GAAG,sDAAsD,CAAC;QACvE,KAAK,GAAG,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;IACvC,CAAC;IAGD,IAAI,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC9C,IAAI,OAAO,GAAG,8BAA8B,CAAC;QAC7C,IAAI,UAAU,GAAG,GAAG,CAAC;QAErB,QAAQ,GAAG,CAAC,IAAI,EAAE,CAAC;YACjB,KAAK,iBAAiB;gBACpB,OAAO,GAAG,gBAAgB,GAAG,CAAC,OAAO,EAAE,CAAC;gBACxC,MAAM;YACR,KAAK,sBAAsB;gBACzB,OAAO,GAAG,+DAA+D,CAAC;gBAC1E,UAAU,GAAG,GAAG,CAAC;gBACjB,MAAM;YACR,KAAK,2BAA2B;gBAC9B,OAAO,GAAG,8BAA8B,CAAC;gBACzC,MAAM;YACR,KAAK,gBAAgB;gBACnB,OAAO,GAAG,uBAAuB,CAAC;gBAClC,UAAU,GAAG,GAAG,CAAC;gBACjB,MAAM;YACR,KAAK,uBAAuB;gBAC1B,OAAO,GAAG,+BAA+B,CAAC;gBAC1C,UAAU,GAAG,GAAG,CAAC;gBACjB,MAAM;YACR,KAAK,2BAA2B;gBAC9B,OAAO,GAAG,gCAAgC,CAAC;gBAC3C,UAAU,GAAG,GAAG,CAAC;gBACjB,MAAM;YACR;gBACE,OAAO,GAAG,8BAA8B,CAAC;QAC7C,CAAC;QAED,KAAK,GAAG,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;IAClC,CAAC;IAGD,IAAI,GAAG,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;QACnC,MAAM,OAAO,GAAG,gBAAgB,CAAC;QACjC,KAAK,GAAG,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;IACvC,CAAC;IAED,IAAI,GAAG,CAAC,IAAI,KAAK,uBAAuB,EAAE,CAAC;QACzC,MAAM,OAAO,GAAG,uBAAuB,CAAC;QACxC,KAAK,GAAG,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;IACvC,CAAC;IAGD,IAAI,GAAG,CAAC,MAAM,KAAK,GAAG,EAAE,CAAC;QACvB,MAAM,OAAO,GAAG,2CAA2C,CAAC;QAC5D,KAAK,GAAG,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;IACvC,CAAC;IAGD,MAAM,UAAU,GAAG,KAAK,CAAC,UAAU,IAAI,GAAG,CAAC,UAAU,IAAI,GAAG,CAAC;IAC7D,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,uBAAuB,CAAC;IAGzD,MAAM,QAAQ,GAAQ;QACpB,OAAO,EAAE,KAAK;QACd,KAAK,EAAE,OAAO;QACd,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa,IAAI,EAAE,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,CAAC;KACpE,CAAC;IAEF,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACxC,CAAC,CAAC;AAEF,kBAAe,YAAY,CAAC"}