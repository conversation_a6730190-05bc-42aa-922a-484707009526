"use strict";(self.webpackChunkexpense_tracker_frontend=self.webpackChunkexpense_tracker_frontend||[]).push([[700],{3573:(e,r,t)=>{t.d(r,{o:()=>o});var s=t(888),a=t(2389),n=t(8114);const o=()=>{const{t:e,i18n:r}=(0,a.Bd)(),t=(0,n.UK)(),o=(0,n.qX)(),l=r=>e(`languages.${r}`,{defaultValue:r.toUpperCase()});return{t:e,i18n:r,setLanguage:async e=>{try{await(0,n.v2)(e);const r="ro"===e?"Limba a fost schimbată cu succes!":"Language changed successfully!";s.oR.success(r)}catch(e){console.error("Error changing language:",e);const r="ro"===(0,n.UK)()?"Eroare la schimbarea limbii":"Error changing language";s.oR.error(r)}},currentLanguage:t,supportedLanguages:o,isLanguageSupported:e=>o.includes(e),getLanguageDisplayName:l,getLanguagesWithNames:()=>o.map(e=>({code:e,name:l(e)})),isRTL:()=>["ar","he","fa"].includes(t)}}},6103:(e,r,t)=>{t.d(r,{Ay:()=>o});var s=t(4848),a=(t(6540),t(2392)),n=t(9264);const o=({children:e,variant:r="primary",size:t="md",loading:o=!1,disabled:l=!1,fullWidth:i=!1,leftIcon:c=null,rightIcon:d=null,className:g="",onClick:h,type:m="button",...x})=>{const u={primary:["bg-primary-600 text-white border-primary-600","hover:bg-primary-700 hover:border-primary-700","focus:ring-primary-500","disabled:bg-primary-300 disabled:border-primary-300"].join(" "),secondary:["bg-gray-600 text-white border-gray-600","hover:bg-gray-700 hover:border-gray-700","focus:ring-gray-500","disabled:bg-gray-300 disabled:border-gray-300"].join(" "),outline:["bg-transparent text-primary-600 border-primary-600","hover:bg-primary-50 hover:text-primary-700","focus:ring-primary-500","disabled:text-primary-300 disabled:border-primary-300"].join(" "),ghost:["bg-transparent text-gray-700 border-transparent","hover:bg-gray-100 hover:text-gray-900","focus:ring-gray-500","disabled:text-gray-400"].join(" "),danger:["bg-red-600 text-white border-red-600","hover:bg-red-700 hover:border-red-700","focus:ring-red-500","disabled:bg-red-300 disabled:border-red-300"].join(" "),success:["bg-green-600 text-white border-green-600","hover:bg-green-700 hover:border-green-700","focus:ring-green-500","disabled:bg-green-300 disabled:border-green-300"].join(" "),warning:["bg-yellow-600 text-white border-yellow-600","hover:bg-yellow-700 hover:border-yellow-700","focus:ring-yellow-500","disabled:bg-yellow-300 disabled:border-yellow-300"].join(" "),white:["bg-white text-gray-900 border-white","hover:bg-gray-50 hover:text-gray-900","focus:ring-gray-500","disabled:bg-gray-100 disabled:text-gray-400"].join(" ")},p={xs:"h-3 w-3",sm:"h-4 w-4",md:"h-4 w-4",lg:"h-5 w-5",xl:"h-6 w-6"},b=l||o;return(0,s.jsxs)("button",{type:m,onClick:e=>{b?e.preventDefault():h?.(e)},disabled:b,className:(0,a.cn)("inline-flex items-center justify-center","border font-medium rounded-lg","transition-all duration-200 ease-in-out","focus:outline-none focus:ring-2 focus:ring-offset-2","disabled:cursor-not-allowed disabled:opacity-60",u[r],{xs:"px-2 py-1 text-xs",sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-sm",lg:"px-6 py-3 text-base",xl:"px-8 py-4 text-lg"}[t],i&&"w-full",g),...x,children:[c&&!o&&(0,s.jsx)("span",{className:(0,a.cn)(p[t],e&&"mr-2"),children:c}),o&&(0,s.jsx)("span",{className:(0,a.cn)(e&&"mr-2"),children:(0,s.jsx)(n.Ay,{size:"xs"===t||"sm"===t?"sm":"md",color:"currentColor"})}),e&&(0,s.jsx)("span",{className:o?"opacity-70":"",children:e}),d&&!o&&(0,s.jsx)("span",{className:(0,a.cn)(p[t],e&&"ml-2"),children:d})]})}},8700:(e,r,t)=>{t.d(r,{A:()=>d});var s=t(4266),a=(t(6540),t(2389)),n=t(4976),o=t(3573),l=t(6103),i=t(8724),c=t(4848);const d=({children:e})=>{const{t:r}=(0,a.Bd)(),{currentLanguage:t,setLanguage:d}=(0,o.o)(),g=[{value:"ro",label:"Română"},{value:"en",label:"English"}];return(0,c.jsxs)("div",{className:"min-h-screen bg-white",children:[(0,c.jsx)("header",{className:"bg-white shadow-sm sticky top-0 z-50",children:(0,c.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,c.jsxs)("div",{className:"flex justify-between items-center py-4",children:[(0,c.jsxs)(n.N_,{to:"/",className:"flex items-center space-x-2",children:[(0,c.jsx)("div",{className:"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center",children:(0,c.jsx)(s.A,{className:"w-5 h-5 text-white"})}),(0,c.jsx)("span",{className:"text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"FinanceFlow"})]}),(0,c.jsxs)("nav",{className:"hidden md:flex space-x-8",children:[(0,c.jsx)(n.N_,{to:"/#features",className:"text-gray-600 hover:text-blue-600 transition-colors",children:r("landing.header.features")}),(0,c.jsx)(n.N_,{to:"/#testimonials",className:"text-gray-600 hover:text-blue-600 transition-colors",children:r("landing.header.testimonials")}),(0,c.jsx)(n.N_,{to:"/#pricing",className:"text-gray-600 hover:text-blue-600 transition-colors",children:r("landing.header.pricing")})]}),(0,c.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,c.jsx)(i.l6,{value:g.find(e=>e.value===t),onChange:e=>d(e.value),options:g,className:"w-32",buttonClassName:"bg-white hover:bg-gray-50 border-gray-300 hover:border-blue-400 rounded-t-lg px-4 py-2.5 text-sm font-medium text-gray-800 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 shadow-sm hover:shadow-md",optionClassName:"hover:bg-blue-50 font-medium",menuClassName:"border-t-0 rounded-t-none rounded-b-lg border-gray-300 shadow-lg",offset:0}),(0,c.jsx)(n.N_,{to:"/login",className:"text-gray-600 hover:text-blue-600 transition-colors",children:r("landing.header.login")}),(0,c.jsx)(n.N_,{to:"/register",children:(0,c.jsx)(l.Ay,{variant:"primary",size:"sm",children:r("landing.header.register")})})]})]})})}),(0,c.jsx)("main",{className:"flex-1",children:e}),(0,c.jsx)("footer",{className:"bg-gray-900 text-white py-12",children:(0,c.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,c.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,c.jsxs)("div",{children:[(0,c.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,c.jsx)("div",{className:"w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center",children:(0,c.jsx)(s.A,{className:"w-5 h-5 text-white"})}),(0,c.jsx)("span",{className:"text-xl font-bold",children:"FinanceFlow"})]}),(0,c.jsx)("p",{className:"text-gray-400",children:r("landing.footer.description")})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("h3",{className:"font-semibold mb-4",children:r("landing.footer.product.title")}),(0,c.jsxs)("ul",{className:"space-y-2 text-gray-400",children:[(0,c.jsx)("li",{children:(0,c.jsx)(n.N_,{to:"/features",className:"hover:text-white transition-colors",children:r("landing.footer.product.features")})}),(0,c.jsx)("li",{children:(0,c.jsx)(n.N_,{to:"/pricing",className:"hover:text-white transition-colors",children:r("landing.footer.product.pricing")})}),(0,c.jsx)("li",{children:(0,c.jsx)("a",{href:"#",className:"hover:text-white transition-colors",children:r("landing.footer.product.api")})}),(0,c.jsx)("li",{children:(0,c.jsx)("a",{href:"#",className:"hover:text-white transition-colors",children:r("landing.footer.product.integrations")})})]})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("h3",{className:"font-semibold mb-4",children:r("landing.footer.support.title")}),(0,c.jsxs)("ul",{className:"space-y-2 text-gray-400",children:[(0,c.jsx)("li",{children:(0,c.jsx)(n.N_,{to:"/documentation",className:"hover:text-white transition-colors",children:r("landing.footer.support.documentation")})}),(0,c.jsx)("li",{children:(0,c.jsx)(n.N_,{to:"/help",className:"hover:text-white transition-colors",children:r("landing.footer.support.guides")})}),(0,c.jsx)("li",{children:(0,c.jsx)(n.N_,{to:"/contact",className:"hover:text-white transition-colors",children:r("landing.footer.support.contact")})}),(0,c.jsx)("li",{children:(0,c.jsx)("a",{href:"#",className:"hover:text-white transition-colors",children:r("landing.footer.support.status")})})]})]}),(0,c.jsxs)("div",{children:[(0,c.jsx)("h3",{className:"font-semibold mb-4",children:r("landing.footer.legal.title")}),(0,c.jsxs)("ul",{className:"space-y-2 text-gray-400",children:[(0,c.jsx)("li",{children:(0,c.jsx)(n.N_,{to:"/terms",className:"hover:text-white transition-colors",children:r("landing.footer.legal.terms")})}),(0,c.jsx)("li",{children:(0,c.jsx)(n.N_,{to:"/privacy",className:"hover:text-white transition-colors",children:r("landing.footer.legal.privacy")})}),(0,c.jsx)("li",{children:(0,c.jsx)(n.N_,{to:"/cookies",className:"hover:text-white transition-colors",children:r("landing.footer.legal.cookies")})}),(0,c.jsx)("li",{children:(0,c.jsx)("a",{href:"#",className:"hover:text-white transition-colors",children:r("landing.footer.legal.gdpr")})})]})]})]}),(0,c.jsx)("div",{className:"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400",children:(0,c.jsx)("p",{children:r("landing.footer.copyright")})})]})})]})}},8724:(e,r,t)=>{t.d(r,{Wy:()=>u,l6:()=>x});var s=t(4848),a=t(5410),n=t(2933),o=t(6540),l=t.n(o),i=t(2392),c=t(9264);const d=({trigger:e,children:r,isOpen:t,onToggle:a,position:n="bottom-left",offset:c=8,className:d="",menuClassName:g="",disabled:h=!1,closeOnSelect:m=!0,...x})=>{const[u,p]=(0,o.useState)(!1),b=(0,o.useRef)(null),f=void 0!==t?t:u,y=()=>{a?a(!1):p(!1)};var j,v;return j=b,v=y,(0,o.useEffect)(()=>{const e=e=>{j.current&&!j.current.contains(e.target)&&v()};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[j,v]),(0,s.jsxs)("div",{ref:b,className:(0,i.cn)("relative inline-block",d),...x,children:[(0,s.jsx)("div",{onClick:()=>{h||(a?a(!f):p(!f))},children:e}),f&&(0,s.jsx)("div",{className:(0,i.cn)("absolute z-50 min-w-full","bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5","transform transition-all duration-200 ease-out","animate-scale-in origin-top",{"top-left":"bottom-full left-0 mb-2","top-right":"bottom-full right-0 mb-2","bottom-left":"top-full left-0 mt-2","bottom-right":"top-full right-0 mt-2",left:"right-full top-0 mr-2",right:"left-full top-0 ml-2"}[n],g),style:{marginTop:n.includes("bottom")?c:void 0},children:(0,s.jsx)("div",{className:"py-1",children:l().Children.map(r,e=>l().isValidElement(e)?l().cloneElement(e,{onSelect:m?()=>{e.props.onSelect?.(),y()}:e.props.onSelect}):e)})})]})},g=({children:e,onClick:r,onSelect:t,disabled:a=!1,active:n=!1,className:o="",icon:l,shortcut:c,...d})=>(0,s.jsxs)("button",{type:"button",className:(0,i.cn)("w-full text-left px-4 py-2 text-sm","flex items-center justify-between","transition-colors duration-150",a?"text-gray-400 cursor-not-allowed":["text-gray-700 hover:bg-gray-100 hover:text-gray-900","focus:bg-gray-100 focus:text-gray-900 focus:outline-none"],n&&"bg-primary-50 text-primary-700",o),onClick:e=>{a||(r?.(e),t?.())},disabled:a,...d,children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 min-w-0 flex-1",children:[l&&(0,s.jsx)("span",{className:"flex-shrink-0",children:l}),(0,s.jsx)("span",{className:"truncate",children:e})]}),c&&(0,s.jsx)("span",{className:"text-xs text-gray-400 ml-2",children:c})]}),h=({className:e=""})=>(0,s.jsx)("div",{className:(0,i.cn)("border-t border-gray-100 my-1",e)}),m=({children:e,className:r="",...t})=>(0,s.jsx)("div",{className:(0,i.cn)("px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider",r),...t,children:e}),x=({value:e,onChange:r,options:t=[],placeholder:l="Selectează o opțiune",disabled:h=!1,loading:m=!1,error:x=!1,searchable:u=!1,clearable:p=!1,multiple:b=!1,className:f="",buttonClassName:y="",optionClassName:j="",menuClassName:v="",renderOption:N,renderValue:w,getOptionLabel:k=e=>e?.label||e,getOptionValue:C=e=>e?.value||e,offset:L=8,..._})=>{const[E,A]=(0,o.useState)(!1),[S,z]=(0,o.useState)(""),D=(0,o.useRef)(null),O=b?Array.isArray(e)?e:[]:e?[e]:[],R=u&&S?t.filter(e=>k(e).toLowerCase().includes(S.toLowerCase())):t,F=((e,r,t,s)=>{const[a,n]=(0,o.useState)(-1);return(0,o.useEffect)(()=>{if(!e)return void n(-1);const o=e=>{switch(e.key){case"ArrowDown":e.preventDefault(),n(e=>e<r.length-1?e+1:0);break;case"ArrowUp":e.preventDefault(),n(e=>e>0?e-1:r.length-1);break;case"Enter":case" ":e.preventDefault(),a>=0&&r[a]&&t(r[a]);break;case"Escape":e.preventDefault(),s()}};return document.addEventListener("keydown",o),()=>document.removeEventListener("keydown",o)},[e,r,a,t,s]),a})(E,R,T,()=>A(!1));function T(e){if(b){const t=C(e);let s;s=O.some(e=>C(e)===t)?O.filter(e=>C(e)!==t):[...O,e],r?.(s)}else r?.(e),A(!1);z("")}return(0,s.jsxs)(d,{isOpen:E,onToggle:A,className:f,menuClassName:v,offset:L,trigger:(0,s.jsxs)("button",{type:"button",className:(0,i.cn)("relative w-full bg-white border rounded-md shadow-sm pl-3 pr-10 py-2 text-left","focus:outline-none focus:ring-1 focus:border-primary-500",h?"bg-gray-50 text-gray-500 cursor-not-allowed border-gray-200":"cursor-pointer border-gray-300 hover:border-gray-400",x&&"border-red-300 focus:border-red-500 focus:ring-red-500",y),disabled:h||m,children:[(0,s.jsx)("span",{className:(0,i.cn)("block truncate",!e&&"text-gray-500"),children:w?w(e):b?0===O.length?l:1===O.length?k(O[0]):`${O.length} opțiuni selectate`:e?k(e):l}),(0,s.jsx)("span",{className:"absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none",children:m?(0,s.jsx)(c.Ay,{size:"sm"}):p&&e&&!h?(0,s.jsx)("button",{type:"button",className:"p-1 hover:bg-gray-100 rounded pointer-events-auto",onClick:e=>{e.stopPropagation(),r?.(b?[]:null)},children:(0,s.jsx)("svg",{className:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}):(0,s.jsx)(a.A,{className:(0,i.cn)("w-5 h-5 text-gray-400 transition-transform duration-200",E&&"transform rotate-180")})})]}),closeOnSelect:!b,children:[u&&(0,s.jsx)("div",{className:"p-2 border-b border-gray-100",children:(0,s.jsx)("input",{ref:D,type:"text",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-primary-500 focus:border-primary-500",placeholder:"Caută...",value:S,onChange:e=>z(e.target.value),onClick:e=>e.stopPropagation()})}),(0,s.jsx)("div",{className:"max-h-60 overflow-auto",children:0===R.length?(0,s.jsx)("div",{className:"px-4 py-2 text-sm text-gray-500",children:S?"Nu s-au găsit rezultate":"Nu există opțiuni"}):R.map((e,r)=>{const t=(e=>{const r=C(e);return O.some(e=>C(e)===r)})(e),a=r===F;return(0,s.jsx)(g,{onClick:()=>T(e),active:a,className:(0,i.cn)(t&&"bg-primary-50 text-primary-700",j),children:(0,s.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,s.jsx)("span",{children:N?(0,s.jsx)(s.Fragment,{children:N(e)}):k(e)}),t&&(0,s.jsx)(n.A,{className:"w-4 h-4 text-primary-600"})]})},C(e))})})]})},u=({trigger:e,actions:r=[],position:t="bottom-right",className:a="",...n})=>(0,s.jsx)(d,{trigger:e,position:t,className:a,...n,children:r.map((e,r)=>"separator"===e.type?(0,s.jsx)(h,{},r):"header"===e.type?(0,s.jsx)(m,{children:e.label},r):(0,s.jsx)(g,{onClick:e.onClick,disabled:e.disabled,icon:e.icon,shortcut:e.shortcut,children:e.label},r))})}}]);