import { Request, Response } from 'express';
interface AuthenticatedRequest extends Request {
    user: {
        id: string;
        email: string;
        stripe_customer_id?: string;
    };
}
declare class SubscriptionController {
    getPlans(req: Request, res: Response): Promise<void>;
    getCurrentSubscription(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    createCheckoutSession(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    createCustomerPortal(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    cancelSubscription(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    reactivateSubscription(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    checkCheckoutSession(req: Request, res: Response): Promise<Response<any, Record<string, any>> | undefined>;
    getUsageStats(req: Request, res: Response): Promise<void>;
    checkPermission(req: Request, res: Response): Promise<void>;
    syncPlans(req: Request, res: Response): Promise<void>;
    getSubscriptions(req: Request, res: Response): Promise<void>;
    getSubscriptionStats(req: Request, res: Response): Promise<void>;
    syncPlanStats(req: AuthenticatedRequest, res: Response): Promise<void>;
    getPlanStats(req: AuthenticatedRequest, res: Response): Promise<void>;
    handleWebhook(req: Request, res: Response): Promise<void>;
    handleCheckoutCompleted(session: any): Promise<void>;
    handleSubscriptionUpdated(subscription: any): Promise<void>;
    handleSubscriptionDeleted(subscription: any): Promise<void>;
    handlePaymentSucceeded(invoice: any): Promise<void>;
    handlePaymentFailed(invoice: any): Promise<void>;
}
declare const _default: SubscriptionController;
export default _default;
//# sourceMappingURL=subscriptionController.d.ts.map