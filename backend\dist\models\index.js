"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Expense = exports.Category = exports.User = exports.prisma = void 0;
const prisma_1 = require("./prisma");
Object.defineProperty(exports, "prisma", { enumerable: true, get: function () { return prisma_1.prisma; } });
Object.defineProperty(exports, "User", { enumerable: true, get: function () { return prisma_1.User; } });
Object.defineProperty(exports, "Category", { enumerable: true, get: function () { return prisma_1.Category; } });
Object.defineProperty(exports, "Expense", { enumerable: true, get: function () { return prisma_1.Expense; } });
//# sourceMappingURL=index.js.map