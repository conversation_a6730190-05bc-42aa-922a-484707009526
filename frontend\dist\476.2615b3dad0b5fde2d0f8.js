"use strict";(self.webpackChunkexpense_tracker_frontend=self.webpackChunkexpense_tracker_frontend||[]).push([[476],{476:(e,a,t)=>{t.r(a),t.d(a,{default:()=>k});var s=t(9654),i=t(8624),l=t(7117),r=t(3416),n=t(3322),c=t(6156),d=t(6540),o=t(9582),x=t(6103),m=t(125),u=t(8724),h=t(6215),v=t(9264),j=t(2552),g=t(1235),b=t(5191),p=t(5649),y=t(4477),N=t(2392),f=t(4848);const k=()=>{const[e,a]=(0,d.useState)(""),[t,k]=(0,d.useState)("all"),[A,C]=(0,d.useState)("all"),[z,w]=(0,d.useState)(1),[S,D]=(0,d.useState)(null),[L,B]=(0,d.useState)(!1),[P,R]=(0,d.useState)(!1),[E,O]=(0,d.useState)(""),{data:G,isLoading:_,error:I}=(0,y.kp)({page:z,search:e,status:"all"!==t?t:void 0,plan:"all"!==A?A:void 0,limit:10}),{data:U}=(0,y.RB)(S?.id,{enabled:!!S?.id}),V=(0,y.o4)(),W=(0,y.b4)(),$=(e,a)=>{D(e),O(a),R(!0)},M=e=>{const a={active:{variant:"success",label:"Activ"},inactive:{variant:"secondary",label:"Inactiv"},blocked:{variant:"error",label:"Blocat"},pending:{variant:"warning",label:"În așteptare"}},t=a[e]||a.inactive;return(0,f.jsx)(o.Wh,{variant:t.variant,children:t.label})},T=e=>{const a={free:{variant:"secondary",label:"Gratuit"},basic:{variant:"primary",label:"Basic"},premium:{variant:"success",label:"Premium"},enterprise:{variant:"warning",label:"Enterprise"}},t=a[e?.toLowerCase()]||a.free;return(0,f.jsx)(o.Wh,{variant:t.variant,children:t.label})},F=[{key:"user",label:"Utilizator",render:e=>(0,f.jsxs)("div",{className:"flex items-center",children:[(0,f.jsx)("div",{className:"flex-shrink-0",children:(0,f.jsx)(p.A,{user:{name:`${e.firstName} ${e.lastName}`,avatar:e.avatar,subscription:e.subscription},size:"lg",showBadge:!0})}),(0,f.jsxs)("div",{className:"ml-4",children:[(0,f.jsxs)("div",{className:"text-sm font-medium text-gray-900",children:[e.firstName," ",e.lastName]}),(0,f.jsx)("div",{className:"text-sm text-gray-500",children:e.email})]})]})},{key:"plan",label:"Plan",render:e=>T(e.subscription?.plan?.name)},{key:"status",label:"Status",render:e=>M(e.status)},{key:"revenue",label:"Venituri",render:e=>(0,f.jsxs)("div",{className:"text-sm",children:[(0,f.jsx)("div",{className:"font-medium text-gray-900",children:(0,N.vv)(e.totalRevenue||0)}),(0,f.jsx)("div",{className:"text-gray-500",children:e.subscription?.plan?.price?`${(0,N.vv)(e.subscription.plan.price)}/lună`:"Gratuit"})]})},{key:"joinDate",label:"Data înregistrării",render:e=>(0,f.jsx)("div",{className:"text-sm text-gray-900",children:new Date(e.createdAt).toLocaleDateString("ro-RO")})},{key:"lastActive",label:"Ultima activitate",render:e=>(0,f.jsx)("div",{className:"text-sm text-gray-500",children:e.lastActiveAt?new Date(e.lastActiveAt).toLocaleDateString("ro-RO"):"Niciodată"})},{key:"actions",label:"Acțiuni",render:e=>(0,f.jsx)(u.Wy,{trigger:(0,f.jsx)(x.Ay,{variant:"ghost",size:"sm",children:(0,f.jsx)(s.A,{className:"h-4 w-4"})}),items:[{label:"Vezi detalii",icon:i.A,onClick:()=>{D(e),B(!0)}},{label:"blocked"===e.status?"Deblochează":"Blochează",icon:"blocked"===e.status?l.A:r.A,onClick:()=>$(e,"blocked"===e.status?"unblock":"block"),className:"blocked"===e.status?"text-green-600":"text-red-600"},{label:"Gestionează abonament",icon:CreditCardIcon,onClick:()=>$(e,"manage-subscription")}]})}];if(_)return(0,f.jsx)(m.Ay,{className:"p-6",children:(0,f.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,f.jsx)(v.Ay,{size:"lg"})})});if(I)return(0,f.jsx)(m.Ay,{className:"p-6",children:(0,f.jsx)("div",{className:"text-center text-red-600",children:"Eroare la încărcarea utilizatorilor"})});const Q=G?.users||[],q=Math.ceil((G?.total||0)/10);return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsxs)(m.Ay,{className:"p-6",children:[(0,f.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,f.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Gestionarea utilizatorilor"}),(0,f.jsxs)("div",{className:"text-sm text-gray-500",children:[G?.total||0," utilizatori"]})]}),(0,f.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 mb-6",children:[(0,f.jsx)("div",{className:"flex-1",children:(0,f.jsx)(h.Ay,{type:"text",placeholder:"Caută după nume sau email...",value:e,onChange:e=>a(e.target.value),icon:n.A})}),(0,f.jsxs)("div",{className:"flex gap-2",children:[(0,f.jsxs)("select",{value:t,onChange:e=>k(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500",children:[(0,f.jsx)("option",{value:"all",children:"Toate statusurile"}),(0,f.jsx)("option",{value:"active",children:"Activ"}),(0,f.jsx)("option",{value:"inactive",children:"Inactiv"}),(0,f.jsx)("option",{value:"blocked",children:"Blocat"})]}),(0,f.jsxs)("select",{value:A,onChange:e=>C(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary-500",children:[(0,f.jsx)("option",{value:"all",children:"Toate planurile"}),(0,f.jsx)("option",{value:"free",children:"Gratuit"}),(0,f.jsx)("option",{value:"basic",children:"Basic"}),(0,f.jsx)("option",{value:"premium",children:"Premium"}),(0,f.jsx)("option",{value:"enterprise",children:"Enterprise"})]})]})]}),(0,f.jsx)(b.bQ,{columns:F,data:Q,emptyMessage:"Nu au fost găsiți utilizatori"}),q>1&&(0,f.jsx)("div",{className:"mt-6",children:(0,f.jsx)(g.nw,{currentPage:z,totalPages:q,onPageChange:w})})]}),(0,f.jsx)(j.Ay,{isOpen:L,onClose:()=>B(!1),title:"Detalii utilizator",size:"lg",children:S&&(0,f.jsxs)("div",{className:"space-y-6",children:[(0,f.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,f.jsxs)("div",{children:[(0,f.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Nume complet"}),(0,f.jsxs)("p",{className:"text-sm text-gray-900",children:[S.firstName," ",S.lastName]})]}),(0,f.jsxs)("div",{children:[(0,f.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),(0,f.jsx)("p",{className:"text-sm text-gray-900",children:S.email})]}),(0,f.jsxs)("div",{children:[(0,f.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Status"}),M(S.status)]}),(0,f.jsxs)("div",{children:[(0,f.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Plan curent"}),T(S.subscription?.plan?.name)]}),(0,f.jsxs)("div",{children:[(0,f.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Data înregistrării"}),(0,f.jsx)("p",{className:"text-sm text-gray-900",children:new Date(S.createdAt).toLocaleDateString("ro-RO")})]}),(0,f.jsxs)("div",{children:[(0,f.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Ultima activitate"}),(0,f.jsx)("p",{className:"text-sm text-gray-900",children:S.lastActiveAt?new Date(S.lastActiveAt).toLocaleDateString("ro-RO"):"Niciodată"})]})]}),(0,f.jsxs)("div",{className:"border-t pt-4",children:[(0,f.jsx)("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Statistici"}),(0,f.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-center",children:[(0,f.jsxs)("div",{children:[(0,f.jsx)("p",{className:"text-lg font-semibold text-gray-900",children:S.totalExpenses||0}),(0,f.jsx)("p",{className:"text-xs text-gray-500",children:"Cheltuieli totale"})]}),(0,f.jsxs)("div",{children:[(0,f.jsx)("p",{className:"text-lg font-semibold text-gray-900",children:(0,N.vv)(S.totalRevenue||0)}),(0,f.jsx)("p",{className:"text-xs text-gray-500",children:"Venituri generate"})]}),(0,f.jsxs)("div",{children:[(0,f.jsx)("p",{className:"text-lg font-semibold text-gray-900",children:S.loginCount||0}),(0,f.jsx)("p",{className:"text-xs text-gray-500",children:"Autentificări"})]})]})]})]})}),(0,f.jsx)(j.Ay,{isOpen:P,onClose:()=>R(!1),title:"Confirmă acțiunea",children:(0,f.jsxs)("div",{className:"space-y-4",children:[(0,f.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,f.jsx)(c.A,{className:"h-6 w-6 text-yellow-600"}),(0,f.jsxs)("p",{className:"text-sm text-gray-700",children:["Ești sigur că vrei să ","block"===E?"blochezi":"unblock"===E?"deblochezi":"modifici"," acest utilizator?"]})]}),(0,f.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,f.jsx)(x.Ay,{variant:"secondary",onClick:()=>R(!1),children:"Anulează"}),(0,f.jsx)(x.Ay,{variant:"block"===E?"error":"primary",onClick:()=>{S&&E&&("block"===E?V.mutate(S.id):"unblock"===E&&W.mutate(S.id),R(!1),D(null))},disabled:V.isLoading||W.isLoading,children:V.isLoading||W.isLoading?"Se procesează...":"Confirmă"})]})]})})]})}}}]);