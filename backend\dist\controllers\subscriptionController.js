"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_validator_1 = require("express-validator");
const prisma_1 = require("../config/prisma");
const stripeService_1 = __importDefault(require("../services/stripeService"));
const subscriptionService_1 = __importDefault(require("../services/subscriptionService"));
const usageService_1 = __importDefault(require("../services/usageService"));
const logger_1 = __importDefault(require("../utils/logger"));
class SubscriptionController {
    async getPlans(req, res) {
        try {
            const plans = await subscriptionService_1.default.getAvailablePlans();
            res.json({
                success: true,
                data: plans,
            });
        }
        catch (error) {
            logger_1.default.error('Error getting plans:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to get plans',
            });
        }
    }
    async getCurrentSubscription(req, res) {
        try {
            const userId = req.user.id;
            const user = await prisma_1.prisma.user.findUnique({
                where: { id: userId },
                select: {
                    plan_type: true,
                    subscription_status: true,
                    subscription_id: true,
                    subscription_current_period_start: true,
                    subscription_current_period_end: true,
                    monthly_expense_count: true,
                    monthly_expense_limit: true,
                    subscription: {
                        include: {
                            plan: true,
                        },
                    },
                },
            });
            if (!user) {
                return res.status(404).json({
                    success: false,
                    message: 'User not found',
                });
            }
            const plan = user.subscription?.plan || await prisma_1.prisma.plan.findFirst({
                where: { stripe_id: 'free_plan' },
            });
            const permissions = plan.features;
            const usageStats = await usageService_1.default.getUserUsageStats(userId);
            res.json({
                success: true,
                data: {
                    plan: {
                        id: plan.id,
                        name: plan.name,
                        description: plan.description,
                        price: plan.price,
                        currency: plan.currency,
                        interval: plan.interval,
                    },
                    subscription_status: user.subscription_status,
                    subscription_id: user.subscription_id,
                    current_period_start: user.subscription_current_period_start,
                    current_period_end: user.subscription_current_period_end,
                    usage: usageStats.current_period,
                    permissions,
                    limits: plan.limits,
                },
            });
        }
        catch (error) {
            logger_1.default.error('Error getting current subscription:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to get subscription',
            });
        }
    }
    async createCheckoutSession(req, res) {
        try {
            const errors = (0, express_validator_1.validationResult)(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array(),
                });
            }
            const { planId } = req.body;
            const userId = req.user.id;
            const userEmail = req.user.email;
            const selectedPlan = await prisma_1.prisma.plan.findUnique({
                where: { id: planId },
            });
            if (!selectedPlan || !selectedPlan.stripe_id || selectedPlan.stripe_id === 'free_plan') {
                return res.status(400).json({
                    success: false,
                    message: 'Invalid plan selected',
                });
            }
            const user = await prisma_1.prisma.user.findUnique({
                where: { id: userId },
                select: { subscription_status: true, email: true },
            });
            if (user.subscription_status === 'active') {
                return res.status(400).json({
                    success: false,
                    message: 'User already has an active subscription',
                });
            }
            const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
            const session = await stripe.checkout.sessions.create({
                customer_email: user.email,
                payment_method_types: ['card'],
                line_items: [{
                        price: selectedPlan.stripe_id,
                        quantity: 1,
                    }],
                mode: 'subscription',
                success_url: `${process.env.FRONTEND_URL}/subscription/success?session_id={CHECKOUT_SESSION_ID}`,
                cancel_url: `${process.env.FRONTEND_URL}/subscription/cancel`,
                metadata: {
                    userId,
                    planId,
                },
            });
            res.json({
                success: true,
                data: {
                    sessionId: session.id,
                    url: session.url,
                },
            });
        }
        catch (error) {
            logger_1.default.error('Error creating checkout session:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to create checkout session',
            });
        }
    }
    async createCustomerPortal(req, res) {
        try {
            const userId = req.user.id;
            const customerId = req.user.stripe_customer_id;
            if (!customerId) {
                return res.status(400).json({
                    success: false,
                    message: 'No Stripe customer found for this user',
                });
            }
            const portalSession = await stripeService_1.default.createCustomerPortal({
                customerId,
                returnUrl: `${process.env.FRONTEND_URL}/subscription`,
            });
            res.json({
                success: true,
                data: {
                    url: portalSession.url,
                },
            });
        }
        catch (error) {
            logger_1.default.error('Error creating customer portal:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to create customer portal',
            });
        }
    }
    async cancelSubscription(req, res) {
        try {
            const userId = req.user.id;
            const user = await prisma_1.prisma.user.findUnique({
                where: { id: userId },
                select: {
                    subscription_id: true,
                    subscription_status: true,
                },
            });
            if (!user.subscription_id || user.subscription_status !== 'active') {
                return res.status(400).json({
                    success: false,
                    message: 'No active subscription found',
                });
            }
            const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
            const canceledSubscription = await stripe.subscriptions.update(user.subscription_id, {
                cancel_at_period_end: true,
            });
            await prisma_1.prisma.user.update({
                where: { id: userId },
                data: {
                    subscription_status: 'canceled',
                },
            });
            await prisma_1.prisma.subscription.updateMany({
                where: { user_id: userId },
                data: {
                    status: 'canceled',
                    canceled_at: new Date(),
                },
            });
            res.json({
                success: true,
                message: 'Subscription will be canceled at the end of the current period',
                data: {
                    subscription: canceledSubscription,
                },
            });
        }
        catch (error) {
            logger_1.default.error('Error canceling subscription:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to cancel subscription',
            });
        }
    }
    async reactivateSubscription(req, res) {
        try {
            const userId = req.user.id;
            const subscription = await subscriptionService_1.default.getUserSubscription(userId);
            if (!subscription || subscription.status !== 'canceled') {
                return res.status(400).json({
                    success: false,
                    message: 'No canceled subscription found',
                });
            }
            const reactivatedSubscription = await stripeService_1.default.reactivateSubscription(subscription.stripe_id);
            await subscriptionService_1.default.updateSubscription(subscription.stripe_id, {
                status: 'active',
                canceled_at: null,
                updated_at: new Date(),
            });
            res.json({
                success: true,
                message: 'Subscription reactivated successfully',
                data: {
                    subscription: reactivatedSubscription,
                },
            });
        }
        catch (error) {
            logger_1.default.error('Error reactivating subscription:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to reactivate subscription',
            });
        }
    }
    async checkCheckoutSession(req, res) {
        try {
            const { sessionId } = req.params;
            if (!sessionId) {
                return res.status(400).json({
                    success: false,
                    message: 'Session ID is required',
                });
            }
            const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
            const session = await stripe.checkout.sessions.retrieve(sessionId);
            if (!session) {
                return res.status(404).json({
                    success: false,
                    message: 'Checkout session not found',
                });
            }
            res.json({
                success: true,
                data: {
                    status: session.payment_status,
                    customerEmail: session.customer_email,
                    subscriptionId: session.subscription,
                },
            });
        }
        catch (error) {
            logger_1.default.error('Error checking checkout session:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to check checkout session',
            });
        }
    }
    async getUsageStats(req, res) {
        try {
            const userId = req.user.id;
            const { startDate, endDate } = req.query;
            const stats = await usageService_1.default.getUserUsageStats(userId, startDate, endDate);
            res.json({
                success: true,
                data: stats,
            });
        }
        catch (error) {
            logger_1.default.error('Error getting usage stats:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to get usage statistics',
            });
        }
    }
    async checkPermission(req, res) {
        try {
            const { action } = req.params;
            const userId = req.user.id;
            const canPerform = await subscriptionService_1.default.canUserPerformAction(userId, action);
            const usageProgress = await usageService_1.default.getUsageProgress(userId);
            res.json({
                success: true,
                data: {
                    canPerform,
                    usage: usageProgress,
                },
            });
        }
        catch (error) {
            logger_1.default.error('Error checking permission:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to check permission',
            });
        }
    }
    async syncPlans(req, res) {
        try {
            await subscriptionService_1.default.syncPlansFromStripe();
            res.json({
                success: true,
                message: 'Plans synced successfully',
            });
        }
        catch (error) {
            logger_1.default.error('Error syncing plans:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to sync plans',
            });
        }
    }
    async getSubscriptions(req, res) {
        try {
            const errors = (0, express_validator_1.validationResult)(req);
            if (!errors.isEmpty()) {
                return res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array(),
                });
            }
            const { page = 1, limit = 10, search = '', status = 'all', sortBy = 'created_at', sortOrder = 'desc' } = req.query;
            const offset = (parseInt(page) - 1) * parseInt(limit);
            const where = {};
            if (status !== 'all') {
                where.status = status;
            }
            if (search) {
                where.OR = [
                    {
                        user: {
                            email: {
                                contains: search,
                                mode: 'insensitive'
                            }
                        }
                    },
                    {
                        plan: {
                            name: {
                                contains: search,
                                mode: 'insensitive'
                            }
                        }
                    }
                ];
            }
            const [subscriptions, total] = await Promise.all([
                prisma_1.prisma.subscription.findMany({
                    where,
                    include: {
                        user: {
                            select: {
                                id: true,
                                email: true,
                                name: true
                            }
                        },
                        plan: {
                            select: {
                                id: true,
                                name: true,
                                price: true,
                                currency: true,
                                interval: true
                            }
                        }
                    },
                    orderBy: {
                        [sortBy]: sortOrder
                    },
                    skip: offset,
                    take: parseInt(limit)
                }),
                prisma_1.prisma.subscription.count({ where })
            ]);
            const totalPages = Math.ceil(total / parseInt(limit));
            res.json({
                success: true,
                data: {
                    subscriptions,
                    pagination: {
                        current_page: parseInt(page),
                        total_pages: totalPages,
                        total_items: total,
                        items_per_page: parseInt(limit),
                        has_next: parseInt(page) < totalPages,
                        has_prev: parseInt(page) > 1
                    }
                }
            });
        }
        catch (error) {
            logger_1.default.error('Error getting subscriptions:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to get subscriptions'
            });
        }
    }
    async getSubscriptionStats(req, res) {
        try {
            const { startDate, endDate } = req.query;
            const subscriptionStats = await subscriptionService_1.default.getSubscriptionStats();
            const usageStats = await usageService_1.default.getGlobalUsageStats(startDate, endDate);
            res.json({
                success: true,
                data: {
                    subscriptions: subscriptionStats,
                    usage: usageStats,
                },
            });
        }
        catch (error) {
            logger_1.default.error('Error getting subscription stats:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to get subscription statistics',
            });
        }
    }
    async syncPlanStats(req, res) {
        try {
            const plans = await prisma_1.prisma.plan.findMany({
                include: {
                    subscriptions: {
                        where: {
                            status: 'active',
                        },
                    },
                },
            });
            const stats = plans.map(plan => ({
                id: plan.id,
                name: plan.name,
                active_subscriptions: plan.subscriptions.length,
                total_revenue: plan.subscriptions.length * (Number(plan.price) || 0),
            }));
            res.json({
                success: true,
                message: 'Plan statistics retrieved successfully',
                data: stats,
            });
        }
        catch (error) {
            logger_1.default.error('Error syncing plan stats:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to sync plan statistics',
            });
        }
    }
    async getPlanStats(req, res) {
        try {
            const plans = await prisma_1.prisma.plan.findMany({
                include: {
                    subscriptions: {
                        where: {
                            status: 'active',
                        },
                    },
                    _count: {
                        select: {
                            subscriptions: true,
                        },
                    },
                },
            });
            const totalUsers = await prisma_1.prisma.user.count();
            const activeSubscriptions = await prisma_1.prisma.subscription.count({
                where: { status: 'active' },
            });
            const stats = {
                total_users: totalUsers,
                active_subscriptions: activeSubscriptions,
                plans: plans.map(plan => ({
                    id: plan.id,
                    name: plan.name,
                    price: plan.price,
                    active_subscriptions: plan.subscriptions.length,
                    total_subscriptions: plan._count.subscriptions,
                    monthly_revenue: plan.subscriptions.length * (Number(plan.price) || 0),
                })),
            };
            res.json({
                success: true,
                data: stats,
            });
        }
        catch (error) {
            logger_1.default.error('Error getting plan stats:', error);
            res.status(500).json({
                success: false,
                message: 'Failed to get plan statistics',
            });
        }
    }
    async handleWebhook(req, res) {
        try {
            const sig = req.headers['stripe-signature'];
            const endpointSecret = process.env.STRIPE_WEBHOOK_SECRET;
            let event;
            try {
                const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
                event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
            }
            catch (err) {
                const error = err;
                logger_1.default.error('Webhook signature verification failed:', error.message);
                res.status(400).send(`Webhook Error: ${error.message}`);
                return;
            }
            await prisma_1.prisma.webhookEvent.create({
                data: {
                    stripe_id: event.id,
                    type: event.type,
                    processed: false,
                    data: event.data,
                },
            });
            switch (event.type) {
                case 'checkout.session.completed':
                    await this.handleCheckoutCompleted(event.data.object);
                    break;
                case 'customer.subscription.updated':
                    await this.handleSubscriptionUpdated(event.data.object);
                    break;
                case 'customer.subscription.deleted':
                    await this.handleSubscriptionDeleted(event.data.object);
                    break;
                case 'invoice.payment_succeeded':
                    await this.handlePaymentSucceeded(event.data.object);
                    break;
                case 'invoice.payment_failed':
                    await this.handlePaymentFailed(event.data.object);
                    break;
                default:
                    logger_1.default.info(`Unhandled event type: ${event.type}`);
            }
            await prisma_1.prisma.webhookEvent.updateMany({
                where: { stripe_event_id: event.id },
                data: { processed: true },
            });
            res.json({ received: true });
        }
        catch (error) {
            logger_1.default.error('Error handling webhook:', error);
            res.status(500).json({
                success: false,
                message: 'Webhook processing failed',
            });
        }
    }
    async handleCheckoutCompleted(session) {
        try {
            const { userId } = session.metadata;
            const { planId } = session.metadata;
            if (!userId || !planId) {
                logger_1.default.error('Missing metadata in checkout session:', session.id);
                return;
            }
            const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
            const subscription = await stripe.subscriptions.retrieve(session.subscription);
            const plan = await prisma_1.prisma.plan.findUnique({ where: { id: planId } });
            if (!plan) {
                logger_1.default.error(`Plan not found: ${planId}`);
                return;
            }
            await prisma_1.prisma.subscription.create({
                data: {
                    user_id: userId,
                    plan_id: planId,
                    stripe_subscription_id: subscription.id,
                    stripe_customer_id: subscription.customer,
                    status: subscription.status,
                    current_period_start: new Date(subscription.current_period_start * 1000),
                    current_period_end: new Date(subscription.current_period_end * 1000),
                },
            });
            await prisma_1.prisma.user.update({
                where: { id: userId },
                data: {
                    plan_type: plan.name.toLowerCase(),
                    subscription_status: subscription.status,
                    subscription_id: subscription.id,
                    subscription_current_period_start: new Date(subscription.current_period_start * 1000),
                    subscription_current_period_end: new Date(subscription.current_period_end * 1000),
                    monthly_expense_limit: plan.limits.monthly_expenses || 1000,
                },
            });
            await usageService_1.default.resetMonthlyUsage(userId);
            logger_1.default.info(`Subscription created for user ${userId}, plan ${plan.name}`);
        }
        catch (error) {
            logger_1.default.error('Error handling checkout completed:', error);
            throw error;
        }
    }
    async handleSubscriptionUpdated(subscription) {
        try {
            const user = await prisma_1.prisma.user.findFirst({
                where: { subscription_id: subscription.id },
            });
            if (!user) {
                logger_1.default.error(`User not found for subscription: ${subscription.id}`);
                return;
            }
            await prisma_1.prisma.subscription.updateMany({
                where: { stripe_subscription_id: subscription.id },
                data: {
                    status: subscription.status,
                    current_period_start: new Date(subscription.current_period_start * 1000),
                    current_period_end: new Date(subscription.current_period_end * 1000),
                },
            });
            await prisma_1.prisma.user.update({
                where: { id: user.id },
                data: {
                    subscription_status: subscription.status,
                    subscription_current_period_start: new Date(subscription.current_period_start * 1000),
                    subscription_current_period_end: new Date(subscription.current_period_end * 1000),
                },
            });
            if (subscription.status === 'canceled') {
                await subscriptionService_1.default.downgradeToFree(user.id);
            }
            logger_1.default.info(`Subscription updated for user ${user.id}, status: ${subscription.status}`);
        }
        catch (error) {
            logger_1.default.error('Error handling subscription updated:', error);
            throw error;
        }
    }
    async handleSubscriptionDeleted(subscription) {
        try {
            const user = await prisma_1.prisma.user.findFirst({
                where: { subscription_id: subscription.id },
            });
            if (!user) {
                logger_1.default.error(`User not found for subscription: ${subscription.id}`);
                return;
            }
            await prisma_1.prisma.subscription.updateMany({
                where: { stripe_subscription_id: subscription.id },
                data: {
                    status: 'canceled',
                    canceled_at: new Date(),
                },
            });
            await subscriptionService_1.default.downgradeToFree(user.id);
            logger_1.default.info(`Subscription deleted for user ${user.id}`);
        }
        catch (error) {
            logger_1.default.error('Error handling subscription deleted:', error);
            throw error;
        }
    }
    async handlePaymentSucceeded(invoice) {
        try {
            const subscriptionId = invoice.subscription;
            if (!subscriptionId) {
                return;
            }
            const user = await prisma_1.prisma.user.findFirst({
                where: { subscription_id: subscriptionId },
            });
            if (!user) {
                logger_1.default.error(`User not found for subscription: ${subscriptionId}`);
                return;
            }
            await usageService_1.default.resetMonthlyUsage(user.id);
            logger_1.default.info(`Payment succeeded for user ${user.id}, subscription ${subscriptionId}`);
        }
        catch (error) {
            logger_1.default.error('Error handling payment succeeded:', error);
            throw error;
        }
    }
    async handlePaymentFailed(invoice) {
        try {
            const subscriptionId = invoice.subscription;
            if (!subscriptionId) {
                return;
            }
            const user = await prisma_1.prisma.user.findFirst({
                where: { subscription_id: subscriptionId },
            });
            if (!user) {
                logger_1.default.error(`User not found for subscription: ${subscriptionId}`);
                return;
            }
            await prisma_1.prisma.subscription.updateMany({
                where: { stripe_subscription_id: subscriptionId },
                data: {
                    status: 'past_due',
                },
            });
            await prisma_1.prisma.user.update({
                where: { id: user.id },
                data: {
                    subscription_status: 'past_due',
                },
            });
            logger_1.default.info(`Payment failed for user ${user.id}, subscription ${subscriptionId}`);
        }
        catch (error) {
            logger_1.default.error('Error handling payment failed:', error);
            throw error;
        }
    }
}
exports.default = new SubscriptionController();
//# sourceMappingURL=subscriptionController.js.map