"use strict";(self.webpackChunkexpense_tracker_frontend=self.webpackChunkexpense_tracker_frontend||[]).push([[896],{2896:(e,t,a)=>{a.r(t),a.d(t,{default:()=>f});var s=a(4015),r=a(6364),i=a(5763),c=a(7117),l=a(8404),n=a(7037),o=a(4985),d=a(587),m=a(2254),x=a(6156),u=a(6540),h=a(2389),g=a(4976),p=a(8700),b=a(4848);const f=()=>{const{t:e}=(0,h.Bd)(),[t,a]=(0,u.useState)({name:"",email:"",subject:"",category:"general",priority:"medium",message:""}),[f,j]=(0,u.useState)(!1),[y,N]=(0,u.useState)(null),v=[{id:"general",name:e("contact.categories.general","Întrebare Generală"),icon:s.A},{id:"technical",name:e("contact.categories.technical","Problemă Tehnică"),icon:r.A},{id:"billing",name:e("contact.categories.billing","Facturare"),icon:i.A},{id:"feature",name:e("contact.categories.feature","Cerere Funcționalitate"),icon:c.A}],w=[{id:"low",name:e("contact.priority.low","Scăzută"),color:"text-green-600"},{id:"medium",name:e("contact.priority.medium","Medie"),color:"text-yellow-600"},{id:"high",name:e("contact.priority.high","Înaltă"),color:"text-red-600"}],q=[{icon:l.A,title:e("contact.methods.chat.title","Chat Live"),description:e("contact.methods.chat.description","Răspuns imediat pentru întrebări urgente"),availability:e("contact.methods.chat.availability","Luni - Vineri, 9:00 - 18:00"),action:e("contact.methods.chat.action","Începeți Chat"),color:"bg-blue-500"},{icon:i.A,title:e("contact.methods.email.title","Email"),description:e("contact.methods.email.description","Pentru întrebări detaliate și documentație"),availability:e("contact.methods.email.availability","Răspuns în 24 ore"),action:"<EMAIL>",color:"bg-green-500"},{icon:n.A,title:e("contact.methods.phone.title","Telefon"),description:e("contact.methods.phone.description","Suport telefonic pentru clienții Premium"),availability:e("contact.methods.phone.availability","Luni - Vineri, 10:00 - 17:00"),action:"+40 21 123 4567",color:"bg-purple-500"}],A=e=>{const{name:t,value:s}=e.target;a(e=>({...e,[t]:s}))};return(0,b.jsx)(p.A,{children:(0,b.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,b.jsx)("div",{className:"bg-white shadow-sm",children:(0,b.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",children:[(0,b.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,b.jsxs)(g.N_,{to:"/",className:"flex items-center text-gray-600 hover:text-gray-900 transition-colors",children:[(0,b.jsx)(o.A,{className:"w-5 h-5 mr-2"}),e("common.back","Înapoi")]}),(0,b.jsx)("div",{className:"h-6 w-px bg-gray-300"}),(0,b.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:e("support.contact.title","Contact")})]}),(0,b.jsx)("p",{className:"mt-4 text-lg text-gray-600 max-w-3xl",children:e("support.contact.subtitle","Suntem aici să vă ajutăm! Alegeți metoda de contact care vi se potrivește cel mai bine.")})]})}),(0,b.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,b.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,b.jsxs)("div",{className:"lg:col-span-1",children:[(0,b.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-6",children:e("contact.methods.title","Metode de Contact")}),(0,b.jsx)("div",{className:"space-y-4",children:q.map((e,t)=>{const a=e.icon;return(0,b.jsx)("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-200",children:(0,b.jsxs)("div",{className:"flex items-start",children:[(0,b.jsx)("div",{className:`${e.color} p-3 rounded-lg mr-4`,children:(0,b.jsx)(a,{className:"w-6 h-6 text-white"})}),(0,b.jsxs)("div",{className:"flex-1",children:[(0,b.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:e.title}),(0,b.jsx)("p",{className:"text-gray-600 text-sm mb-3",children:e.description}),(0,b.jsxs)("div",{className:"flex items-center text-sm text-gray-500 mb-3",children:[(0,b.jsx)(d.A,{className:"w-4 h-4 mr-1"}),e.availability]}),(0,b.jsx)("button",{className:"text-blue-600 hover:text-blue-800 font-medium text-sm",children:e.action})]})]})},t)})}),(0,b.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-200 mt-6",children:[(0,b.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:e("contact.office.title","Biroul Nostru")}),(0,b.jsxs)("div",{className:"space-y-3",children:[(0,b.jsxs)("div",{className:"flex items-start",children:[(0,b.jsx)(m.A,{className:"w-5 h-5 text-gray-400 mr-3 mt-0.5"}),(0,b.jsxs)("div",{children:[(0,b.jsx)("p",{className:"text-gray-900 font-medium",children:e("contact.office.address.title","Adresa")}),(0,b.jsxs)("p",{className:"text-gray-600 text-sm",children:[e("contact.office.address.line1","Strada Exemplu nr. 123"),(0,b.jsx)("br",{}),e("contact.office.address.line2","Sector 1, București"),(0,b.jsx)("br",{}),e("contact.office.address.line3","România, 010101")]})]})]}),(0,b.jsxs)("div",{className:"flex items-start",children:[(0,b.jsx)(d.A,{className:"w-5 h-5 text-gray-400 mr-3 mt-0.5"}),(0,b.jsxs)("div",{children:[(0,b.jsx)("p",{className:"text-gray-900 font-medium",children:e("contact.office.hours.title","Program")}),(0,b.jsxs)("p",{className:"text-gray-600 text-sm",children:[e("contact.office.hours.weekdays","Luni - Vineri: 9:00 - 18:00"),(0,b.jsx)("br",{}),e("contact.office.hours.weekend","Sâmbătă - Duminică: Închis")]})]})]})]})]})]}),(0,b.jsx)("div",{className:"lg:col-span-2",children:(0,b.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8",children:[(0,b.jsx)("h2",{className:"text-2xl font-semibold text-gray-900 mb-6",children:e("contact.form.title","Trimiteți-ne un Mesaj")}),"success"===y&&(0,b.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-6",children:(0,b.jsxs)("div",{className:"flex items-center",children:[(0,b.jsx)(c.A,{className:"w-5 h-5 text-green-600 mr-3"}),(0,b.jsx)("p",{className:"text-green-800",children:e("contact.form.success","Mesajul a fost trimis cu succes! Vă vom răspunde în curând.")})]})}),"error"===y&&(0,b.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6",children:(0,b.jsxs)("div",{className:"flex items-center",children:[(0,b.jsx)(x.A,{className:"w-5 h-5 text-red-600 mr-3"}),(0,b.jsx)("p",{className:"text-red-800",children:e("contact.form.error","A apărut o eroare. Vă rugăm să încercați din nou.")})]})}),(0,b.jsxs)("form",{onSubmit:async e=>{e.preventDefault(),j(!0);try{await new Promise(e=>setTimeout(e,2e3)),N("success"),a({name:"",email:"",subject:"",category:"general",priority:"medium",message:""})}catch(e){N("error")}finally{j(!1)}},className:"space-y-6",children:[(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,b.jsxs)("div",{children:[(0,b.jsxs)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:[e("contact.form.name","Nume Complet")," *"]}),(0,b.jsx)("input",{type:"text",id:"name",name:"name",value:t.name,onChange:A,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:e("contact.form.name_placeholder","Introduceți numele dvs.")})]}),(0,b.jsxs)("div",{children:[(0,b.jsxs)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:[e("contact.form.email","Email")," *"]}),(0,b.jsx)("input",{type:"email",id:"email",name:"email",value:t.email,onChange:A,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:e("contact.form.email_placeholder","<EMAIL>")})]})]}),(0,b.jsxs)("div",{children:[(0,b.jsxs)("label",{htmlFor:"subject",className:"block text-sm font-medium text-gray-700 mb-2",children:[e("contact.form.subject","Subiect")," *"]}),(0,b.jsx)("input",{type:"text",id:"subject",name:"subject",value:t.subject,onChange:A,required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:e("contact.form.subject_placeholder","Descrieți pe scurt problema")})]}),(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{htmlFor:"category",className:"block text-sm font-medium text-gray-700 mb-2",children:e("contact.form.category","Categorie")}),(0,b.jsx)("select",{id:"category",name:"category",value:t.category,onChange:A,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:v.map(e=>(0,b.jsx)("option",{value:e.id,children:e.name},e.id))})]}),(0,b.jsxs)("div",{children:[(0,b.jsx)("label",{htmlFor:"priority",className:"block text-sm font-medium text-gray-700 mb-2",children:e("contact.form.priority","Prioritate")}),(0,b.jsx)("select",{id:"priority",name:"priority",value:t.priority,onChange:A,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:w.map(e=>(0,b.jsx)("option",{value:e.id,children:e.name},e.id))})]})]}),(0,b.jsxs)("div",{children:[(0,b.jsxs)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-2",children:[e("contact.form.message","Mesaj")," *"]}),(0,b.jsx)("textarea",{id:"message",name:"message",value:t.message,onChange:A,required:!0,rows:6,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:e("contact.form.message_placeholder","Descrieți detaliat problema sau întrebarea dvs...")})]}),(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("p",{className:"text-sm text-gray-500",children:["* ",e("contact.form.required","Câmpuri obligatorii")]}),(0,b.jsx)("button",{type:"submit",disabled:f,className:"bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:f?e("contact.form.sending","Se trimite..."):e("contact.form.send","Trimite Mesajul")})]})]})]})})]}),(0,b.jsxs)("div",{className:"mt-16",children:[(0,b.jsxs)("div",{className:"text-center mb-12",children:[(0,b.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:e("contact.faq.title","Întrebări Frecvente")}),(0,b.jsx)("p",{className:"text-lg text-gray-600",children:e("contact.faq.subtitle","Poate găsiți răspunsul aici înainte de a ne contacta")})]}),(0,b.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,b.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-200",children:[(0,b.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:e("contact.faq.response_time.question","Cât durează să primiți un răspuns?")}),(0,b.jsx)("p",{className:"text-gray-600",children:e("contact.faq.response_time.answer","De obicei răspundem în 24 de ore pentru email și imediat pentru chat live în timpul programului.")})]}),(0,b.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-200",children:[(0,b.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:e("contact.faq.technical_support.question","Oferiti suport tehnic?")}),(0,b.jsx)("p",{className:"text-gray-600",children:e("contact.faq.technical_support.answer","Da, echipa noastră tehnică este disponibilă pentru a vă ajuta cu orice problemă tehnică.")})]}),(0,b.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-200",children:[(0,b.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:e("contact.faq.billing_support.question","Pot primi ajutor cu facturarea?")}),(0,b.jsx)("p",{className:"text-gray-600",children:e("contact.faq.billing_support.answer","Absolut! Echipa noastră de facturare vă poate ajuta cu orice întrebări legate de cont și plăți.")})]}),(0,b.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm border border-gray-200",children:[(0,b.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:e("contact.faq.feature_request.question","Cum pot cere o funcționalitate nouă?")}),(0,b.jsx)("p",{className:"text-gray-600",children:e("contact.faq.feature_request.answer",'Folosiți formularul de contact și selectați "Cerere Funcționalitate" ca categorie. Apreciem feedback-ul!')})]})]})]})]})]})})}}}]);