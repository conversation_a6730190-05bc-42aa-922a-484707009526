module.exports = {
	ci: {
		collect: {
			url: [
				'http://localhost:5173',
				'http://localhost:5173/login',
				'http://localhost:5173/register',
				'http://localhost:5173/dashboard',
				'http://localhost:5173/expenses',
				'http://localhost:5173/expenses/add',
				'http://localhost:5173/categories',
				'http://localhost:5173/reports',
				'http://localhost:5173/settings',
				'http://localhost:5173/profile',
			],
			startServerCommand: 'npm run preview',
			startServerReadyPattern: 'Local:.*:5173',
			startServerReadyTimeout: 30000,
			numberOfRuns: 3,
			settings: {
				chromeFlags: '--no-sandbox --disable-dev-shm-usage',
				preset: 'desktop',
				throttling: {
					rttMs: 40,
					throughputKbps: 10240,
					cpuSlowdownMultiplier: 1,
					requestLatencyMs: 0,
					downloadThroughputKbps: 0,
					uploadThroughputKbps: 0,
				},
				screenEmulation: {
					mobile: false,
					width: 1350,
					height: 940,
					deviceScaleFactor: 1,
					disabled: false,
				},
				formFactor: 'desktop',
				throttlingMethod: 'simulate',
				auditMode: false,
				gatherMode: false,
				disableStorageReset: false,
				debugNavigation: false,
				maxWaitForLoad: 45000,
				maxWaitForFcp: 15000,
				pauseAfterFcpMs: 1000,
				pauseAfterLoadMs: 1000,
				networkQuietThresholdMs: 1000,
				cpuQuietThresholdMs: 1000,
			},
		},
		assert: {
			assertions: {
				'categories:performance': ['error', { minScore: 0.8 }],
				'categories:accessibility': ['error', { minScore: 0.9 }],
				'categories:best-practices': ['error', { minScore: 0.85 }],
				'categories:seo': ['error', { minScore: 0.8 }],
				'categories:pwa': ['warn', { minScore: 0.6 }],

				// Performance metrics
				'first-contentful-paint': ['warn', { maxNumericValue: 2000 }],
				'largest-contentful-paint': ['error', { maxNumericValue: 3000 }],
				'first-meaningful-paint': ['warn', { maxNumericValue: 2500 }],
				'speed-index': ['warn', { maxNumericValue: 3000 }],
				interactive: ['error', { maxNumericValue: 4000 }],
				'total-blocking-time': ['error', { maxNumericValue: 300 }],
				'cumulative-layout-shift': ['error', { maxNumericValue: 0.1 }],

				// Resource optimization
				'unused-javascript': ['warn', { maxNumericValue: 100000 }],
				'unused-css-rules': ['warn', { maxNumericValue: 50000 }],
				'render-blocking-resources': ['warn', { maxNumericValue: 500 }],
				'unminified-css': ['error', { maxNumericValue: 0 }],
				'unminified-javascript': ['error', { maxNumericValue: 0 }],
				'efficient-animated-content': ['warn', { maxNumericValue: 0 }],
				'uses-optimized-images': ['warn', { maxNumericValue: 100000 }],
				'uses-webp-images': ['warn', { maxNumericValue: 100000 }],
				'uses-responsive-images': ['warn', { maxNumericValue: 100000 }],

				// Network optimization
				'uses-http2': ['error', { minScore: 1 }],
				'uses-long-cache-ttl': ['warn', { minScore: 0.75 }],
				'uses-text-compression': ['error', { minScore: 1 }],
				redirects: ['error', { maxNumericValue: 0 }],

				// Accessibility
				'color-contrast': ['error', { minScore: 1 }],
				'image-alt': ['error', { minScore: 1 }],
				label: ['error', { minScore: 1 }],
				'link-name': ['error', { minScore: 1 }],
				'button-name': ['error', { minScore: 1 }],
				'document-title': ['error', { minScore: 1 }],
				'html-has-lang': ['error', { minScore: 1 }],
				'html-lang-valid': ['error', { minScore: 1 }],
				'meta-viewport': ['error', { minScore: 1 }],

				// SEO
				'meta-description': ['error', { minScore: 1 }],
				'http-status-code': ['error', { minScore: 1 }],
				'link-text': ['error', { minScore: 1 }],
				'is-crawlable': ['error', { minScore: 1 }],
				'robots-txt': ['warn', { minScore: 1 }],
				hreflang: ['warn', { minScore: 1 }],
				canonical: ['warn', { minScore: 1 }],

				// Best practices
				'is-on-https': ['error', { minScore: 1 }],
				'uses-https': ['error', { minScore: 1 }],
				'no-vulnerable-libraries': ['error', { minScore: 1 }],
				'external-anchors-use-rel-noopener': ['error', { minScore: 1 }],
				'geolocation-on-start': ['error', { minScore: 1 }],
				'notification-on-start': ['error', { minScore: 1 }],
				'no-document-write': ['error', { minScore: 1 }],
				'js-libraries': ['warn', { minScore: 0.8 }],
				deprecations: ['error', { minScore: 1 }],
				'errors-in-console': ['warn', { maxNumericValue: 0 }],

				// PWA (warnings only since it's optional)
				'installable-manifest': ['warn', { minScore: 1 }],
				'service-worker': ['warn', { minScore: 1 }],
				'works-offline': ['warn', { minScore: 1 }],
				viewport: ['warn', { minScore: 1 }],
				'without-javascript': ['warn', { minScore: 1 }],
			},
		},
		upload: {
			target: 'temporary-public-storage',
			githubAppToken: process.env.LHCI_GITHUB_APP_TOKEN,
			githubToken: process.env.GITHUB_TOKEN,
			githubApiHost: 'api.github.com',
			githubStatusContextSuffix: '/lighthouse',
		},
		server: {
			port: 9001,
			storage: {
				storageMethod: 'sql',
				sqlDialect: 'sqlite',
				sqlDatabasePath: './lhci.db',
			},
		},
		wizard: {
			// Configuration for LHCI wizard
		},
	},
};

// Environment-specific overrides
if (process.env.CI) {
	// CI-specific settings
	module.exports.ci.collect.numberOfRuns = 1; // Faster CI runs
	module.exports.ci.collect.settings.chromeFlags += ' --headless';
}

if (process.env.NODE_ENV === 'production') {
	// Production URLs
	module.exports.ci.collect.url = [
		process.env.FRONTEND_URL || 'https://your-domain.com',
		`${process.env.FRONTEND_URL || 'https://your-domain.com'}/login`,
		`${process.env.FRONTEND_URL || 'https://your-domain.com'}/register`,
		// Add other production URLs
	];

	// Remove local server command for production
	delete module.exports.ci.collect.startServerCommand;
	delete module.exports.ci.collect.startServerReadyPattern;
	delete module.exports.ci.collect.startServerReadyTimeout;
}

if (process.env.LHCI_BUILD_CONTEXT__CURRENT_HASH) {
	// GitHub Actions context
	module.exports.ci.upload.githubStatusContextSuffix = `/lighthouse-${process.env.LHCI_BUILD_CONTEXT__CURRENT_HASH.slice(
		0,
		7
	)}`;
}

// Mobile testing configuration
if (process.env.LIGHTHOUSE_MOBILE === 'true') {
	module.exports.ci.collect.settings = {
		...module.exports.ci.collect.settings,
		preset: 'mobile',
		formFactor: 'mobile',
		screenEmulation: {
			mobile: true,
			width: 375,
			height: 667,
			deviceScaleFactor: 2,
			disabled: false,
		},
		throttling: {
			rttMs: 150,
			throughputKbps: 1638.4,
			cpuSlowdownMultiplier: 4,
			requestLatencyMs: 0,
			downloadThroughputKbps: 0,
			uploadThroughputKbps: 0,
		},
	};
}
