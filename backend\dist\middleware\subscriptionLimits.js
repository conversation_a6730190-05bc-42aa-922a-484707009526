"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.checkExportPermission = exports.checkExportLimit = exports.checkExpenseLimit = exports.checkCategoryLimit = void 0;
const prisma_1 = require("../config/prisma");
const checkCategoryLimit = async (req, res, next) => {
    try {
        const { user } = req;
        if (!user) {
            res.status(401).json({
                success: false,
                message: 'Authentication required'
            });
            return;
        }
        const categoryCount = await prisma_1.prisma.category.count({
            where: {
                user_id: user.id,
                deleted_at: null
            }
        });
        const limits = {
            free: 10,
            basic: 25,
            premium: 100,
            enterprise: 1000
        };
        const userPlan = user.plan_type || 'free';
        const limit = limits[userPlan] || limits.free;
        if (categoryCount >= limit) {
            res.status(403).json({
                success: false,
                message: `Category limit reached. Your ${userPlan} plan allows up to ${limit} categories.`,
                data: {
                    currentCount: categoryCount,
                    limit: limit,
                    planType: userPlan
                }
            });
            return;
        }
        next();
    }
    catch (error) {
        console.error('Category limit check error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error while checking category limits'
        });
    }
};
exports.checkCategoryLimit = checkCategoryLimit;
const checkExpenseLimit = async (req, res, next) => {
    try {
        const { user } = req;
        if (!user) {
            res.status(401).json({
                success: false,
                message: 'Authentication required'
            });
            return;
        }
        const startOfMonth = new Date();
        startOfMonth.setDate(1);
        startOfMonth.setHours(0, 0, 0, 0);
        const endOfMonth = new Date(startOfMonth);
        endOfMonth.setMonth(endOfMonth.getMonth() + 1);
        endOfMonth.setDate(0);
        endOfMonth.setHours(23, 59, 59, 999);
        const expenseCount = await prisma_1.prisma.expense.count({
            where: {
                user_id: user.id,
                date: {
                    gte: startOfMonth,
                    lte: endOfMonth
                },
                deleted_at: null
            }
        });
        const limits = {
            free: 50,
            basic: 200,
            premium: 1000,
            enterprise: 10000
        };
        const userPlan = user.plan_type || 'free';
        const limit = limits[userPlan] || limits.free;
        if (expenseCount >= limit) {
            res.status(403).json({
                success: false,
                message: `Monthly expense limit reached. Your ${userPlan} plan allows up to ${limit} expenses per month.`,
                data: {
                    currentCount: expenseCount,
                    limit: limit,
                    planType: userPlan,
                    period: 'monthly'
                }
            });
            return;
        }
        next();
    }
    catch (error) {
        console.error('Expense limit check error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error while checking expense limits'
        });
    }
};
exports.checkExpenseLimit = checkExpenseLimit;
const checkExportLimit = async (req, res, next) => {
    try {
        const { user } = req;
        if (!user) {
            res.status(401).json({
                success: false,
                message: 'Authentication required'
            });
            return;
        }
        const userPlan = user.plan_type || 'free';
        if (userPlan === 'free') {
            const startOfMonth = new Date();
            startOfMonth.setDate(1);
            startOfMonth.setHours(0, 0, 0, 0);
            const endOfMonth = new Date(startOfMonth);
            endOfMonth.setMonth(endOfMonth.getMonth() + 1);
            endOfMonth.setDate(0);
            endOfMonth.setHours(23, 59, 59, 999);
            const exportLimit = 3;
        }
        next();
    }
    catch (error) {
        console.error('Export limit check error:', error);
        res.status(500).json({
            success: false,
            message: 'Internal server error while checking export limits'
        });
    }
};
exports.checkExportLimit = checkExportLimit;
const checkExportPermission = (format) => {
    return async (req, res, next) => {
        try {
            const { user } = req;
            if (!user) {
                res.status(401).json({
                    success: false,
                    message: 'Authentication required'
                });
                return;
            }
            const userPlan = user.plan_type || 'free';
            const permissions = {
                free: [],
                basic: ['csv'],
                premium: ['csv', 'pdf', 'excel'],
                enterprise: ['csv', 'pdf', 'excel']
            };
            const allowedFormats = permissions[userPlan] || [];
            if (!allowedFormats.includes(format)) {
                res.status(403).json({
                    success: false,
                    message: `${format.toUpperCase()} export is not available for your ${userPlan} plan. Please upgrade to access this feature.`,
                    data: {
                        currentPlan: userPlan,
                        requiredPlan: format === 'csv' ? 'basic' : 'premium',
                        availableFormats: allowedFormats
                    }
                });
                return;
            }
            next();
        }
        catch (error) {
            console.error('Export permission check error:', error);
            res.status(500).json({
                success: false,
                message: 'Internal server error while checking export permissions'
            });
        }
    };
};
exports.checkExportPermission = checkExportPermission;
//# sourceMappingURL=subscriptionLimits.js.map