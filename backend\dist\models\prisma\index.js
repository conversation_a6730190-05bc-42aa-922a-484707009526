"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Expense = exports.Category = exports.User = exports.prisma = void 0;
const prisma_1 = require("../../config/prisma");
Object.defineProperty(exports, "prisma", { enumerable: true, get: function () { return prisma_1.prisma; } });
exports.User = prisma_1.prisma.user;
exports.Category = prisma_1.prisma.category;
exports.Expense = prisma_1.prisma.expense;
//# sourceMappingURL=index.js.map