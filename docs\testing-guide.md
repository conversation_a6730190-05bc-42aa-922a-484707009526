# GHID TESTARE MVP

## 👥 UTILIZATORI DE TEST

### Conturi de test disponibile
Pentru testarea aplicației, sunt disponibile următoarele conturi pre-configurate:

#### 🆓 Utilizator Gratuit
- **Email**: `<EMAIL>`
- **Parola**: `Test123!`
- **Plan**: Free
- **Limit<PERSON>ri**: 50 cheltuieli/lună
- **Funcționalități**: CRUD cheltuieli de bază, 5 categorii personalizate

#### 💰 Utilizator Basic
- **Email**: `<EMAIL>`
- **Parola**: `Test123!`
- **Plan**: Basic ($5/lună)
- **Limitări**: 500 cheltuieli/lună
- **Funcționalități**: Export CSV, categorii nelimitate, rapoarte de bază

#### 🌟 Utilizator Premium
- **Email**: `<EMAIL>`
- **Parola**: `Test123!`
- **Plan**: Premium ($15/lună)
- **Limitări**: Cheltuieli nelimitate
- **Funcționalități**: Toate funcționalitățile, rapoarte avansate, backup automat

#### 🎯 Utilizator Demo
- **Email**: `<EMAIL>`
- **Parola**: `password123`
- **Plan**: Free (cu date sample)
- **Scop**: Demonstrație funcționalități pentru vizitatori

#### 🔧 Administrator
- **Email**: `<EMAIL>`
- **Parola**: `admin123`
- **Rol**: Administrator sistem
- **Acces**: Dashboard admin, gestionarea utilizatorilor, statistici globale

### Generarea utilizatorilor de test
Pentru a crea utilizatorii de test în baza de date:

```bash
# Din directorul backend
cd backend
node scripts/createTestUsers.js
```

### Date de test incluse
Fiecare utilizator de test include:
- **6 categorii default**: Mâncare, Transport, Utilități, Divertisment, Sănătate, Cumpărături
- **4 cheltuieli sample**: Pentru demonstrarea funcționalității
- **Abonament configurat**: Pentru utilizatorii Basic și Premium
- **Metadata de test**: Pentru identificarea în sistem

---

## 🎯 OBIECTIVE TESTARE

### Scopul testării pentru MVP
- **Validare funcționalitate de bază** - Asigură că features-urile core funcționează
- **Previne regresii** - Detectează bug-uri introduse în dezvoltare
- **Documentare comportament** - Testele servesc ca documentație vie
- **Confidence în deploy** - Permite lansări rapide și sigure

### Metrici țintă pentru MVP
- **Code Coverage**: Minim 70% (țintă 80%)
- **Test Success Rate**: 100% pentru CI/CD
- **Test Execution Time**: Sub 30 secunde pentru unit tests
- **Integration Tests**: Sub 2 minute

---

## 🏗️ STRATEGIA DE TESTARE

### Piramida testelor pentru MVP
```
        E2E Tests (5%)
       ┌─────────────────┐
      │  User Journeys   │
     └─────────────────────┘
    
    Integration Tests (25%)
   ┌─────────────────────────┐
  │    API + Database        │
 └─────────────────────────────┘

      Unit Tests (70%)
 ┌─────────────────────────────────┐
│  Functions, Components, Services │
└─────────────────────────────────────┘
```

### Faze de testare
1. **Development** - Unit tests în timpul codării
2. **Integration** - API tests după implementare endpoint
3. **System** - E2E tests pentru user flows
4. **Acceptance** - Manual testing pentru UX

---

## 💳 TESTARE STRIPE ȘI MONETIZARE

### Setup pentru testarea Stripe

#### Environment de test
```javascript
// backend/tests/setup.js - Adaugă configurare Stripe
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY_TEST);

// Mock Stripe pentru unit tests
jest.mock('stripe', () => {
  return jest.fn(() => ({
    customers: {
      create: jest.fn(),
      retrieve: jest.fn(),
      update: jest.fn()
    },
    subscriptions: {
      create: jest.fn(),
      retrieve: jest.fn(),
      update: jest.fn(),
      cancel: jest.fn()
    },
    webhooks: {
      constructEvent: jest.fn()
    }
  }));
});
```

#### Teste pentru Subscription Service
```javascript
// backend/tests/services/subscriptionService.test.js
const subscriptionService = require('../../src/services/subscriptionService');
const stripe = require('stripe')();

describe('SubscriptionService', () => {
  describe('createSubscription', () => {
    it('should create Stripe customer and subscription', async () => {
      const mockCustomer = { id: 'cus_test123' };
      const mockSubscription = { 
        id: 'sub_test123', 
        status: 'active',
        current_period_end: 1234567890
      };
      
      stripe.customers.create.mockResolvedValue(mockCustomer);
      stripe.subscriptions.create.mockResolvedValue(mockSubscription);
      
      const result = await subscriptionService.createSubscription({
        userId: 1,
        priceId: 'price_basic',
        paymentMethodId: 'pm_test123'
      });
      
      expect(stripe.customers.create).toHaveBeenCalledWith({
        payment_method: 'pm_test123',
        invoice_settings: { default_payment_method: 'pm_test123' }
      });
      
      expect(result.subscriptionId).toBe('sub_test123');
    });
  });
});
```

#### Teste pentru Webhook Handler
```javascript
// backend/tests/controllers/webhookController.test.js
const request = require('supertest');
const app = require('../../src/app');
const stripe = require('stripe')();

describe('Stripe Webhooks', () => {
  it('should handle subscription created event', async () => {
    const mockEvent = {
      type: 'customer.subscription.created',
      data: {
        object: {
          id: 'sub_test123',
          customer: 'cus_test123',
          status: 'active',
          current_period_end: 1234567890
        }
      }
    };
    
    stripe.webhooks.constructEvent.mockReturnValue(mockEvent);
    
    const response = await request(app)
      .post('/api/webhooks/stripe')
      .set('stripe-signature', 'test_signature')
      .send(JSON.stringify(mockEvent))
      .expect(200);
      
    expect(response.body.received).toBe(true);
  });
});
```

### Testare limitări și utilizare
```javascript
// backend/tests/middleware/subscriptionCheck.test.js
const subscriptionCheck = require('../../src/middleware/subscriptionCheck');
const { User, UsageTracking } = require('../../src/models');

describe('Subscription Middleware', () => {
  it('should allow action within limits', async () => {
    const req = { user: { id: 1, subscription_plan: 'basic' } };
    const res = {};
    const next = jest.fn();
    
    // Mock usage tracking
    UsageTracking.findOne = jest.fn().mockResolvedValue({
      expenses_count: 5,  // Sub limita de 50
      month: '2024-01',
      year: 2024
    });
    
    await subscriptionCheck.checkExpenseLimit(req, res, next);
    
    expect(next).toHaveBeenCalled();
  });
  
  it('should block action when limit exceeded', async () => {
    const req = { user: { id: 1, subscription_plan: 'free' } };
    const res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };
    const next = jest.fn();
    
    UsageTracking.findOne = jest.fn().mockResolvedValue({
      expenses_count: 10,  // La limita de 10
      month: '2024-01',
      year: 2024
    });
    
    await subscriptionCheck.checkExpenseLimit(req, res, next);
    
    expect(res.status).toHaveBeenCalledWith(403);
    expect(res.json).toHaveBeenCalledWith({
      error: 'Monthly expense limit reached. Upgrade to continue.'
    });
  });
});
```

---

## 🧪 UNIT TESTING

### Backend Unit Tests (Jest)

#### Setup pentru backend testing
**`backend/tests/setup.js`:**
```javascript
const { sequelize } = require('../src/config/database');

// Setup test database
beforeAll(async () => {
  await sequelize.sync({ force: true });
});

// Cleanup after tests
afterAll(async () => {
  await sequelize.close();
});

// Reset database between tests
beforeEach(async () => {
  await sequelize.truncate({ cascade: true });
});
```

#### Exemple unit tests backend

**Test pentru User Service:**
```javascript
// backend/tests/services/userService.test.js
const userService = require('../../src/services/userService');
const { User } = require('../../src/models');

describe('UserService', () => {
  describe('createUser', () => {
    it('should create user with valid data', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User'
      };

      const user = await userService.createUser(userData);

      expect(user.email).toBe(userData.email);
      expect(user.name).toBe(userData.name);
      expect(user.password).not.toBe(userData.password); // Should be hashed
    });

    it('should throw error for duplicate email', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User'
      };

      await userService.createUser(userData);

      await expect(userService.createUser(userData))
        .rejects.toThrow('Email already exists');
    });

    it('should throw error for invalid email', async () => {
      const userData = {
        email: 'invalid-email',
        password: 'password123',
        name: 'Test User'
      };

      await expect(userService.createUser(userData))
        .rejects.toThrow('Invalid email format');
    });
  });

  describe('authenticateUser', () => {
    it('should return user for valid credentials', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User'
      };

      await userService.createUser(userData);
      const user = await userService.authenticateUser(
        userData.email, 
        userData.password
      );

      expect(user.email).toBe(userData.email);
    });

    it('should return null for invalid password', async () => {
      const userData = {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User'
      };

      await userService.createUser(userData);
      const user = await userService.authenticateUser(
        userData.email, 
        'wrongpassword'
      );

      expect(user).toBeNull();
    });
  });
});
```

**Test pentru Expense Controller:**
```javascript
// backend/tests/controllers/expenseController.test.js
const request = require('supertest');
const app = require('../../src/app');
const { User, Expense } = require('../../src/models');
const jwt = require('jsonwebtoken');

describe('ExpenseController', () => {
  let user, authToken;

  beforeEach(async () => {
    user = await User.create({
      email: '<EMAIL>',
      password: 'hashedpassword',
      name: 'Test User'
    });

    authToken = jwt.sign(
      { userId: user.id }, 
      process.env.JWT_SECRET
    );
  });

  describe('POST /api/expenses', () => {
    it('should create expense with valid data', async () => {
      const expenseData = {
        amount: 25.50,
        description: 'Coffee',
        categoryId: 1,
        date: '2024-01-15'
      };

      const response = await request(app)
        .post('/api/expenses')
        .set('Authorization', `Bearer ${authToken}`)
        .send(expenseData)
        .expect(201);

      expect(response.body.amount).toBe(expenseData.amount);
      expect(response.body.description).toBe(expenseData.description);
      expect(response.body.userId).toBe(user.id);
    });

    it('should return 400 for invalid amount', async () => {
      const expenseData = {
        amount: -10,
        description: 'Invalid expense',
        categoryId: 1
      };

      const response = await request(app)
        .post('/api/expenses')
        .set('Authorization', `Bearer ${authToken}`)
        .send(expenseData)
        .expect(400);

      expect(response.body.error).toContain('Amount must be positive');
    });

    it('should return 401 without auth token', async () => {
      const expenseData = {
        amount: 25.50,
        description: 'Coffee',
        categoryId: 1
      };

      await request(app)
        .post('/api/expenses')
        .send(expenseData)
        .expect(401);
    });
  });

  describe('GET /api/expenses', () => {
    beforeEach(async () => {
      await Expense.bulkCreate([
        {
          amount: 25.50,
          description: 'Coffee',
          categoryId: 1,
          userId: user.id,
          date: '2024-01-15'
        },
        {
          amount: 12.00,
          description: 'Lunch',
          categoryId: 2,
          userId: user.id,
          date: '2024-01-14'
        }
      ]);
    });

    it('should return user expenses', async () => {
      const response = await request(app)
        .get('/api/expenses')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.length).toBe(2);
      expect(response.body[0].userId).toBe(user.id);
    });

    it('should filter expenses by date range', async () => {
      const response = await request(app)
        .get('/api/expenses?startDate=2024-01-15&endDate=2024-01-15')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.length).toBe(1);
      expect(response.body[0].description).toBe('Coffee');
    });
  });
});
```

### Frontend Unit Tests (Vitest + React Testing Library)

#### Setup pentru frontend testing
**`frontend/src/tests/setup.js`:**
```javascript
import { expect, afterEach } from 'vitest';
import { cleanup } from '@testing-library/react';
import * as matchers from '@testing-library/jest-dom/matchers';

// Extend Vitest's expect with jest-dom matchers
expect.extend(matchers);

// Cleanup after each test
afterEach(() => {
  cleanup();
});

// Mock environment variables
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});
```

#### Exemple unit tests frontend

**Test pentru ExpenseForm Component:**
```javascript
// frontend/src/components/__tests__/ExpenseForm.test.jsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import ExpenseForm from '../ExpenseForm';

describe('ExpenseForm', () => {
  const mockOnSubmit = vi.fn();
  const mockCategories = [
    { id: 1, name: 'Food' },
    { id: 2, name: 'Transport' }
  ];

  beforeEach(() => {
    mockOnSubmit.mockClear();
  });

  it('renders form fields correctly', () => {
    render(
      <ExpenseForm 
        onSubmit={mockOnSubmit} 
        categories={mockCategories} 
      />
    );

    expect(screen.getByLabelText(/amount/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/description/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/category/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/date/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /add expense/i })).toBeInTheDocument();
  });

  it('submits form with valid data', async () => {
    render(
      <ExpenseForm 
        onSubmit={mockOnSubmit} 
        categories={mockCategories} 
      />
    );

    fireEvent.change(screen.getByLabelText(/amount/i), {
      target: { value: '25.50' }
    });
    fireEvent.change(screen.getByLabelText(/description/i), {
      target: { value: 'Coffee' }
    });
    fireEvent.change(screen.getByLabelText(/category/i), {
      target: { value: '1' }
    });
    fireEvent.change(screen.getByLabelText(/date/i), {
      target: { value: '2024-01-15' }
    });

    fireEvent.click(screen.getByRole('button', { name: /add expense/i }));

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        amount: 25.50,
        description: 'Coffee',
        categoryId: 1,
        date: '2024-01-15'
      });
    });
  });

  it('shows validation error for invalid amount', async () => {
    render(
      <ExpenseForm 
        onSubmit={mockOnSubmit} 
        categories={mockCategories} 
      />
    );

    fireEvent.change(screen.getByLabelText(/amount/i), {
      target: { value: '-10' }
    });
    fireEvent.click(screen.getByRole('button', { name: /add expense/i }));

    await waitFor(() => {
      expect(screen.getByText(/amount must be positive/i)).toBeInTheDocument();
    });

    expect(mockOnSubmit).not.toHaveBeenCalled();
  });

  it('resets form after successful submission', async () => {
    render(
      <ExpenseForm 
        onSubmit={mockOnSubmit} 
        categories={mockCategories} 
      />
    );

    const amountInput = screen.getByLabelText(/amount/i);
    const descriptionInput = screen.getByLabelText(/description/i);

    fireEvent.change(amountInput, { target: { value: '25.50' } });
    fireEvent.change(descriptionInput, { target: { value: 'Coffee' } });
    fireEvent.change(screen.getByLabelText(/category/i), { target: { value: '1' } });
    fireEvent.change(screen.getByLabelText(/date/i), { target: { value: '2024-01-15' } });

    fireEvent.click(screen.getByRole('button', { name: /add expense/i }));

    await waitFor(() => {
      expect(amountInput.value).toBe('');
      expect(descriptionInput.value).toBe('');
    });
  });
});
```

**Test pentru Custom Hook:**
```javascript
// frontend/src/hooks/__tests__/useExpenses.test.js
import { renderHook, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import useExpenses from '../useExpenses';
import * as expenseService from '../../services/expenseService';

// Mock the expense service
vi.mock('../../services/expenseService');

describe('useExpenses', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('loads expenses on mount', async () => {
    const mockExpenses = [
      { id: 1, amount: 25.50, description: 'Coffee' },
      { id: 2, amount: 12.00, description: 'Lunch' }
    ];

    expenseService.getExpenses.mockResolvedValue(mockExpenses);

    const { result } = renderHook(() => useExpenses());

    expect(result.current.loading).toBe(true);

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.expenses).toEqual(mockExpenses);
    expect(expenseService.getExpenses).toHaveBeenCalledTimes(1);
  });

  it('handles error when loading expenses fails', async () => {
    const errorMessage = 'Failed to load expenses';
    expenseService.getExpenses.mockRejectedValue(new Error(errorMessage));

    const { result } = renderHook(() => useExpenses());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    expect(result.current.error).toBe(errorMessage);
    expect(result.current.expenses).toEqual([]);
  });

  it('adds new expense', async () => {
    const initialExpenses = [
      { id: 1, amount: 25.50, description: 'Coffee' }
    ];
    const newExpense = { id: 2, amount: 12.00, description: 'Lunch' };

    expenseService.getExpenses.mockResolvedValue(initialExpenses);
    expenseService.createExpense.mockResolvedValue(newExpense);

    const { result } = renderHook(() => useExpenses());

    await waitFor(() => {
      expect(result.current.loading).toBe(false);
    });

    await result.current.addExpense({
      amount: 12.00,
      description: 'Lunch',
      categoryId: 1
    });

    expect(result.current.expenses).toHaveLength(2);
    expect(result.current.expenses[1]).toEqual(newExpense);
  });
});
```

---

## 🔗 INTEGRATION TESTING

### API Integration Tests

**Test pentru Authentication Flow:**
```javascript
// backend/tests/integration/auth.test.js
const request = require('supertest');
const app = require('../../src/app');
const { User } = require('../../src/models');

describe('Authentication Integration', () => {
  describe('User Registration and Login Flow', () => {
    const userData = {
      email: '<EMAIL>',
      password: 'password123',
      name: 'Test User'
    };

    it('should complete full registration and login flow', async () => {
      // 1. Register user
      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      expect(registerResponse.body.user.email).toBe(userData.email);
      expect(registerResponse.body.token).toBeDefined();

      // 2. Login with same credentials
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: userData.email,
          password: userData.password
        })
        .expect(200);

      expect(loginResponse.body.user.email).toBe(userData.email);
      expect(loginResponse.body.token).toBeDefined();

      // 3. Access protected route
      const profileResponse = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${loginResponse.body.token}`)
        .expect(200);

      expect(profileResponse.body.email).toBe(userData.email);
    });

    it('should prevent duplicate registration', async () => {
      // First registration
      await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      // Second registration with same email
      await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(400);
    });
  });
});
```

### Database Integration Tests

**Test pentru Expense CRUD Operations:**
```javascript
// backend/tests/integration/expense-crud.test.js
const { User, Expense, Category } = require('../../src/models');
const expenseService = require('../../src/services/expenseService');

describe('Expense CRUD Integration', () => {
  let user, category;

  beforeEach(async () => {
    user = await User.create({
      email: '<EMAIL>',
      password: 'hashedpassword',
      name: 'Test User'
    });

    category = await Category.create({
      name: 'Food',
      userId: user.id
    });
  });

  it('should create, read, update, and delete expense', async () => {
    // CREATE
    const expenseData = {
      amount: 25.50,
      description: 'Coffee',
      categoryId: category.id,
      userId: user.id,
      date: '2024-01-15'
    };

    const createdExpense = await expenseService.createExpense(expenseData);
    expect(createdExpense.amount).toBe(expenseData.amount);

    // READ
    const foundExpense = await expenseService.getExpenseById(
      createdExpense.id, 
      user.id
    );
    expect(foundExpense.description).toBe(expenseData.description);

    // UPDATE
    const updateData = {
      amount: 30.00,
      description: 'Expensive Coffee'
    };
    const updatedExpense = await expenseService.updateExpense(
      createdExpense.id,
      user.id,
      updateData
    );
    expect(updatedExpense.amount).toBe(updateData.amount);
    expect(updatedExpense.description).toBe(updateData.description);

    // DELETE
    await expenseService.deleteExpense(createdExpense.id, user.id);
    const deletedExpense = await expenseService.getExpenseById(
      createdExpense.id,
      user.id
    );
    expect(deletedExpense).toBeNull();
  });

  it('should calculate monthly totals correctly', async () => {
    // Create multiple expenses
    await Expense.bulkCreate([
      {
        amount: 25.50,
        description: 'Coffee',
        categoryId: category.id,
        userId: user.id,
        date: '2024-01-15'
      },
      {
        amount: 12.00,
        description: 'Lunch',
        categoryId: category.id,
        userId: user.id,
        date: '2024-01-16'
      },
      {
        amount: 8.75,
        description: 'Snack',
        categoryId: category.id,
        userId: user.id,
        date: '2024-02-01' // Different month
      }
    ]);

    const januaryTotal = await expenseService.getMonthlyTotal(
      user.id,
      2024,
      1
    );
    expect(januaryTotal).toBe(37.50);

    const februaryTotal = await expenseService.getMonthlyTotal(
      user.id,
      2024,
      2
    );
    expect(februaryTotal).toBe(8.75);
  });
});
```

---

## 🌐 E2E TESTING

### Setup Playwright pentru E2E

**`e2e/playwright.config.js`:**
```javascript
module.exports = {
  testDir: './tests',
  timeout: 30000,
  expect: {
    timeout: 5000
  },
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:5173',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure'
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] }
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] }
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] }
    }
  ],
  webServer: [
    {
      command: 'npm run dev',
      cwd: '../backend',
      port: 3001
    },
    {
      command: 'npm run dev',
      cwd: '../frontend',
      port: 5173
    }
  ]
};
```

### E2E Test Examples

**Test pentru User Journey:**
```javascript
// e2e/tests/expense-management.spec.js
const { test, expect } = require('@playwright/test');

test.describe('Expense Management User Journey', () => {
  test.beforeEach(async ({ page }) => {
    // Setup: Create test user and login
    await page.goto('/register');
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'password123');
    await page.fill('[data-testid="name"]', 'Test User');
    await page.click('[data-testid="register-button"]');
    
    // Should redirect to dashboard after registration
    await expect(page).toHaveURL('/dashboard');
  });

  test('should complete full expense management flow', async ({ page }) => {
    // 1. Add a new expense
    await page.click('[data-testid="add-expense-button"]');
    await page.fill('[data-testid="amount"]', '25.50');
    await page.fill('[data-testid="description"]', 'Coffee');
    await page.selectOption('[data-testid="category"]', 'Food');
    await page.fill('[data-testid="date"]', '2024-01-15');
    await page.click('[data-testid="save-expense"]');

    // 2. Verify expense appears in list
    await expect(page.locator('[data-testid="expense-item"]')).toContainText('Coffee');
    await expect(page.locator('[data-testid="expense-item"]')).toContainText('$25.50');

    // 3. Edit the expense
    await page.click('[data-testid="edit-expense"]');
    await page.fill('[data-testid="amount"]', '30.00');
    await page.fill('[data-testid="description"]', 'Expensive Coffee');
    await page.click('[data-testid="save-expense"]');

    // 4. Verify changes
    await expect(page.locator('[data-testid="expense-item"]')).toContainText('Expensive Coffee');
    await expect(page.locator('[data-testid="expense-item"]')).toContainText('$30.00');

    // 5. Check monthly total
    await expect(page.locator('[data-testid="monthly-total"]')).toContainText('$30.00');

    // 6. Delete the expense
    await page.click('[data-testid="delete-expense"]');
    await page.click('[data-testid="confirm-delete"]');

    // 7. Verify expense is removed
    await expect(page.locator('[data-testid="expense-item"]')).toHaveCount(0);
    await expect(page.locator('[data-testid="monthly-total"]')).toContainText('$0.00');
  });

  test('should handle form validation correctly', async ({ page }) => {
    await page.click('[data-testid="add-expense-button"]');
    
    // Try to submit empty form
    await page.click('[data-testid="save-expense"]');
    
    // Check validation messages
    await expect(page.locator('[data-testid="amount-error"]')).toContainText('Amount is required');
    await expect(page.locator('[data-testid="description-error"]')).toContainText('Description is required');
    
    // Try invalid amount
    await page.fill('[data-testid="amount"]', '-10');
    await page.click('[data-testid="save-expense"]');
    await expect(page.locator('[data-testid="amount-error"]')).toContainText('Amount must be positive');
  });

  test('should filter expenses by date range', async ({ page }) => {
    // Add multiple expenses with different dates
    const expenses = [
      { amount: '25.50', description: 'Coffee', date: '2024-01-15' },
      { amount: '12.00', description: 'Lunch', date: '2024-01-16' },
      { amount: '8.75', description: 'Snack', date: '2024-02-01' }
    ];

    for (const expense of expenses) {
      await page.click('[data-testid="add-expense-button"]');
      await page.fill('[data-testid="amount"]', expense.amount);
      await page.fill('[data-testid="description"]', expense.description);
      await page.selectOption('[data-testid="category"]', 'Food');
      await page.fill('[data-testid="date"]', expense.date);
      await page.click('[data-testid="save-expense"]');
    }

    // Filter by January 2024
    await page.fill('[data-testid="start-date"]', '2024-01-01');
    await page.fill('[data-testid="end-date"]', '2024-01-31');
    await page.click('[data-testid="apply-filter"]');

    // Should show only January expenses
    await expect(page.locator('[data-testid="expense-item"]')).toHaveCount(2);
    await expect(page.locator('[data-testid="monthly-total"]')).toContainText('$37.50');
  });
});
```

---

## 🚀 CI/CD TESTING

### GitHub Actions Workflow

**`.github/workflows/test.yml`:**
```yaml
name: Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  backend-tests:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: expense_tracker_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: backend/package-lock.json
    
    - name: Install backend dependencies
      working-directory: ./backend
      run: npm ci
    
    - name: Run backend tests
      working-directory: ./backend
      run: npm run test:coverage
      env:
        NODE_ENV: test
        DB_HOST: localhost
        DB_PORT: 5432
        DB_NAME: expense_tracker_test
        DB_USER: postgres
        DB_PASSWORD: postgres
        JWT_SECRET: test-secret
        STRIPE_SECRET_KEY: ${{ secrets.STRIPE_TEST_SECRET_KEY }}
        STRIPE_PUBLISHABLE_KEY: ${{ secrets.STRIPE_TEST_PUBLISHABLE_KEY }}
        STRIPE_WEBHOOK_SECRET: ${{ secrets.STRIPE_TEST_WEBHOOK_SECRET }}
    
    - name: Upload backend coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/coverage/lcov.info
        flags: backend

  frontend-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: Install frontend dependencies
      working-directory: ./frontend
      run: npm ci
    
    - name: Run frontend tests
      working-directory: ./frontend
      run: npm run test:coverage
    
    - name: Upload frontend coverage
      uses: codecov/codecov-action@v3
      with:
        file: ./frontend/coverage/lcov.info
        flags: frontend

  e2e-tests:
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
    
    - name: Install dependencies
      run: |
        cd backend && npm ci
        cd ../frontend && npm ci
        cd ../e2e && npm ci
    
    - name: Install Playwright
      working-directory: ./e2e
      run: npx playwright install
    
    - name: Run E2E tests
      working-directory: ./e2e
      run: npx playwright test
    
    - name: Upload E2E results
      uses: actions/upload-artifact@v3
      if: failure()
      with:
        name: playwright-report
        path: e2e/playwright-report/
```

---

### Configurare Secrets pentru Stripe în GitHub Actions

Pentru testarea Stripe în CI/CD, adaugă următoarele secrets în repository:

1. **În GitHub Repository Settings > Secrets and variables > Actions:**
   ```
   STRIPE_TEST_SECRET_KEY=sk_test_...
   STRIPE_TEST_PUBLISHABLE_KEY=pk_test_...
   STRIPE_TEST_WEBHOOK_SECRET=whsec_...
   ```

2. **Workflow pentru teste Stripe:**
   ```yaml
   # .github/workflows/stripe-tests.yml
   name: Stripe Integration Tests
   
   on:
     push:
       branches: [main, develop]
     pull_request:
       branches: [main]
   
   jobs:
     stripe-tests:
       runs-on: ubuntu-latest
       
       steps:
         - uses: actions/checkout@v3
         
         - name: Setup Node.js
           uses: actions/setup-node@v3
           with:
             node-version: '18'
             cache: 'npm'
             cache-dependency-path: backend/package-lock.json
         
         - name: Install Stripe CLI
           run: |
             wget -O stripe.tar.gz https://github.com/stripe/stripe-cli/releases/latest/download/stripe_1.19.4_linux_x86_64.tar.gz
             tar -xzf stripe.tar.gz
             sudo mv stripe /usr/local/bin/
         
         - name: Install dependencies
           working-directory: ./backend
           run: npm ci
         
         - name: Run Stripe integration tests
           working-directory: ./backend
           run: npm run test:stripe
           env:
             STRIPE_SECRET_KEY: ${{ secrets.STRIPE_TEST_SECRET_KEY }}
             STRIPE_PUBLISHABLE_KEY: ${{ secrets.STRIPE_TEST_PUBLISHABLE_KEY }}
             STRIPE_WEBHOOK_SECRET: ${{ secrets.STRIPE_TEST_WEBHOOK_SECRET }}
   ```

---

## 📊 COVERAGE & REPORTING

### Coverage Configuration

**Backend Coverage (Jest):**
```javascript
// backend/jest.config.js
module.exports = {
  collectCoverageFrom: [
    'src/**/*.js',
    '!src/config/**',
    '!src/migrations/**',
    '!src/seeders/**',
    '!src/app.js'
  ],
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70
    },
    './src/services/': {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  },
  coverageReporters: ['text', 'lcov', 'html']
};
```

**Frontend Coverage (Vitest):**
```javascript
// frontend/vitest.config.js
export default defineConfig({
  test: {
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html', 'lcov'],
      exclude: [
        'node_modules/',
        'src/tests/',
        '**/*.test.{js,jsx}',
        'src/main.jsx',
        'src/vite-env.d.ts'
      ],
      threshold: {
        global: {
          branches: 70,
          functions: 70,
          lines: 70,
          statements: 70
        }
      }
    }
  }
});
```

### Test Reports

**Package.json scripts pentru rapoarte:**
```json
{
  "scripts": {
    "test:unit": "jest --coverage",
    "test:integration": "jest --testPathPattern=integration",
    "test:e2e": "playwright test",
    "test:stripe": "jest --testPathPattern=stripe --coverage",
    "test:stripe:unit": "jest tests/unit/services/subscriptionService.test.js",
    "test:stripe:integration": "jest tests/integration/stripe",
    "test:stripe:webhooks": "jest tests/unit/webhooks/stripeWebhook.test.js",
    "test:all": "npm run test:unit && npm run test:integration && npm run test:stripe && npm run test:e2e",
    "test:watch": "jest --watch",
    "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand",
    "coverage:open": "open coverage/lcov-report/index.html",
    "coverage:stripe": "jest --testPathPattern=stripe --coverage --coverageDirectory=coverage/stripe"
  }
}
```

---

## 💳 TESTARE STRIPE ȘI MONETIZARE

### Setup Environment pentru Stripe Testing

**Configurare variabile de mediu pentru teste:**
```bash
# backend/.env.test
STRIPE_SECRET_KEY=sk_test_...
STRIPE_PUBLISHABLE_KEY=pk_test_...
STRIPE_WEBHOOK_SECRET=whsec_test_...
STRIPE_WEBHOOK_ENDPOINT_SECRET=whsec_test_...
```

**Mock Stripe pentru Unit Tests:**
```javascript
// backend/tests/mocks/stripe.js
const mockStripe = {
  customers: {
    create: jest.fn(),
    retrieve: jest.fn(),
    update: jest.fn(),
    del: jest.fn()
  },
  subscriptions: {
    create: jest.fn(),
    retrieve: jest.fn(),
    update: jest.fn(),
    cancel: jest.fn(),
    list: jest.fn()
  },
  invoices: {
    retrieve: jest.fn(),
    list: jest.fn(),
    pay: jest.fn()
  },
  webhooks: {
    constructEvent: jest.fn()
  },
  prices: {
    list: jest.fn(),
    retrieve: jest.fn()
  },
  products: {
    list: jest.fn(),
    retrieve: jest.fn()
  }
};

module.exports = mockStripe;
```

### Unit Tests pentru Subscription Service

**Test pentru SubscriptionService:**
```javascript
// backend/tests/unit/services/subscriptionService.test.js
const subscriptionService = require('../../../src/services/subscriptionService');
const mockStripe = require('../../mocks/stripe');
const { User, Subscription } = require('../../../src/models');

jest.mock('stripe', () => {
  return jest.fn(() => mockStripe);
});

describe('SubscriptionService', () => {
  let user;

  beforeEach(async () => {
    jest.clearAllMocks();
    user = await User.create({
      email: '<EMAIL>',
      password: 'hashedpassword',
      name: 'Test User'
    });
  });

  describe('createSubscription', () => {
    it('should create Stripe customer and subscription', async () => {
      const mockCustomer = {
        id: 'cus_test123',
        email: '<EMAIL>'
      };
      const mockSubscription = {
        id: 'sub_test123',
        customer: 'cus_test123',
        status: 'active',
        current_period_start: 1640995200,
        current_period_end: 1643673600,
        items: {
          data: [{
            price: {
              id: 'price_premium',
              nickname: 'Premium Plan'
            }
          }]
        }
      };

      mockStripe.customers.create.mockResolvedValue(mockCustomer);
      mockStripe.subscriptions.create.mockResolvedValue(mockSubscription);

      const result = await subscriptionService.createSubscription(
        user.id,
        'price_premium',
        'pm_test_card'
      );

      expect(mockStripe.customers.create).toHaveBeenCalledWith({
        email: user.email,
        name: user.name,
        metadata: { userId: user.id.toString() }
      });

      expect(mockStripe.subscriptions.create).toHaveBeenCalledWith({
        customer: mockCustomer.id,
        items: [{ price: 'price_premium' }],
        default_payment_method: 'pm_test_card',
        expand: ['latest_invoice.payment_intent']
      });

      expect(result.stripeSubscriptionId).toBe(mockSubscription.id);
      expect(result.status).toBe('active');
    });

    it('should handle Stripe errors gracefully', async () => {
      const stripeError = new Error('Your card was declined.');
      stripeError.type = 'StripeCardError';
      stripeError.code = 'card_declined';

      mockStripe.customers.create.mockRejectedValue(stripeError);

      await expect(
        subscriptionService.createSubscription(
          user.id,
          'price_premium',
          'pm_test_card'
        )
      ).rejects.toThrow('Your card was declined.');
    });
  });

  describe('cancelSubscription', () => {
    it('should cancel active subscription', async () => {
      const subscription = await Subscription.create({
        userId: user.id,
        stripeSubscriptionId: 'sub_test123',
        stripeCustomerId: 'cus_test123',
        status: 'active',
        planType: 'premium',
        currentPeriodStart: new Date(),
        currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
      });

      const mockCancelledSubscription = {
        id: 'sub_test123',
        status: 'canceled',
        canceled_at: Math.floor(Date.now() / 1000)
      };

      mockStripe.subscriptions.cancel.mockResolvedValue(mockCancelledSubscription);

      const result = await subscriptionService.cancelSubscription(user.id);

      expect(mockStripe.subscriptions.cancel).toHaveBeenCalledWith('sub_test123');
      expect(result.status).toBe('canceled');
    });
  });

  describe('updateSubscription', () => {
    it('should upgrade subscription plan', async () => {
      const subscription = await Subscription.create({
        userId: user.id,
        stripeSubscriptionId: 'sub_test123',
        stripeCustomerId: 'cus_test123',
        status: 'active',
        planType: 'basic',
        currentPeriodStart: new Date(),
        currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
      });

      const mockUpdatedSubscription = {
        id: 'sub_test123',
        status: 'active',
        items: {
          data: [{
            id: 'si_test123',
            price: {
              id: 'price_premium',
              nickname: 'Premium Plan'
            }
          }]
        }
      };

      mockStripe.subscriptions.retrieve.mockResolvedValue({
        id: 'sub_test123',
        items: {
          data: [{ id: 'si_test123' }]
        }
      });
      mockStripe.subscriptions.update.mockResolvedValue(mockUpdatedSubscription);

      const result = await subscriptionService.updateSubscription(
        user.id,
        'price_premium'
      );

      expect(mockStripe.subscriptions.update).toHaveBeenCalledWith(
        'sub_test123',
        {
          items: [{
            id: 'si_test123',
            price: 'price_premium'
          }],
          proration_behavior: 'always_invoice'
        }
      );

      expect(result.planType).toBe('premium');
    });
  });
});
```

### Tests pentru Webhook Handler

**Test pentru Stripe Webhooks:**
```javascript
// backend/tests/unit/controllers/webhookController.test.js
const request = require('supertest');
const app = require('../../../src/app');
const mockStripe = require('../../mocks/stripe');
const { User, Subscription } = require('../../../src/models');

jest.mock('stripe', () => {
  return jest.fn(() => mockStripe);
});

describe('Webhook Controller', () => {
  let user, subscription;

  beforeEach(async () => {
    jest.clearAllMocks();
    user = await User.create({
      email: '<EMAIL>',
      password: 'hashedpassword',
      name: 'Test User'
    });

    subscription = await Subscription.create({
      userId: user.id,
      stripeSubscriptionId: 'sub_test123',
      stripeCustomerId: 'cus_test123',
      status: 'active',
      planType: 'premium',
      currentPeriodStart: new Date(),
      currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
    });
  });

  describe('POST /api/webhooks/stripe', () => {
    it('should handle subscription.created event', async () => {
      const webhookEvent = {
        id: 'evt_test123',
        type: 'customer.subscription.created',
        data: {
          object: {
            id: 'sub_new123',
            customer: 'cus_test123',
            status: 'active',
            current_period_start: 1640995200,
            current_period_end: 1643673600,
            items: {
              data: [{
                price: {
                  id: 'price_premium',
                  nickname: 'Premium Plan'
                }
              }]
            }
          }
        }
      };

      mockStripe.webhooks.constructEvent.mockReturnValue(webhookEvent);

      const response = await request(app)
        .post('/api/webhooks/stripe')
        .set('stripe-signature', 'test_signature')
        .send(JSON.stringify(webhookEvent))
        .expect(200);

      expect(response.body.received).toBe(true);
      expect(mockStripe.webhooks.constructEvent).toHaveBeenCalled();
    });

    it('should handle subscription.updated event', async () => {
      const webhookEvent = {
        id: 'evt_test123',
        type: 'customer.subscription.updated',
        data: {
          object: {
            id: 'sub_test123',
            customer: 'cus_test123',
            status: 'past_due',
            current_period_start: 1640995200,
            current_period_end: 1643673600
          }
        }
      };

      mockStripe.webhooks.constructEvent.mockReturnValue(webhookEvent);

      await request(app)
        .post('/api/webhooks/stripe')
        .set('stripe-signature', 'test_signature')
        .send(JSON.stringify(webhookEvent))
        .expect(200);

      const updatedSubscription = await Subscription.findOne({
        where: { stripeSubscriptionId: 'sub_test123' }
      });
      expect(updatedSubscription.status).toBe('past_due');
    });

    it('should handle invoice.payment_failed event', async () => {
      const webhookEvent = {
        id: 'evt_test123',
        type: 'invoice.payment_failed',
        data: {
          object: {
            id: 'in_test123',
            customer: 'cus_test123',
            subscription: 'sub_test123',
            amount_due: 2000,
            attempt_count: 1
          }
        }
      };

      mockStripe.webhooks.constructEvent.mockReturnValue(webhookEvent);

      await request(app)
        .post('/api/webhooks/stripe')
        .set('stripe-signature', 'test_signature')
        .send(JSON.stringify(webhookEvent))
        .expect(200);

      // Verifică că s-a trimis notificare utilizatorului
      // (implementarea depinde de sistemul de notificări)
    });

    it('should handle invalid webhook signature', async () => {
      const error = new Error('Invalid signature');
      error.type = 'StripeSignatureVerificationError';
      mockStripe.webhooks.constructEvent.mockImplementation(() => {
        throw error;
      });

      await request(app)
        .post('/api/webhooks/stripe')
        .set('stripe-signature', 'invalid_signature')
        .send('{"test": "data"}')
        .expect(400);
    });
  });
});
```

### Tests pentru Usage Tracking și Limits

**Test pentru Usage Tracking:**
```javascript
// backend/tests/unit/services/usageTrackingService.test.js
const usageTrackingService = require('../../../src/services/usageTrackingService');
const { User, Subscription, UsageTracking } = require('../../../src/models');

describe('UsageTrackingService', () => {
  let user, subscription;

  beforeEach(async () => {
    user = await User.create({
      email: '<EMAIL>',
      password: 'hashedpassword',
      name: 'Test User'
    });

    subscription = await Subscription.create({
      userId: user.id,
      stripeSubscriptionId: 'sub_test123',
      stripeCustomerId: 'cus_test123',
      status: 'active',
      planType: 'basic', // 50 expenses limit
      currentPeriodStart: new Date(),
      currentPeriodEnd: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
    });
  });

  describe('trackExpenseCreation', () => {
    it('should track expense creation within limits', async () => {
      const result = await usageTrackingService.trackExpenseCreation(user.id);

      expect(result.allowed).toBe(true);
      expect(result.currentUsage).toBe(1);
      expect(result.limit).toBe(50);
      expect(result.remaining).toBe(49);
    });

    it('should prevent expense creation when limit exceeded', async () => {
      // Creează 50 de înregistrări de usage (limita pentru basic)
      for (let i = 0; i < 50; i++) {
        await UsageTracking.create({
          userId: user.id,
          feature: 'expenses',
          count: 1,
          period: new Date().toISOString().slice(0, 7) // YYYY-MM
        });
      }

      const result = await usageTrackingService.trackExpenseCreation(user.id);

      expect(result.allowed).toBe(false);
      expect(result.currentUsage).toBe(50);
      expect(result.limit).toBe(50);
      expect(result.remaining).toBe(0);
      expect(result.message).toContain('limit exceeded');
    });

    it('should allow unlimited expenses for premium users', async () => {
      await subscription.update({ planType: 'premium' });

      // Creează 100 de înregistrări de usage
      for (let i = 0; i < 100; i++) {
        await UsageTracking.create({
          userId: user.id,
          feature: 'expenses',
          count: 1,
          period: new Date().toISOString().slice(0, 7)
        });
      }

      const result = await usageTrackingService.trackExpenseCreation(user.id);

      expect(result.allowed).toBe(true);
      expect(result.currentUsage).toBe(101);
      expect(result.limit).toBe(-1); // unlimited
    });
  });

  describe('getUsageStats', () => {
    it('should return current usage statistics', async () => {
      // Creează câteva înregistrări de usage
      await UsageTracking.bulkCreate([
        {
          userId: user.id,
          feature: 'expenses',
          count: 1,
          period: new Date().toISOString().slice(0, 7)
        },
        {
          userId: user.id,
          feature: 'expenses',
          count: 1,
          period: new Date().toISOString().slice(0, 7)
        },
        {
          userId: user.id,
          feature: 'categories',
          count: 1,
          period: new Date().toISOString().slice(0, 7)
        }
      ]);

      const stats = await usageTrackingService.getUsageStats(user.id);

      expect(stats.expenses.current).toBe(2);
      expect(stats.expenses.limit).toBe(50);
      expect(stats.expenses.remaining).toBe(48);
      expect(stats.categories.current).toBe(1);
      expect(stats.categories.limit).toBe(10);
    });
  });

  describe('resetMonthlyUsage', () => {
    it('should reset usage for new billing period', async () => {
      // Creează usage pentru luna precedentă
      const lastMonth = new Date();
      lastMonth.setMonth(lastMonth.getMonth() - 1);
      
      await UsageTracking.create({
        userId: user.id,
        feature: 'expenses',
        count: 25,
        period: lastMonth.toISOString().slice(0, 7)
      });

      await usageTrackingService.resetMonthlyUsage(user.id);

      const currentStats = await usageTrackingService.getUsageStats(user.id);
      expect(currentStats.expenses.current).toBe(0);
    });
  });
});
```

### Integration Tests pentru Stripe

**Test pentru Subscription Flow:**
```javascript
// backend/tests/integration/stripe-subscription.test.js
const request = require('supertest');
const app = require('../../src/app');
const { User } = require('../../src/models');

// Folosește Stripe Test Mode pentru integration tests
describe('Stripe Subscription Integration', () => {
  let user, authToken;

  beforeEach(async () => {
    user = await User.create({
      email: '<EMAIL>',
      password: 'hashedpassword',
      name: 'Test User'
    });

    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({
        email: '<EMAIL>',
        password: 'password123'
      });
    
    authToken = loginResponse.body.token;
  });

  describe('Subscription Creation Flow', () => {
    it('should create subscription with test card', async () => {
      const subscriptionData = {
        priceId: 'price_test_premium',
        paymentMethodId: 'pm_card_visa' // Stripe test card
      };

      const response = await request(app)
        .post('/api/subscriptions')
        .set('Authorization', `Bearer ${authToken}`)
        .send(subscriptionData)
        .expect(201);

      expect(response.body.subscription.status).toBe('active');
      expect(response.body.subscription.planType).toBe('premium');
      expect(response.body.subscription.stripeSubscriptionId).toBeDefined();
    });

    it('should handle declined card', async () => {
      const subscriptionData = {
        priceId: 'price_test_premium',
        paymentMethodId: 'pm_card_chargeDeclined' // Stripe test declined card
      };

      const response = await request(app)
        .post('/api/subscriptions')
        .set('Authorization', `Bearer ${authToken}`)
        .send(subscriptionData)
        .expect(400);

      expect(response.body.error).toContain('declined');
    });
  });

  describe('Subscription Management', () => {
    let subscription;

    beforeEach(async () => {
      // Creează o subscripție de test
      const subscriptionResponse = await request(app)
        .post('/api/subscriptions')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          priceId: 'price_test_basic',
          paymentMethodId: 'pm_card_visa'
        });
      
      subscription = subscriptionResponse.body.subscription;
    });

    it('should upgrade subscription', async () => {
      const response = await request(app)
        .put(`/api/subscriptions/${subscription.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({ priceId: 'price_test_premium' })
        .expect(200);

      expect(response.body.subscription.planType).toBe('premium');
    });

    it('should cancel subscription', async () => {
      const response = await request(app)
        .delete(`/api/subscriptions/${subscription.id}`)
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body.subscription.status).toBe('canceled');
    });
  });
});
```

### Frontend Tests pentru Stripe Components

**Test pentru SubscriptionForm:**
```javascript
// frontend/src/components/__tests__/SubscriptionForm.test.jsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import { Elements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import SubscriptionForm from '../SubscriptionForm';

// Mock Stripe
const mockStripe = {
  elements: vi.fn(() => ({
    create: vi.fn(() => ({
      mount: vi.fn(),
      unmount: vi.fn(),
      on: vi.fn(),
      off: vi.fn()
    })),
    getElement: vi.fn()
  })),
  createPaymentMethod: vi.fn(),
  confirmCardPayment: vi.fn()
};

vi.mock('@stripe/stripe-js', () => ({
  loadStripe: vi.fn(() => Promise.resolve(mockStripe))
}));

describe('SubscriptionForm', () => {
  const mockOnSuccess = vi.fn();
  const mockPlans = [
    { id: 'price_basic', name: 'Basic', price: 9.99 },
    { id: 'price_premium', name: 'Premium', price: 19.99 }
  ];

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders subscription plans', async () => {
    render(
      <Elements stripe={mockStripe}>
        <SubscriptionForm 
          plans={mockPlans}
          onSuccess={mockOnSuccess}
        />
      </Elements>
    );

    expect(screen.getByText('Basic')).toBeInTheDocument();
    expect(screen.getByText('Premium')).toBeInTheDocument();
    expect(screen.getByText('$9.99')).toBeInTheDocument();
    expect(screen.getByText('$19.99')).toBeInTheDocument();
  });

  it('handles plan selection', async () => {
    render(
      <Elements stripe={mockStripe}>
        <SubscriptionForm 
          plans={mockPlans}
          onSuccess={mockOnSuccess}
        />
      </Elements>
    );

    const premiumPlan = screen.getByTestId('plan-price_premium');
    fireEvent.click(premiumPlan);

    expect(premiumPlan).toHaveClass('selected');
    expect(screen.getByText('Subscribe to Premium')).toBeInTheDocument();
  });

  it('shows payment form when plan selected', async () => {
    render(
      <Elements stripe={mockStripe}>
        <SubscriptionForm 
          plans={mockPlans}
          onSuccess={mockOnSuccess}
        />
      </Elements>
    );

    fireEvent.click(screen.getByTestId('plan-price_basic'));

    await waitFor(() => {
      expect(screen.getByTestId('card-element')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /subscribe/i })).toBeInTheDocument();
    });
  });
});
```

### E2E Tests pentru Stripe Integration

**Test pentru Subscription Flow:**
```javascript
// e2e/tests/subscription-flow.spec.js
const { test, expect } = require('@playwright/test');

test.describe('Subscription Management', () => {
  test.beforeEach(async ({ page }) => {
    // Login cu user de test
    await page.goto('/login');
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'password123');
    await page.click('[data-testid="login-button"]');
    await expect(page).toHaveURL('/dashboard');
  });

  test('should complete subscription purchase flow', async ({ page }) => {
    // Navighează la pagina de pricing
    await page.click('[data-testid="upgrade-button"]');
    await expect(page).toHaveURL('/pricing');

    // Selectează planul Premium
    await page.click('[data-testid="select-premium-plan"]');
    await expect(page).toHaveURL('/checkout');

    // Completează informațiile de plată cu card de test Stripe
    await page.fill('[data-testid="card-number"]', '****************');
    await page.fill('[data-testid="card-expiry"]', '12/25');
    await page.fill('[data-testid="card-cvc"]', '123');
    await page.fill('[data-testid="card-name"]', 'Test User');

    // Confirmă plata
    await page.click('[data-testid="confirm-payment"]');

    // Verifică succesul
    await expect(page.locator('[data-testid="success-message"]')).toContainText('Subscription activated');
    await expect(page).toHaveURL('/dashboard');

    // Verifică că planul este activ
    await expect(page.locator('[data-testid="current-plan"]')).toContainText('Premium');
    await expect(page.locator('[data-testid="plan-status"]')).toContainText('Active');
  });

  test('should handle payment failure gracefully', async ({ page }) => {
    await page.click('[data-testid="upgrade-button"]');
    await page.click('[data-testid="select-premium-plan"]');

    // Folosește card de test care va fi declined
    await page.fill('[data-testid="card-number"]', '****************');
    await page.fill('[data-testid="card-expiry"]', '12/25');
    await page.fill('[data-testid="card-cvc"]', '123');
    await page.fill('[data-testid="card-name"]', 'Test User');

    await page.click('[data-testid="confirm-payment"]');

    // Verifică mesajul de eroare
    await expect(page.locator('[data-testid="error-message"]')).toContainText('Your card was declined');
    
    // Verifică că utilizatorul rămâne pe pagina de checkout
    await expect(page).toHaveURL('/checkout');
  });

  test('should allow subscription cancellation', async ({ page }) => {
    // Presupune că utilizatorul are deja o subscripție activă
    await page.goto('/account/subscription');
    
    await page.click('[data-testid="cancel-subscription"]');
    await page.click('[data-testid="confirm-cancellation"]');

    await expect(page.locator('[data-testid="cancellation-success"]')).toContainText('Subscription cancelled');
    await expect(page.locator('[data-testid="plan-status"]')).toContainText('Cancelled');
  });
});
```

### Performance Tests pentru Stripe

**Test pentru Webhook Performance:**
```javascript
// backend/tests/performance/webhook-performance.test.js
const request = require('supertest');
const app = require('../../src/app');

describe('Webhook Performance', () => {
  it('should process webhook within acceptable time', async () => {
    const webhookPayload = {
      id: 'evt_test123',
      type: 'customer.subscription.updated',
      data: {
        object: {
          id: 'sub_test123',
          customer: 'cus_test123',
          status: 'active'
        }
      }
    };

    const startTime = Date.now();
    
    await request(app)
      .post('/api/webhooks/stripe')
      .set('stripe-signature', 'test_signature')
      .send(JSON.stringify(webhookPayload))
      .expect(200);
    
    const endTime = Date.now();
    const processingTime = endTime - startTime;
    
    // Webhook-ul trebuie procesat în mai puțin de 500ms
    expect(processingTime).toBeLessThan(500);
  });

  it('should handle concurrent webhooks', async () => {
    const webhookPromises = [];
    
    for (let i = 0; i < 10; i++) {
      const webhookPayload = {
        id: `evt_test${i}`,
        type: 'invoice.payment_succeeded',
        data: {
          object: {
            id: `in_test${i}`,
            customer: `cus_test${i}`,
            subscription: `sub_test${i}`
          }
        }
      };
      
      webhookPromises.push(
        request(app)
          .post('/api/webhooks/stripe')
          .set('stripe-signature', 'test_signature')
          .send(JSON.stringify(webhookPayload))
      );
    }
    
    const startTime = Date.now();
    const responses = await Promise.all(webhookPromises);
    const endTime = Date.now();
    
    // Toate webhook-urile trebuie procesate cu succes
    responses.forEach(response => {
      expect(response.status).toBe(200);
    });
    
    // Procesarea concurentă nu trebuie să dureze mai mult de 2 secunde
    expect(endTime - startTime).toBeLessThan(2000);
  });
});
```

---

## 🔧 DEBUGGING TESTS

### VS Code Debug Configuration

**`.vscode/launch.json`:**
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Jest Tests",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/backend/node_modules/.bin/jest",
      "args": ["--runInBand", "--no-cache"],
      "cwd": "${workspaceFolder}/backend",
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen"
    },
    {
      "name": "Debug Vitest Tests",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/frontend/node_modules/.bin/vitest",
      "args": ["--run"],
      "cwd": "${workspaceFolder}/frontend",
      "console": "integratedTerminal"
    }
  ]
}
```

### Test Debugging Tips

```javascript
// Pentru debugging în Jest
describe.only('Specific test suite', () => {
  it.only('specific test', () => {
    // Doar acest test va rula
  });
});

// Pentru debugging cu console.log
it('should debug this test', () => {
  const result = someFunction();
  console.log('Debug result:', result);
  expect(result).toBe(expected);
});

// Pentru debugging async operations
it('should debug async operation', async () => {
  console.log('Before async call');
  const result = await asyncFunction();
  console.log('After async call:', result);
  expect(result).toBeDefined();
});
```

---

## ✅ CHECKLIST TESTARE MVP

### Pre-Development
- [ ] Test environment configurat
- [ ] Database de test setup
- [ ] CI/CD pipeline configurat
- [ ] Coverage thresholds definite
- [ ] Stripe test environment configurat
- [ ] Stripe CLI instalat și configurat
- [ ] Webhook endpoints de test setup
- [ ] Test cards și scenarii Stripe definite

### During Development
- [ ] Unit tests pentru fiecare funcție nouă
- [ ] Integration tests pentru API endpoints
- [ ] Component tests pentru UI
- [ ] Tests rulează în CI/CD
- [ ] Unit tests pentru Stripe services
- [ ] Integration tests pentru Stripe webhooks
- [ ] Tests pentru usage tracking și limits
- [ ] Mock-uri pentru Stripe API în unit tests

### Pre-Launch
- [ ] E2E tests pentru user journeys
- [ ] Performance tests
- [ ] Security tests
- [ ] Cross-browser testing
- [ ] Mobile responsiveness tests
- [ ] E2E tests pentru subscription flow
- [ ] Tests pentru payment failure scenarios
- [ ] Webhook signature validation tests
- [ ] Performance tests pentru webhook processing
- [ ] Tests pentru subscription cancellation
- [ ] Tests pentru plan upgrades/downgrades

### Post-Launch
- [ ] Monitoring și alerting setup
- [ ] Error tracking configurat
- [ ] User feedback collection
- [ ] A/B testing framework
- [ ] Stripe webhook monitoring
- [ ] Payment failure alerting
- [ ] Subscription metrics tracking
- [ ] Revenue analytics setup

---

## 🎯 NEXT STEPS

1. **Setup test environment** conform ghidului
2. **Configurează Stripe test environment:**
   - Creează cont Stripe test
   - Instalează Stripe CLI
   - Configurează webhook endpoints
3. **Scrie primul unit test** pentru user registration
4. **Implementează Stripe unit tests:**
   - SubscriptionService tests
   - Webhook handler tests
   - Usage tracking tests
5. **Configurează CI/CD** pentru automated testing
6. **Adaugă Stripe secrets** în GitHub Actions
7. **Implementează coverage reporting**
8. **Adaugă E2E tests** pentru critical user flows
9. **Testează Stripe integration** end-to-end
10. **Setup monitoring** pentru Stripe webhooks și payments

---

*Pentru întrebări despre testare, consultă documentația sau contactează echipa de development.*